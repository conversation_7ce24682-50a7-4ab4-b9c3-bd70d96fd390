import axios from 'axios';

// Get API URL from environment variables
const getApiUrl = () => process.env.REACT_APP_API_URL || 'http://localhost:5001/api';

// Create axios instance
const createApi = () => {
  return axios.create({
    baseURL: getApiUrl(),
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

// Get API instance
const api = createApi();

// Handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response || error);
    return Promise.reject(error);
  }
);

// Container API
export const fetchContainers = async () => {
  const response = await api.get('/containers');
  return response.data;
};

export const fetchContainerById = async (containerId) => {
  const response = await api.get(`/containers/${containerId}`);
  return response.data;
};

export const fetchContainerSensors = async (containerId) => {
  const response = await api.get(`/containers/${containerId}/sensors`);
  return response.data;
};

// Leshan API
export const fetchLeshanClients = async () => {
  const response = await api.get('/leshan/clients');
  return response.data;
};

export const fetchClientDetails = async (endpoint) => {
  const response = await api.get(`/leshan/clients/${endpoint}`);
  return response.data;
};

export const observeResource = async (endpoint, objectId, instanceId, resourceId) => {
  const response = await api.post(`/leshan/observe/${endpoint}/${objectId}/${instanceId}/${resourceId}`);
  return response.data;
};

export const cancelObservation = async (endpoint, objectId, instanceId, resourceId) => {
  const response = await api.delete(`/leshan/observe/${endpoint}/${objectId}/${instanceId}/${resourceId}`);
  return response.data;
};

// Remove or comment out InfluxDB API direct calls
// export const queryInfluxDB = async (params) => {
//   const response = await api.get('/influxdb/query', { params });
//   return response.data;
// };
//
// export const getSensorHistory = async (sensorId) => {
//   const response = await api.get(`/influxdb/sensors/${sensorId}/history`);
//   return response.data;
// };

export default api;