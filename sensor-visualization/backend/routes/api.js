// routes/api.js - API routes for the sensor visualization backend
const express = require("express");
const router = express.Router();

// Import controllers
const leshanController = require("../controllers/leshanController");
const containerController = require("../controllers/containerController");

// Container routes
router.get("/containers", containerController.getAllContainers);
router.get("/containers/:containerId", containerController.getContainerById);
router.get("/containers/:containerId/sensors", containerController.getContainerSensors);

// Leshan routes
router.get("/leshan/clients", leshanController.getAllClients);
router.get("/leshan/clients/:endpoint", leshanController.getClientDetails);
router.post("/leshan/observe/:endpoint/:objectId/:instanceId/:resourceId", leshanController.observeResource);
router.delete("/leshan/observe/:endpoint/:objectId/:instanceId/:resourceId", leshanController.cancelObservation);

// Webhook for Leshan notifications
router.post("/notifications", (req, res) => {
  const notification = req.body;
  global.broadcastSensorUpdate(notification);
  res.status(200).end();
});

module.exports = router;
