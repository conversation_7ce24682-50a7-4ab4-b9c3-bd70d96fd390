# Digital Twin Project

A comprehensive platform for creating and managing digital twins of IoT devices with LwM2M and MQTT integration.

## Features

- Create virtual sensors and actuators
- Configure MQTT broker settings
- Dynamically generate LwM2M objects
- Deploy containers with Node-RED flows

## Project Structure

- `backend/`: Node.js backend server
- `frontend/`: React frontend application
- `node-red-custom/`: Custom Node-RED Docker image

## Setup

### Prerequisites

- Node.js
- Docker
- MQTT Broker (e.g., HiveMQ)
- LwM2M Server (e.g., Leshan)

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/ShaheemNaqvi/Digital-Twin-Project.git
   cd Digital-Twin-Project
   ```
2. Install dependencies:
   ```
   cd backend && npm install
   cd frontend && npm install
   ```
3. Build the custom Node-RED image:
   ```
   cd node-red-custom
   docker build -t custom-nodered-image -f Dockerfile .
   ```
4. Start the backend:
   ```
   cd backend && npm start
   ```
5. Start the frontend:
   ```
   cd frontend && npm start
   ```

## Usage

1. Navigate to the frontend application
2. Create a new virtual object (sensor or actuator)
3. Configure the MQTT broker settings
4. Add resources to the virtual object
5. Deploy the virtual object

## License

[MIT](LICENSE)
