Task:
Integrate a “Web Search” feature into the existing MCP + LLM backend and frontend system that uses Brave MCP’s web search tool. This includes backend, LLM, and frontend changes.

Requirements:

Backend MCP Client:

Add support to call the Brave MCP "web-search" tool by name, passing a search query string and returning the results.

Ensure that the MCP client dynamically lists and recognizes the "web-search" tool.

Backend Query Processing:

Modify the query processing flow to accept a new boolean flag useWebSearch indicating whether web search should be used.

When enabled, call the "web-search" tool with the user query or a derived query.

Integrate the web search results as context or supplement to the LLM prompt or response generation.

API Controller:

Update the /query POST endpoint to accept a useWebSearch flag from the client request body.

Pass this flag to the MCP client or query service layer to trigger web search tool usage.

Frontend UI:

Add a toggle (checkbox or button) labeled “Enable Web Search” near the query input.

When toggled ON, send useWebSearch: true in the request body when submitting the query.

Update the UI to show loading states and display enriched answers incorporating web search results.

Allow toggling ON/OFF with clear visual feedback.

LLM Prompt Integration:

Adjust LLM prompt templates to optionally incorporate web search results when useWebSearch is true, helping the model answer queries with live web info.

Ensure prompt templates remain clear and concise.

Error Handling and UX:

Display appropriate messages if web search tool calls fail or time out.

Keep the system responsive and user-friendly.

Testing:

Add automated tests or manual test instructions to verify web search integration on backend and frontend.

Deliverables:

Updated MCP client code supporting web-search tool calls

Modified backend query processing and API controller supporting useWebSearch flag

Frontend React (or your framework) component updates adding the toggle button and handling the flag in API calls

Updated LLM prompt templates with optional web search context

Sample usage and testing instructions

Additional Notes:
Use existing code patterns and modules for consistency. Follow good coding practices: modular design, clear separation of concerns, and proper error handling.\\\\\\