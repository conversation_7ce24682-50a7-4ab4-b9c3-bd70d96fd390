{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.8", "@mui/material": "^6.4.8", "@mui/styled-engine": "^6.4.8", "@mui/x-data-grid": "^7.22.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.6.7", "body-parser": "^1.20.3", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "date-fns": "^4.1.0", "express": "^4.21.2", "framer-motion": "^11.15.0", "lodash": "^4.17.21", "lucide-react": "^0.482.0", "markdown-to-jsx": "^7.5.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "react": "^19.0.0", "react-app-rewired": "^2.2.1", "react-dom": "^19.0.0", "react-hotkeys-hook": "^4.6.1", "react-router-dom": "^7.3.0", "react-scripts": "5.0.1", "react-syntax-highlighter": "^15.5.0", "recharts": "^2.12.2", "stream-browserify": "^3.0.0", "util": "^0.12.5", "uuid": "^11.0.3", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-app-rewired start", "start-mcp": "BROWSER=none REACT_APP_INFLUXDB_URL=http://localhost:8086 react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"null-loader": "^4.0.1"}}