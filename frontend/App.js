// App.js
import React from 'react';
import { useState } from 'react';
import { Routes, Route, useNavigate } from 'react-router-dom';
import Login from './pages/Login';
import ChatAgentPage from './mcp-client/pages/ChatAgentPage';
import './App.css';

import { FaCode, FaChartLine, FaFileAlt, FaLaptopCode, FaTools, FaRocket } from 'react-icons/fa';
import { BiNetworkChart, BiCog, BiData, BiCloudUpload } from 'react-icons/bi';
import { IoAnalyticsSharp, IoSpeedometerSharp } from 'react-icons/io5';
import { HiOutlineLightBulb } from 'react-icons/hi';
import { BsGraphUp, BsThreeDots } from 'react-icons/bs';

function App() {
  const [hoveredOption, setHoveredOption] = useState(null);
  const navigate = useNavigate();
  const options = [
    {
      id: 1,
      title: 'Creation from Scratch',
      description: 'Build your digital twin from the ground up with our intuitive creation tools.',
      icon: <FaCode size={50} className="option-icon-svg" />,
      features: ['Visual Modeling', 'Integration Setup', 'Custom Attributes'],
      link: '/create-from-scratch'  // Link for navigation
    },
    {
      id: 2,
      title: 'Management',
      description: 'Monitor, update, and optimize your existing digital twins in real-time.',
      icon: <BiNetworkChart size={50} className="option-icon-svg" />,
      features: ['Live Monitoring', 'Performance Analytics', 'System Alerts']
    },
    {
      id: 3,
      title: 'Templates',
      description: 'Choose from pre-built templates to quickly deploy industry-specific digital twins.',
      icon: <FaFileAlt size={50} className="option-icon-svg" />,
      features: ['Industry Standards', 'Quick Deployment', 'Customizable']
    }
  ];

  
  return (
    <div className="app">
      <header>
        <nav>
          <div className="logo">
            <BiCog className="logo-icon" />
            <span>DTMP</span>
          </div>
          <div className="nav-links">
            <a href="#features"><FaRocket className="nav-icon" /> Features</a>
            <a href="#how-it-works"><HiOutlineLightBulb className="nav-icon" /> How it Works</a>
            <a href="#contact"><BiData className="nav-icon" /> Solutions</a>
            <button className="login-btn" onClick={() => navigate('/Login')}><BiCloudUpload className="btn-icon" /> Login</button>
          </div>
        </nav>
      </header>

      <main>
        <section className="hero">
          <div className="hero-content">
            <h1>Digital Twin Management Platform</h1>
            <p>Create, manage, and optimize digital replicas of your physical assets with industry-leading technology</p>
            <div className="hero-buttons">
              <button className="cta-btn primary-cta"><FaRocket className="btn-icon" /> Get Started</button>
              <button className="cta-btn secondary-cta"><FaLaptopCode className="btn-icon" /> Live Demo</button>
            </div>
          </div>
          <div className="hero-image">
            <div className="digital-twin-visualization">
              <div className="visualization-label physical-label">Physical Asset</div>
              <div className="physical-asset"></div>
              <div className="connection-lines">
                <div className="data-point p1"><IoSpeedometerSharp /></div>
                <div className="data-point p2"><BsGraphUp /></div>
                <div className="data-point p3"><IoAnalyticsSharp /></div>
                <div className="data-stream"></div>
              </div>
              <div className="digital-asset"></div>
              <div className="visualization-label digital-label">Digital Twin</div>
            </div>
          </div>
        </section>

        <section className="stats-banner">
          <div className="stat-item">
            <div className="stat-icon"><BsGraphUp /></div>
            <div className="stat-number">87%</div>
            <div className="stat-text">Increased Efficiency</div>
          </div>
          <div className="stat-item">
            <div className="stat-icon"><IoAnalyticsSharp /></div>
            <div className="stat-number">65%</div>
            <div className="stat-text">Reduced Downtime</div>
          </div>
          <div className="stat-item">
            <div className="stat-icon"><FaTools /></div>
            <div className="stat-number">5000+</div>
            <div className="stat-text">Assets Managed</div>
          </div>
        </section>

        <section className="options" id="options">
          <h2>Choose Your Path</h2>
          <div className="options-container">
            {options.map((option) => (
              <div 
                key={option.id}
                className={`option-card ${hoveredOption === option.id ? 'hovered' : ''}`}
                onMouseEnter={() => setHoveredOption(option.id)}
                onMouseLeave={() => setHoveredOption(null)}
                onClick={() => console.log(`Selected option: ${option.title}`)}
              >
                <div className="option-icon">
                  {option.icon}
                </div>
                <h3>{option.title}</h3>
                <p>{option.description}</p>
                <ul className="option-features">
                  {option.features.map((feature, index) => (
                    <li key={index}><BsThreeDots /> {feature}</li>
                  ))}
                </ul>
                <button className="option-btn">Select <span>&rarr;</span></button>
              </div>
            ))}
          </div>
        </section>

        <section className="how-it-works" id="how-it-works">
          <h2>How It Works</h2>
          <div className="steps-container">
            <div className="step">
              <div className="step-number">1</div>
              <div className="step-icon"><FaCode /></div>
              <h3>Model Creation</h3>
              <p>Create a digital model that mirrors your physical asset's characteristics and behaviors</p>
            </div>
            <div className="step-connector"></div>
            <div className="step">
              <div className="step-number">2</div>
              <div className="step-icon"><BiNetworkChart /></div>
              <h3>Data Integration</h3>
              <p>Connect real-time data sources to synchronize physical and digital entities</p>
            </div>
            <div className="step-connector"></div>
            <div className="step">
              <div className="step-number">3</div>
              <div className="step-icon"><IoAnalyticsSharp /></div>
              <h3>Analysis & Optimization</h3>
              <p>Leverage insights from your digital twin to optimize operations and predict maintenance</p>
            </div>
          </div>
        </section>

        <section className="features" id="features">
          <h2>Key Features</h2>
          <div className="features-grid">
            <div className="feature">
              <div className="feature-icon"><FaChartLine /></div>
              <h3>Real-time Monitoring</h3>
              <p>Track your physical assets with live data updates</p>
            </div>
            <div className="feature">
              <div className="feature-icon"><IoSpeedometerSharp /></div>
              <h3>Predictive Maintenance</h3>
              <p>Anticipate issues before they occur</p>
            </div>
            <div className="feature">
              <div className="feature-icon"><FaRocket /></div>
              <h3>Simulation Testing</h3>
              <p>Test scenarios without risk to physical assets</p>
            </div>
            <div className="feature">
              <div className="feature-icon"><BiData /></div>
              <h3>Data Analytics</h3>
              <p>Gain insights from comprehensive data analysis</p>
            </div>
          </div>
        </section>

        <section className="cta-section">
          <div className="cta-content">
            <h2>Ready to Transform Your Operations?</h2>
            <p>Join hundreds of industry leaders already using our platform</p>
            <button className="cta-btn primary-cta">Get Started Today</button>
          </div>
        </section>
      </main>
      <Routes>
        <Route path="/" element={<h1>Welcome to the Digital Twin Management Platform!</h1>} />
        <Route path="/Login" element={<Login />} />
        <Route path="/assistant" element={<ChatAgentPage />} />
      </Routes>
      <footer>
        <div className="footer-content">
          <div className="footer-info">
            <div className="footer-logo">
              <BiCog className="logo-icon" />
              <span>DTMP</span>
            </div>
            <p>Revolutionizing asset management through digital twin technology</p>
            <div className="social-icons">
            <a href="https://x.com/shaheemnaqvi" className="social-icon" target="_blank" rel="noopener noreferrer"><i className="fab fa-twitter"></i></a>
            <a href="https://linkedin.com/" className="social-icon" target="_blank" rel="noopener noreferrer"><i className="fab fa-linkedin"></i></a>
              <a href="https://github.com/ShaheemNaqvi" className="social-icon" target="_blank" rel="noopener noreferrer"><i className="fab fa-github"></i></a>
            </div>
          </div>
          <div className="footer-links">
            <div className="footer-column">
              <h4>Product</h4>
              <a href="#features">Features</a>
              <a href="#pricing">Pricing</a>
              <a href="#docs">Documentation</a>
              <a href="#updates">Updates</a>
            </div>
            <div className="footer-column">
              <h4>Company</h4>
              <a href="#about">About</a>
              <a href="#careers">Careers</a>
              <a href="#contact">Contact</a>
              <a href="#partners">Partners</a>
            </div>
            <div className="footer-column">
              <h4>Resources</h4>
              <a href="#blog">Blog</a>
              <a href="#support">Support</a>
              <a href="#community">Community</a>
              <a href="#webinars">Webinars</a>
            </div>
          </div>
        </div>
        <div className="footer-bottom">
          <p>&copy; 2025 Digital Twin Management Platform. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );  
}

export default App;