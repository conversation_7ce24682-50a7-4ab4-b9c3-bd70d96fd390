/* App.css */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

:root {
  /* New color palette */
  --primary: #6B46C1;
  --primary-light: #9F7AEA;
  --primary-dark: #553C9A;
  --secondary: #319795;
  --secondary-light: #4FD1C5;
  --secondary-dark: #2C7A7B;
  --accent: #ED8936;
  --accent-light: #F6AD55;
  --accent-dark: #DD6B20;
  --success: #48BB78;
  --error: #F56565;
  --background: #F7FAFC;
  --text-primary: #2D3748;
  --text-secondary: #4A5568;
  --text-light: #718096;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background);
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Icons General Styling */
.nav-icon, .btn-icon, .logo-icon {
  margin-right: 0.5rem;
  vertical-align: middle;
}

/* Header & Navigation */
header {
  background-color: #fff;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 5%;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary);
  display: flex;
  align-items: center;
}

.logo-icon {
  font-size: 1.8rem;
  margin-right: 0.5rem;
  animation: spin 10s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-links a {
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 500;
  transition: color 0.3s ease;
  display: flex;
  align-items: center;
}

.nav-links a:hover {
  color: var(--primary);
}

.login-btn {
  background-color: transparent;
  border: 2px solid var(--primary);
  color: var(--primary);
  padding: 0.5rem 1.5rem;
  border-radius: 50px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.login-btn:hover {
  background-color: var(--primary);
  color: #fff;
  transform: translateY(-2px);
}

/* Main Content */
main {
  flex: 1;
}

/* Hero Section */
.hero {
  display: flex;
  padding: 5rem 5% 3rem;
  max-width: 1200px;
  margin: 0 auto;
  align-items: center;
  gap: 2rem;
}

.hero-content {
  flex: 1;
}

.hero h1 {
  font-size: 3.5rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
  line-height: 1.2;
  font-weight: 700;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.hero p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: var(--text-secondary);
}

.hero-buttons {
  display: flex;
  gap: 1rem;
}

.cta-btn {
  display: flex;
  align-items: center;
  padding: 0.8rem 2rem;
  border: none;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.primary-cta {
  background-color: var(--primary);
  color: #fff;
}

.primary-cta:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(107, 70, 193, 0.2);
}

.secondary-cta {
  background-color: transparent;
  border: 2px solid var(--primary);
  color: var(--primary);
}

.secondary-cta:hover {
  background-color: rgba(107, 70, 193, 0.1);
  transform: translateY(-2px);
}

.hero-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

  
  /* Digital Twin Visualization (continued) */
.digital-twin-visualization {
  position: relative;
  width: 450px;
  height: 350px;
  background-color: #f8fafc;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(37, 99, 235, 0.2);
}

.visualization-label {
  position: absolute;
  background-color: rgba(107, 70, 193, 0.9);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  z-index: 10;
}

.physical-label {
  left: 30px;
  top: 70px;
}

.digital-label {
  right: 30px;
  top: 70px;
}

.physical-asset {
  position: absolute;
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  border-radius: 15px;
  left: 50px;
  top: 100px;
  box-shadow: 0 10px 25px rgba(107, 70, 193, 0.3);
  animation: pulse 3s infinite;
  z-index: 2;
}

.digital-asset {
  position: absolute;
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, var(--secondary-light), var(--secondary));
  border-radius: 15px;
  right: 50px;
  top: 100px;
  box-shadow: 0 10px 25px rgba(49, 151, 149, 0.3);
  animation: pulse 3s infinite reverse;
  z-index: 2;
}

.connection-lines {
  position: absolute;
  width: 160px;
  height: 80px;
  left: 50%;
  top: 120px;
  transform: translateX(-50%);
  z-index: 1;
}

.data-stream {
  position: absolute;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #0ea5e9, #6366f1);
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

.data-point {
  position: absolute;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #2563eb;
  font-size: 1rem;
  box-shadow: 0 5px 15px rgba(37, 99, 235, 0.3);
  animation: dataFlow 5s infinite;
  z-index: 3;
}

.p1 {
  left: 10%;
  top: 10%;
  animation-delay: 0s;
}

.p2 {
  left: 45%;
  top: 60%;
  animation-delay: 1.5s;
}

.p3 {
  left: 75%;
  top: 30%;
  animation-delay: 3s;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes dataFlow {
  0% {
    opacity: 0;
    transform: translateX(-50px) scale(0.8);
  }
  20% {
    opacity: 1;
    transform: translateX(-25px) scale(1);
  }
  80% {
    opacity: 1;
    transform: translateX(25px) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateX(50px) scale(0.8);
  }
}

/* Stats Banner */
.stats-banner {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  padding: 1.5rem 5%;
  display: flex;
  justify-content: space-around;
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.stats-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1), transparent 70%);
  pointer-events: none;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
  padding: 0.5rem;
}

.stat-icon {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.1);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.2rem;
  background: linear-gradient(90deg, #fff, rgba(255, 255, 255, 0.8));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-text {
  font-size: 0.9rem;
  font-weight: 500;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Options Section */
.options {
  padding: 4rem 5%;
  background-color: var(--background);
  position: relative;
  overflow: hidden;
}

.options::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(107, 70, 193, 0.1), transparent 70%);
  pointer-events: none;
}

.options h2 {
  text-align: center;
  margin-bottom: 3rem;
  font-size: 2.5rem;
  color: var(--text-primary);
  position: relative;
}

.options h2:after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  border-radius: 2px;
}

.options-container {
  display: flex;
  justify-content: center;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  flex-wrap: wrap;
}

.option-card {
  background-color: #fff;
  border-radius: 20px;
  padding: 2.5rem;
  width: 320px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  border: 1px solid rgba(107, 70, 193, 0.1);
  overflow: hidden;
}

.option-card:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.option-card.hovered {
  transform: translateY(-15px);
  box-shadow: 0 20px 40px rgba(107, 70, 193, 0.1);
  border-color: rgba(107, 70, 193, 0.2);
}

.option-card.hovered:before {
  transform: scaleX(1);
}

.option-icon {
  margin-bottom: 1.5rem;
  color: var(--primary);
}

.option-icon-svg {
  transition: all 0.3s ease;
}

.option-card.hovered .option-icon-svg {
  transform: scale(1.1);
  color: var(--primary-light);
}

.option-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.option-card p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

.option-features {
  list-style-type: none;
  text-align: left;
  margin-bottom: 1.5rem;
}

.option-features li {
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
}

.option-features li svg {
  margin-right: 0.5rem;
  color: var(--primary);
}

.option-btn {
  background-color: var(--primary);
  color: #fff;
  padding: 0.6rem 1.5rem;
  border: none;
  border-radius: 50px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}

.option-btn span {
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.option-btn:hover span {
  transform: translateX(5px);
}

.option-btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(107, 70, 193, 0.2);
}

/* How It Works Section */
.how-it-works {
  padding: 4rem 5%;
  background-color: var(--background);
  position: relative;
  overflow: hidden;
}

.how-it-works::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at bottom right, rgba(107, 70, 193, 0.05), transparent 70%);
  pointer-events: none;
}

.how-it-works h2 {
  text-align: center;
  margin-bottom: 3rem;
  font-size: 2.5rem;
  color: var(--text-primary);
  position: relative;
}

.how-it-works h2:after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  border-radius: 2px;
}

.steps-container {
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 1000px;
  margin: 0 auto;
  flex-wrap: wrap;
}

.step {
  background-color: #fff;
  border-radius: 20px;
  padding: 2rem;
  width: 250px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 2;
  border: 1px solid rgba(107, 70, 193, 0.1);
}

.step-number {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--primary);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 700;
  font-size: 1.2rem;
  box-shadow: 0 4px 10px rgba(107, 70, 193, 0.2);
}

.step-icon {
  font-size: 2rem;
  color: var(--primary);
  margin-bottom: 1rem;
}

.step h3 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.step p {
  color: var(--text-secondary);
}

.step-connector {
  width: 100px;
  height: 2px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  position: relative;
  z-index: 1;
}

/* Features Section */
.features {
  padding: 4rem 5%;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
}

.features::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at bottom left, rgba(49, 151, 149, 0.1), transparent 70%);
  pointer-events: none;
}

.features h2 {
  text-align: center;
  margin-bottom: 3rem;
  font-size: 2.5rem;
  color: var(--text-primary);
  position: relative;
}

.features h2:after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  border-radius: 2px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.feature {
  background-color: #fff;
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(107, 70, 193, 0.1);
  position: relative;
  overflow: hidden;
}

.feature::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.feature:hover {
  box-shadow: 0 15px 30px rgba(107, 70, 193, 0.1);
  transform: translateY(-5px);
  border-color: rgba(107, 70, 193, 0.2);
}

.feature:hover::before {
  transform: scaleX(1);
}

.feature-icon {
  font-size: 2rem;
  color: var(--primary);
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.feature:hover .feature-icon {
  color: var(--primary-light);
  transform: scale(1.1);
}

.feature h3 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.feature p {
  color: var(--text-secondary);
}

/* CTA Section */
.cta-section {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  padding: 3rem 5%;
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1), transparent 70%);
  pointer-events: none;
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.cta-section h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cta-section p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.cta-section .cta-btn {
  display: inline-flex;
  background-color: white;
  color: var(--primary);
  font-weight: 600;
  padding: 0.8rem 2.5rem;
  border-radius: 50px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.cta-section .cta-btn:hover {
  background-color: rgba(255, 255, 255, 0.95);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Footer */
footer {
  background-color: #1e293b;
  color: #f8fafc;
  padding: 4rem 5% 2rem;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  gap: 2rem;
}

.footer-info {
  flex: 1;
  min-width: 300px;
}

.footer-logo {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.footer-info p {
  color: #94a3b8;
  margin-bottom: 1.5rem;
}

.social-icons {
  display: flex;
  gap: 1rem;
}

  /* Footer (continued) */
.social-icon {
  background-color: #334155;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #f8fafc;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-icon:hover {
  background-color: #2563eb;
  transform: translateY(-3px);
}

.footer-links {
  display: flex;
  flex-wrap: wrap;
  gap: 3rem;
}

.footer-column {
  min-width: 150px;
}

.footer-column h4 {
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
  color: #f8fafc;
}

.footer-column a {
  display: block;
  color: #94a3b8;
  text-decoration: none;
  margin-bottom: 0.8rem;
  transition: color 0.3s ease;
}

.footer-column a:hover {
  color: #2563eb;
}

.footer-bottom {
  max-width: 1200px;
  margin: 0 auto;
  padding-top: 2rem;
  margin-top: 2rem;
  border-top: 1px solid #334155;
  text-align: center;
  color: #94a3b8;
  font-size: 0.9rem;
}

/* Responsive Design */
@media screen and (max-width: 992px) {
  .hero {
    flex-direction: column;
    text-align: center;
    padding: 3rem 5%;
  }
  
  .hero h1 {
    font-size: 2.8rem;
  }
  
  .hero-buttons {
    justify-content: center;
  }
  
  .steps-container {
    flex-direction: column;
    gap: 2rem;
  }
  
  .step-connector {
    width: 2px;
    height: 40px;
  }
  
  .digital-twin-visualization {
    max-width: 350px;
    height: 300px;
  }
  
  .stats-banner {
    flex-direction: column;
    gap: 2rem;
  }
}

@media screen and (max-width: 768px) {
  .nav-links {
    display: none;
  }
  
  .hero h1 {
    font-size: 2.5rem;
  }
  
  .options-container {
    flex-direction: column;
    align-items: center;
  }
  
  .option-card {
    width: 100%;
    max-width: 350px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .footer-content {
    flex-direction: column;
  }
  
  .footer-links {
    flex-direction: column;
    gap: 2rem;
  }

  .stats-banner {
    flex-direction: row;
    gap: 1rem;
    padding: 1rem 5%;
  }

  .stat-item {
    padding: 0.3rem;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 1.5rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .stat-text {
    font-size: 0.8rem;
  }

  .cta-section {
    padding: 2rem 5%;
  }

  .cta-section h2 {
    font-size: 2rem;
  }

  .cta-section p {
    font-size: 1.1rem;
  }

  .cta-section .cta-btn {
    padding: 0.7rem 2rem;
  }

  .how-it-works {
    padding: 3rem 5%;
  }

  .step {
    width: 100%;
    max-width: 300px;
    margin-bottom: 2rem;
  }

  .step-connector {
    width: 2px;
    height: 40px;
  }

  .step-icon {
    font-size: 1.8rem;
  }
}

/* Additional animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-content, .hero-image, .options h2, .option-card, .features h2, .feature, .cta-content {
  animation: fadeIn 1s ease forwards;
}

.option-card:nth-child(2) {
  animation-delay: 0.2s;
}

.option-card:nth-child(3) {
  animation-delay: 0.4s;
}

.feature:nth-child(2) {
  animation-delay: 0.2s;
}

.feature:nth-child(3) {
  animation-delay: 0.4s;
}

.feature:nth-child(4) {
  animation-delay: 0.6s;
}