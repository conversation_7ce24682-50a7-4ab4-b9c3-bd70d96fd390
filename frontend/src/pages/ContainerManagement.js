import React, { useEffect, useState } from "react";
import {
  Container,
  Typography,
  Button,
  Paper,
  Grid,
  Box,
  Card,
  CardContent,
  Chip,
  IconButton,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Snackbar,
  Alert,
  Tooltip,
} from "@mui/material";
import { Delete, Refresh, CheckCircle, Cancel, Info } from "@mui/icons-material";

function DeviceManagement() {
  const [devices, setDevices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Show snackbar message
  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  // Open delete confirmation dialog
  const handleOpenDeleteDialog = (device) => {
    setSelectedDevice(device);
    setOpenDeleteDialog(true);
  };

  // Close delete confirmation dialog
  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
  };

  // Fetch devices (containers) from backend
  const fetchDevices = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch("http://localhost:5000/management");

      if (!response.ok) {
        let errorMessage = "Failed to fetch devices";

        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;

          // Extract specific error contexts
          if (errorMessage.includes("ECONNREFUSED")) {
            errorMessage = "Connection refused: Could not connect to the backend server. Please check if the server is running.";
          } else if (errorMessage.includes("timeout")) {
            errorMessage = "Request timeout: The server took too long to respond. This might be due to network issues or high server load.";
          } else if (response.status === 404) {
            errorMessage = "API endpoint not found: The management endpoint could not be found. Please check the server configuration.";
          } else if (response.status === 500) {
            errorMessage = "Server error: The server encountered an internal error. Please check the server logs for more details.";
          }
        } catch (jsonError) {
          // If we can't parse the error as JSON, use the status text
          errorMessage = response.statusText || errorMessage;
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      setDevices(data);
    } catch (error) {
      setError(error.message);
      showSnackbar(`Error: ${error.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  // Initial device fetch
  useEffect(() => {
    fetchDevices();
  }, []);

  // Handle Refresh
  const handleRefresh = () => {
    setLoading(true);
    setError(null);
    fetchDevices();
  };

  // Handle Delete
  const handleDelete = async () => {
    if (!selectedDevice) return;

    try {
      setLoading(true);
      const response = await fetch(`http://localhost:5000/devices/${selectedDevice.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        let errorMessage = "Failed to delete device";

        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;

          // Extract specific error contexts
          if (errorMessage.includes("container not found")) {
            errorMessage = `Device not found: The device "${selectedDevice.name}" may have already been deleted or doesn't exist.`;
          } else if (errorMessage.includes("permission denied")) {
            errorMessage = `Permission denied: You don't have sufficient permissions to delete the device "${selectedDevice.name}".`;
          } else if (errorMessage.includes("is in use")) {
            errorMessage = `Device in use: The device "${selectedDevice.name}" is currently in use by another process. Please stop all processes using this device and try again.`;
          }
        } catch (jsonError) {
          // If we can't parse the error as JSON, use the status text
          errorMessage = response.statusText || errorMessage;
        }

        throw new Error(errorMessage);
      }

      setDevices((prev) => prev.filter((device) => device.id !== selectedDevice.id));
      showSnackbar(`Device ${selectedDevice.name} deleted successfully`);
      setOpenDeleteDialog(false);
    } catch (error) {
      setError(error.message);
      showSnackbar(`Error: ${error.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper elevation={3} sx={{ p: 4, backgroundColor: "#f8f9fa" }}>
        <Typography variant="h4" gutterBottom>
          Deployed Devices
        </Typography>
        <Box sx={{ display: "flex", justifyContent: "space-between", mb: 3 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleRefresh}
            startIcon={<Refresh />}
          >
            Refresh
          </Button>
        </Box>

        {loading ? (
          <CircularProgress />
        ) : error ? (
          <Typography color="error">{error}</Typography>
        ) : (
          <Grid container spacing={3}>
            {devices.map((device) => (
              <Grid item xs={12} sm={6} md={4} key={device.id}>
                <Card
                  elevation={3}
                  sx={{
                    padding: 2,
                    backgroundColor:
                      device.status === "Active" ? "#d4edda" : "#f8d7da",
                    borderLeft: `5px solid ${
                      device.status === "Active" ? "green" : "red"
                    }`,
                    position: "relative", // To position the buttons at the top right
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "space-between", // Align content within the card
                    height: "250px", // Flexible card height
                  }}
                >
                  {/* Active status and Delete button in the top-right corner */}
                  <Box
                    sx={{
                      position: "absolute",
                      top: 10,
                      right: 10,
                      display: "flex",
                      gap: 1,
                      alignItems: "center",
                    }}
                  >
                    {/* Active status as Chip */}
                    {device.status === "Active" ? (
                      <Chip
                        label="Active"
                        color="success"
                        icon={<CheckCircle />}
                        size="small"
                      />
                    ) : (
                      <Chip
                        label="Inactive"
                        color="error"
                        icon={<Cancel />}
                        size="small"
                      />
                    )}

                    {/* Delete button */}
                    <Tooltip title="Delete Device">
                      <IconButton
                        color="error"
                        onClick={() => handleOpenDeleteDialog(device)}
                      >
                        <Delete />
                      </IconButton>
                    </Tooltip>
                  </Box>

                  <CardContent sx={{ display: "flex", flexDirection: "column" }}>
                    <Typography variant="h6" sx={{ marginBottom: 1 }}>
                      {device.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ marginBottom: 1 }}>
                      Image: {device.image}
                    </Typography>
                    {/* Display the IP address and port in the format IP:Port */}
                    <Typography variant="body2" color="text.secondary" sx={{ marginBottom: 1 }}>
                      Exposed: {device.ip}:{device.port || "N/A"}
                    </Typography>

                    {/* Digital Twin button for Node-RED containers */}
                    {device.isNodeRed && (
                      <Box sx={{ display: "flex", justifyContent: "center", marginTop: "auto" }}>
                        <Button
                          variant="contained"
                          color="primary"
                          href={`http://localhost:8084/#/clients/${device.name}/3`}
                          target="_blank"
                        >
                          Go to Virtual Object
                        </Button>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </Paper>

      {/* Delete Confirmation Dialog */}
      <Dialog open={openDeleteDialog} onClose={handleCloseDeleteDialog}>
        <DialogTitle>Confirm Deletion</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the device "{selectedDevice?.name}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Cancel</Button>
          <Button onClick={handleDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
}

export default DeviceManagement;
