import React from 'react';
import { Routes, Route } from 'react-router-dom';
import MainLayout from './layouts/MainLayout';
import Home from './pages/Home';
import CreateFromScratch from './pages/CreateFromScratch';
import Login from './pages/Login';
import ContainerManagement from './pages/ContainerManagement';
import TemplateManagement from './pages/TemplateManagement';
import ChatAgentPage from './mcp-client/pages/ChatAgentPage';
import EnhancedChatAgentPage from './mcp-client/pages/EnhancedChatAgentPage';
import AGUIPage from './mcp-client/pages/AGUIPage';

import './App.css';


function App() {
    return (
        <Routes>
            {/* Routes with Layout */}
            <Route path="/" element={<MainLayout />}>
                <Route index element={<Home />} />
                <Route path="/create-from-scratch" element={<CreateFromScratch />} />
                <Route path="/management" element={<ContainerManagement />} />
                <Route path="/templates" element={<TemplateManagement />} />
                <Route path="/assistant" element={<EnhancedChatAgentPage />} />
                <Route path="/assistant-legacy" element={<ChatAgentPage />} />
                <Route path="/agui" element={<AGUIPage />} />
            </Route>

            {/* Standalone Routes */}
            <Route path="/login" element={<Login />} />
        </Routes>
    );
}

export default App;
