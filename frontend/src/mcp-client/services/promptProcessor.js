import mcpService from './mcpService';

/**
 * Process natural language prompts and send to the MCP server
 * @param {string} prompt - User's natural language prompt
 * @returns {Promise<Object>} Processed response with type and content
 */
const processPrompt = async (prompt) => {
  console.log(`Processing prompt: "${prompt}"`);

  // Validate the prompt
  if (!prompt || typeof prompt !== 'string' || prompt.trim() === '') {
    return {
      type: 'error',
      content: 'Empty or invalid query',
      details: 'Please provide a valid query.'
    };
  }

  // Make sure MCP service is initialized
  if (!mcpService.isClientConnected()) {
    try {
      const initialized = await mcpService.initialize();
      if (!initialized) {
        return {
          type: 'error',
          content: 'Failed to connect to the MCP server',
          details: 'Could not establish a connection with the MCP server. Please check if the server is running.'
        };
      }
    } catch (initError) {
      console.error('Error initializing MCP service:', initError);
      return {
        type: 'error',
        content: 'Failed to initialize MCP service',
        details: initError.message || 'An unknown error occurred during initialization.'
      };
    }
  }

  try {
    // Process common query patterns
    const normalizedPrompt = prompt.toLowerCase().trim();

    // Handle help queries
    if (normalizedPrompt === 'help' || normalizedPrompt === 'what can you do?' || normalizedPrompt === 'what can you do') {
      return {
        type: 'text',
        content: `
# Digital Twin Assistant Help

I can help you access and analyze data from your Digital Twin. Here are some things you can ask me:

## Data Queries
- "Show me temperature data from the last hour"
- "What's the current humidity?"
- "Show me air quality data for the past day"
- "Get pressure readings from the last week"

## System Information
- "List all available sensors"
- "What sensors are available?"
- "Show me the status of all sensors"

## Time Ranges
You can specify time ranges in your queries:
- "last hour", "last 1h"
- "last day", "last 24h"
- "last week", "last 7d"
- "last month", "last 30d"

Try asking a question about your sensor data!
        `.trim()
      };
    }

    // Send the query to the backend MCP server
    const response = await mcpService.sendQuery(prompt);

    // If the response is already an error, just return it
    if (response.type === 'error') {
      return response;
    }

    // Always expect type 'llm_response' and parse toolResults for data
    let data = response.data;
    if (!data && Array.isArray(response.toolResults)) {
      const queryDataResult = response.toolResults.find(r => r.name === 'query-data');
      if (queryDataResult && queryDataResult.result && queryDataResult.result.content) {
        if (typeof queryDataResult.result.content === 'string') {
          data = queryDataResult.result.content;
        } else if (Array.isArray(queryDataResult.result.content) && queryDataResult.result.content.length > 0) {
          const textPart = queryDataResult.result.content.find(part => part.type === 'text' && part.text);
          data = textPart ? textPart.text : JSON.stringify(queryDataResult.result.content);
        } else {
          data = JSON.stringify(queryDataResult.result.content);
        }
      }
    }

    return {
      type: response.type,
      content: response.content,
      data,
      toolResults: response.toolResults,
      chartConfig: response.chartConfig
    };
  } catch (error) {
    console.error('Error processing prompt:', error);
    return {
      type: 'error',
      content: `Error processing your query: ${error.message || 'An unknown error occurred'}`,
      details: error.stack ? `Stack trace: ${error.stack}` : 'No additional details available.'
    };
  }
};

export default processPrompt;
