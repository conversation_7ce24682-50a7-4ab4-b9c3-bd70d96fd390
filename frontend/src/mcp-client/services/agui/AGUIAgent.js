/**
 * AG-UI Compatible Agent Implementation
 * Implements the Agent User Interaction Protocol for standardized AI interactions
 * Based on official AG-UI documentation from Context7
 */

import { v4 as uuidv4 } from 'uuid';
import { BehaviorSubject, Subject, Observable, from } from 'rxjs';
import { map, filter, tap, catchError } from 'rxjs/operators';

// AG-UI Event Types (based on the documentation)
export const EventType = {
  // Lifecycle Events
  RUN_STARTED: 'RUN_STARTED',
  RUN_FINISHED: 'RUN_FINISHED',
  RUN_ERROR: 'RUN_ERROR',
  STEP_STARTED: 'STEP_STARTED',
  STEP_FINISHED: 'STEP_FINISHED',

  // Text Message Events
  TEXT_MESSAGE_START: 'TEXT_MESSAGE_START',
  TEXT_MESSAGE_CONTENT: 'TEXT_MESSAGE_CONTENT',
  TEXT_MESSAGE_END: 'TEXT_MESSAGE_END',

  // Tool Call Events
  TOOL_CALL_START: 'TOOL_CALL_START',
  TOOL_CALL_ARGS: 'TOOL_CALL_ARGS',
  TOOL_CALL_END: 'TOOL_CALL_END',

  // State Management Events
  STATE_SNAPSHOT: 'STATE_SNAPSHOT',
  STATE_DELTA: 'STATE_DELTA',
  MESSAGES_SNAPSHOT: 'MESSAGES_SNAPSHOT',

  // Special Events
  RAW: 'RAW',
  CUSTOM: 'CUSTOM'
};

/**
 * AG-UI Compatible Agent Class
 * Implements the Agent User Interaction Protocol for standardized communication
 */
export class AGUIAgent {
  constructor(config = {}) {
    this.agentId = config.agentId || uuidv4();
    this.threadId = config.threadId || uuidv4();
    this.description = config.description || 'Digital Twin Assistant';
    this.baseUrl = config.baseUrl || 'http://localhost:5000';

    // State management
    this.state = new BehaviorSubject(config.initialState || {});
    this.messages = new BehaviorSubject(config.initialMessages || []);

    // Event stream
    this.eventStream = new Subject();

    // Available tools
    this.availableTools = [];

    // Connection status
    this.isConnected = false;
  }

  /**
   * Get current state
   */
  get currentState() {
    return this.state.value;
  }

  /**
   * Get current messages
   */
  get currentMessages() {
    return this.messages.value;
  }

  /**
   * Add a message to the conversation
   */
  addMessage(message) {
    const newMessage = {
      id: message.id || uuidv4(),
      role: message.role,
      content: message.content,
      timestamp: new Date().toISOString(),
      ...message
    };

    const currentMessages = this.currentMessages;

    // Check if message already exists to prevent duplicates
    const messageExists = currentMessages.some(msg => msg.id === newMessage.id);
    if (messageExists) {
      return newMessage;
    }

    const updatedMessages = [...currentMessages, newMessage];

    // Update internal state immediately (synchronous)
    this.currentMessages = updatedMessages;

    // Use setTimeout to prevent synchronous state updates for observables
    setTimeout(() => {
      this.messages.next(updatedMessages);

      // Emit messages snapshot event
      this.eventStream.next({
        type: EventType.MESSAGES_SNAPSHOT,
        messages: updatedMessages,
        timestamp: Date.now()
      });
    }, 0);

    return newMessage;
  }

  /**
   * Update agent state
   */
  updateState(newState) {
    const currentState = this.currentState;
    const updatedState = { ...currentState, ...newState };

    // Check if state actually changed to prevent unnecessary updates
    if (JSON.stringify(currentState) === JSON.stringify(updatedState)) {
      return;
    }

    // Use setTimeout to prevent synchronous state updates
    setTimeout(() => {
      this.state.next(updatedState);

      // Emit state snapshot event
      this.eventStream.next({
        type: EventType.STATE_SNAPSHOT,
        snapshot: updatedState,
        timestamp: Date.now()
      });
    }, 0);
  }

  /**
   * Run the agent with a user query
   * Returns an Observable of AG-UI events
   */
  runAgent(input = {}) {
    const runId = input.runId || uuidv4();
    const tools = input.tools || [];
    const context = input.context || [];

    return new Observable(observer => {
      // Emit RUN_STARTED event
      observer.next({
        type: EventType.RUN_STARTED,
        threadId: this.threadId,
        runId,
        timestamp: Date.now()
      });

      // Process the query
      this.processQuery(input, runId, observer)
        .then(() => {
          // Emit RUN_FINISHED event
          observer.next({
            type: EventType.RUN_FINISHED,
            threadId: this.threadId,
            runId,
            timestamp: Date.now()
          });
          observer.complete();
        })
        .catch(error => {
          // Emit RUN_ERROR event
          observer.next({
            type: EventType.RUN_ERROR,
            message: error.message,
            code: error.code || 'UNKNOWN_ERROR',
            timestamp: Date.now()
          });
          observer.error(error);
        });
    });
  }

  /**
   * Process a query using AG-UI Server-Sent Events (SSE)
   */
  async processQuery(input, runId, observer) {
    try {
      // Get the user's query from the last message
      const userMessage = this.currentMessages
        .filter(m => m.role === 'user')
        .pop();

      if (!userMessage) {
        throw new Error('No user message found');
      }

      // Prepare AG-UI compliant request body
      const requestBody = {
        threadId: this.threadId,
        runId: runId,
        messages: this.currentMessages,
        tools: input.tools || [],
        context: input.context || []
      };

      console.log('[AG-UI] Sending request:', requestBody);

      // Call the AG-UI endpoint with SSE
      const response = await fetch(`${this.baseUrl}/api/agui/query`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Process Server-Sent Events stream
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let currentMessage = null;
      let pendingData = null; // Store data from STATE_SNAPSHOT for current message

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) break;

          // Decode the chunk and add to buffer
          buffer += decoder.decode(value, { stream: true });

          // Process complete events (separated by \n\n)
          const events = buffer.split('\n\n');
          buffer = events.pop(); // Keep incomplete event in buffer

          for (const eventData of events) {
            if (eventData.trim() === '') continue;

            try {
              // Parse SSE format: "data: {json}"
              const dataMatch = eventData.match(/^data: (.+)$/m);
              if (!dataMatch) continue;

              const event = JSON.parse(dataMatch[1]);
              console.log('[AG-UI] Received event:', event);

              // Handle different event types
              switch (event.type) {
                case 'RUN_STARTED':
                  observer.next({
                    type: EventType.RUN_STARTED,
                    threadId: event.threadId,
                    runId: event.runId,
                    timestamp: event.timestamp
                  });
                  break;

                case 'TEXT_MESSAGE_START':
                  currentMessage = {
                    id: event.messageId,
                    role: 'assistant',
                    content: '',
                    timestamp: new Date().toISOString()
                  };

                  observer.next({
                    type: EventType.TEXT_MESSAGE_START,
                    messageId: event.messageId,
                    role: event.role,
                    timestamp: event.timestamp
                  });
                  break;

                case 'TEXT_MESSAGE_CONTENT':
                  if (currentMessage && currentMessage.id === event.messageId) {
                    currentMessage.content += event.delta;
                  }

                  observer.next({
                    type: EventType.TEXT_MESSAGE_CONTENT,
                    messageId: event.messageId,
                    delta: event.delta,
                    timestamp: event.timestamp
                  });
                  break;

                case 'TEXT_MESSAGE_END':
                  if (currentMessage) {
                    // Attach pending data from STATE_SNAPSHOT if available
                    if (pendingData) {
                      currentMessage.toolResults = pendingData.toolResults;
                      currentMessage.data = pendingData.rawData;
                      currentMessage.metadata = pendingData.metadata || {};
                      console.log('[AG-UI] Attaching data to message:', {
                        hasToolResults: !!(pendingData.toolResults && pendingData.toolResults.length > 0),
                        hasRawData: !!pendingData.rawData,
                        toolResultsCount: pendingData.toolResults ? pendingData.toolResults.length : 0
                      });
                      pendingData = null; // Clear pending data
                    } else {
                      console.log('[AG-UI] No pending data to attach to message');
                    }

                    // Add the complete message to conversation
                    console.log('[AG-UI] Adding message with data:', {
                      hasToolResults: !!(currentMessage.toolResults && currentMessage.toolResults.length > 0),
                      hasData: !!currentMessage.data,
                      messageId: currentMessage.id
                    });
                    this.addMessage(currentMessage);
                    currentMessage = null;
                  }

                  observer.next({
                    type: EventType.TEXT_MESSAGE_END,
                    messageId: event.messageId,
                    timestamp: event.timestamp
                  });
                  break;

                case 'TOOL_CALL_START':
                  observer.next({
                    type: EventType.TOOL_CALL_START,
                    toolCallId: event.toolCallId,
                    toolCallName: event.toolCallName,
                    timestamp: event.timestamp
                  });
                  break;

                case 'TOOL_CALL_ARGS':
                  observer.next({
                    type: EventType.TOOL_CALL_ARGS,
                    toolCallId: event.toolCallId,
                    delta: event.delta,
                    timestamp: event.timestamp
                  });
                  break;

                case 'TOOL_CALL_END':
                  observer.next({
                    type: EventType.TOOL_CALL_END,
                    toolCallId: event.toolCallId,
                    timestamp: event.timestamp
                  });
                  break;

                case 'STATE_SNAPSHOT':
                  // Store data for the current message
                  console.log('[AG-UI] Received STATE_SNAPSHOT:', {
                    dataAvailable: event.snapshot.dataAvailable,
                    hasToolResults: !!(event.snapshot.toolResults && event.snapshot.toolResults.length > 0),
                    hasRawData: !!event.snapshot.rawData,
                    toolResultsCount: event.snapshot.toolResults ? event.snapshot.toolResults.length : 0
                  });

                  if (event.snapshot.dataAvailable) {
                    pendingData = {
                      toolResults: event.snapshot.toolResults,
                      rawData: event.snapshot.rawData,
                      metadata: event.snapshot.metadata
                    };
                    console.log('[AG-UI] Stored pending data for message attachment');
                  } else {
                    console.log('[AG-UI] No data available in STATE_SNAPSHOT');
                  }

                  this.updateState(event.snapshot);
                  observer.next({
                    type: EventType.STATE_SNAPSHOT,
                    snapshot: event.snapshot,
                    timestamp: event.timestamp
                  });
                  break;

                case 'RUN_FINISHED':
                  observer.next({
                    type: EventType.RUN_FINISHED,
                    threadId: event.threadId,
                    runId: event.runId,
                    timestamp: event.timestamp
                  });
                  break;

                case 'RUN_ERROR':
                  observer.next({
                    type: EventType.RUN_ERROR,
                    message: event.message,
                    code: event.code,
                    timestamp: event.timestamp
                  });
                  break;

                default:
                  console.log('[AG-UI] Unknown event type:', event.type);
              }

            } catch (parseError) {
              console.error('[AG-UI] Error parsing event:', parseError, eventData);
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

    } catch (error) {
      console.error('[AG-UI] Error processing query:', error);
      throw error;
    }
  }

  /**
   * Emit tool call events
   */
  async emitToolEvents(toolResult, observer) {
    const toolCallId = uuidv4();

    // Emit TOOL_CALL_START
    observer.next({
      type: EventType.TOOL_CALL_START,
      toolCallId,
      toolCallName: toolResult.name,
      timestamp: Date.now()
    });

    // Emit TOOL_CALL_ARGS
    if (toolResult.arguments) {
      const argsString = JSON.stringify(toolResult.arguments);
      observer.next({
        type: EventType.TOOL_CALL_ARGS,
        toolCallId,
        delta: argsString,
        timestamp: Date.now()
      });
    }

    // Emit TOOL_CALL_END
    observer.next({
      type: EventType.TOOL_CALL_END,
      toolCallId,
      timestamp: Date.now()
    });
  }

  /**
   * Subscribe to events
   */
  subscribe(observer) {
    return this.eventStream.subscribe(observer);
  }

  /**
   * Clone the agent with current state
   */
  clone() {
    return new AGUIAgent({
      agentId: this.agentId,
      threadId: uuidv4(), // New thread for clone
      description: this.description,
      baseUrl: this.baseUrl,
      initialState: this.currentState,
      initialMessages: this.currentMessages
    });
  }

  /**
   * Get agent configuration
   */
  getConfig() {
    return {
      agentId: this.agentId,
      threadId: this.threadId,
      description: this.description,
      state: this.currentState,
      messages: this.currentMessages,
      availableTools: this.availableTools
    };
  }

  /**
   * Set available tools
   */
  setTools(tools) {
    this.availableTools = tools;
  }

  /**
   * Disconnect and cleanup
   */
  disconnect() {
    this.isConnected = false;
    this.eventStream.complete();
  }
}

export default AGUIAgent;
