/**
 * AG-UI Compatible Agent Implementation
 * Implements the Agent User Interaction Protocol for standardized AI interactions
 * Based on official AG-UI documentation from Context7
 */

import { v4 as uuidv4 } from 'uuid';
import { BehaviorSubject, Subject, Observable, from } from 'rxjs';
import { map, filter, tap, catchError } from 'rxjs/operators';

// AG-UI Event Types (based on the documentation)
export const EventType = {
  // Lifecycle Events
  RUN_STARTED: 'RUN_STARTED',
  RUN_FINISHED: 'RUN_FINISHED',
  RUN_ERROR: 'RUN_ERROR',
  STEP_STARTED: 'STEP_STARTED',
  STEP_FINISHED: 'STEP_FINISHED',

  // Text Message Events
  TEXT_MESSAGE_START: 'TEXT_MESSAGE_START',
  TEXT_MESSAGE_CONTENT: 'TEXT_MESSAGE_CONTENT',
  TEXT_MESSAGE_END: 'TEXT_MESSAGE_END',

  // Tool Call Events
  TOOL_CALL_START: 'TOOL_CALL_START',
  TOOL_CALL_ARGS: 'TOOL_CALL_ARGS',
  TOOL_CALL_END: 'TOOL_CALL_END',

  // State Management Events
  STATE_SNAPSHOT: 'STATE_SNAPSHOT',
  STATE_DELTA: 'STATE_DELTA',
  MESSAGES_SNAPSHOT: 'MESSAGES_SNAPSHOT',

  // Special Events
  RAW: 'RAW',
  CUSTOM: 'CUSTOM'
};

/**
 * AG-UI Compatible Agent Class
 * Implements the Agent User Interaction Protocol for standardized communication
 */
export class AGUIAgent {
  constructor(config = {}) {
    this.agentId = config.agentId || uuidv4();
    this.threadId = config.threadId || uuidv4();
    this.description = config.description || 'Digital Twin Assistant';
    this.baseUrl = config.baseUrl || 'http://localhost:5000';

    // State management
    this.state = new BehaviorSubject(config.initialState || {});
    this.messages = new BehaviorSubject(config.initialMessages || []);

    // Event stream
    this.eventStream = new Subject();

    // Available tools
    this.availableTools = [];

    // Connection status
    this.isConnected = false;
  }

  /**
   * Get current state
   */
  get currentState() {
    return this.state.value;
  }

  /**
   * Get current messages
   */
  get currentMessages() {
    return this.messages.value;
  }

  /**
   * Add a message to the conversation
   */
  addMessage(message) {
    const newMessage = {
      id: message.id || uuidv4(),
      role: message.role,
      content: message.content,
      timestamp: new Date().toISOString(),
      ...message
    };

    const currentMessages = this.currentMessages;

    // Check if message already exists to prevent duplicates
    const messageExists = currentMessages.some(msg => msg.id === newMessage.id);
    if (messageExists) {
      return newMessage;
    }

    const updatedMessages = [...currentMessages, newMessage];

    // Use setTimeout to prevent synchronous state updates
    setTimeout(() => {
      this.messages.next(updatedMessages);

      // Emit messages snapshot event
      this.eventStream.next({
        type: EventType.MESSAGES_SNAPSHOT,
        messages: updatedMessages,
        timestamp: Date.now()
      });
    }, 0);

    return newMessage;
  }

  /**
   * Update agent state
   */
  updateState(newState) {
    const currentState = this.currentState;
    const updatedState = { ...currentState, ...newState };

    // Check if state actually changed to prevent unnecessary updates
    if (JSON.stringify(currentState) === JSON.stringify(updatedState)) {
      return;
    }

    // Use setTimeout to prevent synchronous state updates
    setTimeout(() => {
      this.state.next(updatedState);

      // Emit state snapshot event
      this.eventStream.next({
        type: EventType.STATE_SNAPSHOT,
        snapshot: updatedState,
        timestamp: Date.now()
      });
    }, 0);
  }

  /**
   * Run the agent with a user query
   * Returns an Observable of AG-UI events
   */
  runAgent(input = {}) {
    const runId = input.runId || uuidv4();
    const tools = input.tools || [];
    const context = input.context || [];

    return new Observable(observer => {
      // Emit RUN_STARTED event
      observer.next({
        type: EventType.RUN_STARTED,
        threadId: this.threadId,
        runId,
        timestamp: Date.now()
      });

      // Process the query
      this.processQuery(input, runId, observer)
        .then(() => {
          // Emit RUN_FINISHED event
          observer.next({
            type: EventType.RUN_FINISHED,
            threadId: this.threadId,
            runId,
            timestamp: Date.now()
          });
          observer.complete();
        })
        .catch(error => {
          // Emit RUN_ERROR event
          observer.next({
            type: EventType.RUN_ERROR,
            message: error.message,
            code: error.code || 'UNKNOWN_ERROR',
            timestamp: Date.now()
          });
          observer.error(error);
        });
    });
  }

  /**
   * Process a query and emit appropriate events
   */
  async processQuery(input, runId, observer) {
    try {
      // Get the user's query from the last message
      const userMessage = this.currentMessages
        .filter(m => m.role === 'user')
        .pop();

      if (!userMessage) {
        throw new Error('No user message found');
      }

      const query = userMessage.content;

      // Emit STEP_STARTED for query processing
      observer.next({
        type: EventType.STEP_STARTED,
        stepName: 'processing_query',
        timestamp: Date.now()
      });

      // Check if this is a test query for sample data
      let apiEndpoint = `${this.baseUrl}/api/agui/query`;
      let requestBody = {
        query,
        agentId: this.agentId,
        threadId: this.threadId
      };

      // Handle test queries for sample data
      if (query.toLowerCase().includes('test') &&
          (query.toLowerCase().includes('temperature') ||
           query.toLowerCase().includes('humidity') ||
           query.toLowerCase().includes('air quality'))) {

        apiEndpoint = `${this.baseUrl}/api/agui/test-data`;
        let dataType = 'temperature';
        if (query.toLowerCase().includes('humidity')) dataType = 'humidity';
        if (query.toLowerCase().includes('air quality')) dataType = 'air_quality';

        requestBody = { dataType };
      }

      // Call the backend API
      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      // Emit STEP_FINISHED for query processing
      observer.next({
        type: EventType.STEP_FINISHED,
        stepName: 'processing_query',
        timestamp: Date.now()
      });

      // Start streaming the assistant's response
      const messageId = uuidv4();

      // Emit TEXT_MESSAGE_START
      observer.next({
        type: EventType.TEXT_MESSAGE_START,
        messageId,
        role: 'assistant',
        timestamp: Date.now()
      });

      // Stream the content
      if (result.content) {
        // Ensure content is a string before streaming
        const contentString = typeof result.content === 'string'
          ? result.content
          : typeof result.content === 'object'
            ? JSON.stringify(result.content, null, 2)
            : String(result.content);

        // Simulate streaming by breaking content into chunks
        const chunkSize = 10; // Characters per chunk

        for (let i = 0; i < contentString.length; i += chunkSize) {
          const chunk = contentString.slice(i, i + chunkSize);

          observer.next({
            type: EventType.TEXT_MESSAGE_CONTENT,
            messageId,
            delta: chunk,
            timestamp: Date.now()
          });

          // Small delay to simulate real streaming
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }

      // Emit TEXT_MESSAGE_END
      observer.next({
        type: EventType.TEXT_MESSAGE_END,
        messageId,
        timestamp: Date.now()
      });

      // Add the assistant's message to the conversation
      // Ensure content is always a string for React compatibility
      const safeContent = typeof result.content === 'string'
        ? result.content
        : typeof result.content === 'object'
          ? JSON.stringify(result.content, null, 2)
          : String(result.content || 'No response content');

      const assistantMessage = {
        id: messageId,
        role: 'assistant',
        content: safeContent, // Always a string
        data: result.data,
        toolResults: result.toolResults,
        metadata: result.metadata
      };

      this.addMessage(assistantMessage);

      // If there are tool calls, emit tool events
      if (result.toolResults && result.toolResults.length > 0) {
        for (const toolResult of result.toolResults) {
          await this.emitToolEvents(toolResult, observer);
        }
      }

    } catch (error) {
      console.error('Error processing query:', error);
      throw error;
    }
  }

  /**
   * Emit tool call events
   */
  async emitToolEvents(toolResult, observer) {
    const toolCallId = uuidv4();

    // Emit TOOL_CALL_START
    observer.next({
      type: EventType.TOOL_CALL_START,
      toolCallId,
      toolCallName: toolResult.name,
      timestamp: Date.now()
    });

    // Emit TOOL_CALL_ARGS
    if (toolResult.arguments) {
      const argsString = JSON.stringify(toolResult.arguments);
      observer.next({
        type: EventType.TOOL_CALL_ARGS,
        toolCallId,
        delta: argsString,
        timestamp: Date.now()
      });
    }

    // Emit TOOL_CALL_END
    observer.next({
      type: EventType.TOOL_CALL_END,
      toolCallId,
      timestamp: Date.now()
    });
  }

  /**
   * Subscribe to events
   */
  subscribe(observer) {
    return this.eventStream.subscribe(observer);
  }

  /**
   * Clone the agent with current state
   */
  clone() {
    return new AGUIAgent({
      agentId: this.agentId,
      threadId: uuidv4(), // New thread for clone
      description: this.description,
      baseUrl: this.baseUrl,
      initialState: this.currentState,
      initialMessages: this.currentMessages
    });
  }

  /**
   * Get agent configuration
   */
  getConfig() {
    return {
      agentId: this.agentId,
      threadId: this.threadId,
      description: this.description,
      state: this.currentState,
      messages: this.currentMessages,
      availableTools: this.availableTools
    };
  }

  /**
   * Set available tools
   */
  setTools(tools) {
    this.availableTools = tools;
  }

  /**
   * Disconnect and cleanup
   */
  disconnect() {
    this.isConnected = false;
    this.eventStream.complete();
  }
}

export default AGUIAgent;
