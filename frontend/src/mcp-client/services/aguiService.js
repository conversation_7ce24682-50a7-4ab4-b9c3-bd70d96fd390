import { HttpAgent } from '@ag-ui/client';

// AG-UI agent endpoint (replace with your backend or cloud endpoint if needed)
const AGUI_AGENT_URL = process.env.REACT_APP_AGUI_AGENT_URL || 'http://localhost:8000/awp';

// Singleton agent instance
let agent = null;

export function getAgent() {
  if (!agent) {
    agent = new HttpAgent({ url: AGUI_AGENT_URL });
  }
  return agent;
}

/**
 * Send a prompt to the AG-UI agent and handle streaming events using async iteration.
 * @param {string} prompt - User's natural language prompt
 * @param {function} onEvent - Callback for each event (e.g., message chunk, tool call, etc.)
 * @param {function} onError - Callback for errors
 * @param {function} onComplete - Callback when the run is complete
 * @returns {function} unsubscribe - Call to stop listening
 */
export function sendPromptToAguiAgent(prompt, { onEvent, onError, onComplete }) {
  const agentInstance = getAgent();
  let cancelled = false;

  (async () => {
    try {
      const iterator = await agentInstance.runAgent({
        messages: [
          {
            id: Date.now().toString(),
            role: 'user',
            content: prompt
          }
        ]
      });
      for await (const event of iterator) {
        if (cancelled) break;
        onEvent(event);
      }
      if (!cancelled && onComplete) onComplete();
    } catch (err) {
      if (onError) onError(err);
    }
  })();

  // Return a cancel function
  return () => { cancelled = true; };
}

export default {
  sendPromptToAguiAgent,
  getAgent
}; 