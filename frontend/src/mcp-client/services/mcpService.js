import axios from 'axios';

/**
 * Service for interacting with the MCP server through the backend API
 */
class MCPService {
  constructor() {
    this.isConnected = false;
    this.apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
    this.tools = [];
  }

  /**
   * Initialize the MCP service
   * @returns {Promise<boolean>} Whether the initialization was successful
   */
  async initialize() {
    try {
      console.log('Initializing MCP service with API URL:', this.apiUrl);

      // Fetch available tools from the backend
      const response = await axios.get(`${this.apiUrl}/api/mcp/tools`);

      this.isConnected = response.data.connected;
      this.tools = response.data.tools || [];

      console.log('MCP service initialized successfully');
      console.log('Available tools:', this.tools);

      return this.isConnected;
    } catch (error) {
      console.error('Failed to initialize MCP service:', error);
      this.isConnected = false;
      return false;
    }
  }

  /**
   * Query InfluxDB through the MCP server
   * @param {string} query Flux query string
   * @param {string} org Organization name
   * @returns {Promise<any>} Query results
   */
  async queryInfluxDB(query, org = process.env.REACT_APP_INFLUXDB_ORG || 'ICT') {
    if (!this.isConnected) {
      await this.initialize();
    }

    try {
      console.log(`Querying InfluxDB with org: ${org}`);

      // Send the query to the backend API
      const response = await axios.post(`${this.apiUrl}/api/mcp/query`, {
        query: `Query InfluxDB with org=${org}: ${query}`
      });

      return response.data;
    } catch (error) {
      console.error('Error querying InfluxDB:', error);
      throw error;
    }
  }

  /**
   * Send a natural language query to the MCP server
   * @param {string} query Natural language query
   * @returns {Promise<any>} Query results
   */
  async sendQuery(query) {
    if (!this.isConnected) {
      try {
        const initialized = await this.initialize();
        if (!initialized) {
          return {
            type: 'error',
            content: 'Failed to connect to the MCP server. Please check if the server is running.',
            details: 'The MCP client could not establish a connection with the server.'
          };
        }
      } catch (initError) {
        console.error('Error initializing MCP service:', initError);
        return {
          type: 'error',
          content: 'Failed to initialize MCP service',
          details: initError.message
        };
      }
    }

    try {
      console.log(`Sending query: ${query}`);

      // Send the query to the backend API
      const response = await axios.post(`${this.apiUrl}/api/mcp/query`, { query });

      // Validate the response
      if (!response.data) {
        return {
          type: 'error',
          content: 'Received empty response from server',
          details: 'The server returned an empty response. Please try again.'
        };
      }

      console.log('Raw response from server:', response.data);

      // Always return a consistent contract
      const result = {
        type: 'llm_response',
        content: response.data.content || 'No content was returned from the server.',
        toolResults: response.data.toolResults || [],
      };
      if (response.data.data) {
        result.data = response.data.data;
      }
      if (response.data.chartConfig) {
        result.chartConfig = response.data.chartConfig;
      }
      console.log('Processed response:', result);
      return result;
    } catch (error) {
      console.error('Error sending query:', error);

      // Format the error response
      const errorResponse = {
        type: 'error',
        content: 'Error processing your query',
        details: error.message
      };

      // Extract more details from the error if available
      if (error.response) {
        if (error.response.data && error.response.data.error) {
          errorResponse.content = error.response.data.error;
        }
        if (error.response.data && error.response.data.details) {
          errorResponse.details = error.response.data.details;
        }
        errorResponse.status = error.response.status;
      }

      return errorResponse;
    }
  }

  /**
   * Write data to InfluxDB through the MCP server
   * @param {string} data Data in InfluxDB line protocol format
   * @param {string} bucket Bucket name
   * @param {string} org Organization name
   * @param {string} precision Timestamp precision
   * @returns {Promise<any>} Write result
   */
  async writeInfluxDB(data, bucket = process.env.REACT_APP_INFLUXDB_BUCKET || 'olive', org = process.env.REACT_APP_INFLUXDB_ORG || 'ICT', precision = 'ms') {
    if (!this.isConnected) {
      await this.initialize();
    }

    try {
      console.log(`Writing to InfluxDB bucket: ${bucket}, org: ${org}`);

      // Send the write request to the backend API
      const response = await axios.post(`${this.apiUrl}/api/mcp/query`, {
        query: `Write to InfluxDB with org=${org}, bucket=${bucket}, precision=${precision}: ${data}`
      });

      return response.data;
    } catch (error) {
      console.error('Error writing to InfluxDB:', error);
      throw error;
    }
  }

  /**
   * Get the list of available tools
   * @returns {Array} List of tools
   */
  getTools() {
    return this.tools;
  }

  /**
   * Check if the MCP client is connected
   * @returns {boolean} Whether the client is connected
   */
  isClientConnected() {
    return this.isConnected;
  }

  /**
   * Get the current LLM model being used
   * @returns {Promise<Object>} Current model information
   */
  async getCurrentModel() {
    try {
      const response = await axios.get(`${this.apiUrl}/api/mcp/llm`);
      return response.data;
    } catch (error) {
      console.error('Error getting current model:', error);
      // Return default model if there's an error
      return { activeLLM: 'google' };
    }
  }

  /**
   * Set the LLM model to use
   * @param {string} model - Model ID (e.g., 'openai', 'anthropic', 'google')
   * @returns {Promise<Object>} Result of setting the model
   */
  async setModel(model) {
    try {
      console.log(`Setting model to ${model}`);
      const response = await axios.post(`${this.apiUrl}/api/mcp/llm`, { llm: model });
      return response.data;
    } catch (error) {
      console.error('Error setting model:', error);
      throw error;
    }
  }

  /**
   * Close the connection to the MCP server
   */
  async close() {
    if (this.client) {
      await this.client.close();
      this.isConnected = false;
    }
  }
}

// Create and export a singleton instance
const mcpService = new MCPService();
export default mcpService;
