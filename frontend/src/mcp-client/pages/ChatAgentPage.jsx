import React, { useState, useEffect } from 'react';
import { Container, Typography, Grid, Paper, Box, Alert, Chip, Button } from '@mui/material';
import ChatAgent from '../components/ChatAgent';
import mcpService from '../services/mcpService';

// Get environment variables
const INFLUXDB_URL = process.env.REACT_APP_INFLUXDB_URL || 'http://localhost:8086';
const INFLUXDB_ORG = process.env.REACT_APP_INFLUXDB_ORG || 'ICT';
const INFLUXDB_BUCKET = process.env.REACT_APP_INFLUXDB_BUCKET || 'olive';

/**
 * Page component for the Digital Twin Chat Agent
 */
const ChatAgentPage = () => {
  const [connectionStatus, setConnectionStatus] = useState('checking');
  const [availableTools, setAvailableTools] = useState([]);
  const [toolsExpanded, setToolsExpanded] = useState({});

  // Initialize MCP service on component mount
  useEffect(() => {
    const initializeMCP = async () => {
      try {
        // Log the configuration
        console.log('MCP Client configured with:', {
          serverUrl: INFLUXDB_URL,
          org: INFLUXDB_ORG,
          bucket: INFLUXDB_BUCKET
        });

        // Initialize MCP service
        const success = await mcpService.initialize();
        if (success) {
          setConnectionStatus('connected');
          setAvailableTools(mcpService.getTools());

          // Initialize expanded state for each tool
          const expandedState = {};
          mcpService.getTools().forEach(tool => {
            expandedState[tool.name] = false;
          });
          setToolsExpanded(expandedState);
        } else {
          setConnectionStatus('error');
        }
      } catch (error) {
        console.error('Error initializing MCP service:', error);
        setConnectionStatus('error');
      }
    };

    initializeMCP();

    // Clean up on unmount
    return () => {
      mcpService.close();
    };
  }, []);

  // Toggle tool details expansion
  const toggleToolExpansion = (toolName) => {
    setToolsExpanded(prev => ({
      ...prev,
      [toolName]: !prev[toolName]
    }));
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 2, mb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Chip
            label={`MCP: ${connectionStatus === 'connected' ? 'Connected' : 'Checking...'}`}
            color={connectionStatus === 'connected' ? 'success' : connectionStatus === 'checking' ? 'warning' : 'error'}
            sx={{ mr: 1 }}
          />
          <Chip
            label={`Bucket: ${INFLUXDB_BUCKET}`}
            color="primary"
            sx={{ mr: 1 }}
          />
          <Chip
            label={`Org: ${INFLUXDB_ORG}`}
            color="secondary"
          />
        </Box>
      </Box>

      {connectionStatus === 'checking' && (
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            Connecting to InfluxDB MCP server at {INFLUXDB_URL}...
          </Typography>
          <Typography variant="body2">
            Please wait while we establish a connection to the MCP server.
          </Typography>
        </Alert>
      )}

      {connectionStatus === 'error' && (
        <Alert severity="error" sx={{ mb: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            Error connecting to InfluxDB MCP server at {INFLUXDB_URL}
          </Typography>
          <Typography variant="body2">
            Please check:
            <ul>
              <li>The InfluxDB server URL is correct</li>
              <li>The InfluxDB server is running and accessible</li>
              <li>Your environment variables are set correctly</li>
              <li>You have the necessary permissions to access the server</li>
            </ul>
            <Box sx={{ mt: 1 }}>
              See the browser console (F12) for detailed error information.
            </Box>
          </Typography>
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="outlined"
              color="error"
              size="small"
              onClick={() => {
                setConnectionStatus('checking');
                mcpService.initialize().then(success => {
                  setConnectionStatus(success ? 'connected' : 'error');
                  if (success) {
                    setAvailableTools(mcpService.getTools());
                  }
                }).catch(() => {
                  setConnectionStatus('error');
                });
              }}
            >
              Retry Connection
            </Button>
          </Box>
        </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <ChatAgent />
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Example Questions
            </Typography>

            <Box component="ul" sx={{ pl: 2 }}>
              <Box component="li" sx={{ mb: 1 }}>
                "Show me temperature data from the last hour"
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                "What's the current humidity?"
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                "List all available sensors"
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                "Show me air quality for the past 24 hours"
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                "What's the status of all sensors?"
              </Box>
            </Box>

            <Box sx={{ mt: 3, pt: 2, borderTop: '1px solid #eee' }}>
              <Typography variant="subtitle2" gutterBottom>
                Available MCP Tools
              </Typography>
              {availableTools.length > 0 ? (
                <Box sx={{ mt: 1 }}>
                  {availableTools.map((tool, index) => (
                    <Paper
                      key={index}
                      elevation={1}
                      sx={{
                        p: 1.5,
                        mb: 1.5,
                        cursor: 'pointer',
                        '&:hover': {
                          bgcolor: 'rgba(0, 0, 0, 0.03)'
                        }
                      }}
                      onClick={() => toggleToolExpansion(tool.name)}
                    >
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="subtitle2" color="primary">
                          {tool.name}
                        </Typography>
                        <Chip
                          size="small"
                          label={toolsExpanded[tool.name] ? "Hide" : "Details"}
                          color={toolsExpanded[tool.name] ? "secondary" : "default"}
                          sx={{ fontSize: '0.7rem' }}
                        />
                      </Box>

                      <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                        {tool.description || `Tool for interacting with InfluxDB`}
                      </Typography>

                      {toolsExpanded[tool.name] && (
                        <Box sx={{ mt: 1.5, pt: 1.5, borderTop: '1px dashed #eee' }}>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5 }}>
                            Required Parameters:
                          </Typography>
                          <Box component="ul" sx={{ pl: 2, mt: 0, mb: 0 }}>
                            {tool.inputSchema && tool.inputSchema.required && tool.inputSchema.required.map((param, idx) => (
                              <Box component="li" key={idx} sx={{ mb: 0.5 }}>
                                <Typography variant="caption">
                                  <strong>{param}</strong>
                                  {tool.inputSchema.properties && tool.inputSchema.properties[param] &&
                                    ` (${tool.inputSchema.properties[param].type}): ${tool.inputSchema.properties[param].description || ''}`
                                  }
                                </Typography>
                              </Box>
                            ))}
                          </Box>

                          {tool.inputSchema && tool.inputSchema.properties &&
                            Object.keys(tool.inputSchema.properties)
                              .filter(param => !tool.inputSchema.required || !tool.inputSchema.required.includes(param))
                              .length > 0 && (
                            <>
                              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1, mb: 0.5 }}>
                                Optional Parameters:
                              </Typography>
                              <Box component="ul" sx={{ pl: 2, mt: 0 }}>
                                {Object.keys(tool.inputSchema.properties)
                                  .filter(param => !tool.inputSchema.required || !tool.inputSchema.required.includes(param))
                                  .map((param, idx) => (
                                    <Box component="li" key={idx} sx={{ mb: 0.5 }}>
                                      <Typography variant="caption">
                                        <strong>{param}</strong>
                                        {` (${tool.inputSchema.properties[param].type}): ${tool.inputSchema.properties[param].description || ''}`}
                                      </Typography>
                                    </Box>
                                  ))
                                }
                              </Box>
                            </>
                          )}
                        </Box>
                      )}
                    </Paper>
                  ))}
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No tools available or still loading...
                </Typography>
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default ChatAgentPage;
