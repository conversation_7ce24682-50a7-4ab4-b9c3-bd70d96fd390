/**
 * AG-UI Page - Showcases the Agent User Interaction Protocol implementation
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Container,
  Typography,
  Grid,
  Paper,
  Box,
  Alert,
  Chip,
  Card,
  CardContent,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Button
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Psychology as PsychologyIcon,
  Api as ApiIcon,
  Speed as SpeedIcon,
  Security as SecurityIcon,
  Timeline as TimelineIcon,
  Code as CodeIcon,
  DataObject as DataIcon,
  Stream as StreamIcon,
  CheckCircle as CheckIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';

import AGUIChatInterface from '../components/agui/AGUIChatInterface';

/**
 * AG-UI Page Component
 */
const AGUIPage = () => {
  const [agentConfig, setAgentConfig] = useState({
    agentId: 'digital-twin-assistant-agui',
    description: 'Digital Twin Assistant with AG-UI Protocol',
    baseUrl: 'http://localhost:5000',
    initialState: {
      protocolVersion: '1.0',
      capabilities: ['sensor-data', 'real-time-monitoring', 'data-visualization'],
      connectionStatus: 'connected'
    }
  });

  const [systemStatus, setSystemStatus] = useState({
    aguiProtocol: 'active',
    eventStream: 'connected',
    agentState: 'ready',
    tools: 'available'
  });

  const [expandedAccordion, setExpandedAccordion] = useState('protocol');

  const aguiFeatures = [
    {
      icon: <StreamIcon />,
      title: "Event-Driven Architecture",
      description: "Real-time event streaming with RxJS observables"
    },
    {
      icon: <ApiIcon />,
      title: "Standardized Protocol",
      description: "AG-UI compliant agent interactions and state management"
    },
    {
      icon: <TimelineIcon />,
      title: "State Synchronization",
      description: "Automatic state snapshots and delta updates"
    },
    {
      icon: <SecurityIcon />,
      title: "Type-Safe Events",
      description: "Strongly typed event system with validation"
    }
  ];

  const protocolEvents = [
    { type: 'RUN_STARTED', description: 'Agent execution begins' },
    { type: 'TEXT_MESSAGE_START', description: 'Text streaming starts' },
    { type: 'TEXT_MESSAGE_CONTENT', description: 'Streaming content delta' },
    { type: 'TOOL_CALL_START', description: 'Tool execution begins' },
    { type: 'STATE_SNAPSHOT', description: 'Complete state update' },
    { type: 'RUN_FINISHED', description: 'Agent execution completes' }
  ];

  const exampleQueries = [
    {
      category: "🤖 AG-UI Protocol Testing",
      queries: [
        "Test the event streaming system",
        "Show me the current agent state",
        "Demonstrate tool call events",
        "Stream a long response with multiple chunks"
      ]
    },
    {
      category: "📊 Data Visualization Demo",
      queries: [
        "Test temperature data visualization",
        "Test humidity data visualization",
        "Test air quality data visualization",
        "Show sample sensor data in table format"
      ]
    },
    {
      category: "🔧 Advanced AG-UI Features",
      queries: [
        "Clone the current agent session",
        "Export conversation with metadata",
        "Show protocol compliance status",
        "Demonstrate error handling events"
      ]
    }
  ];

  const handleStateChange = useCallback((newState) => {
    console.log('Agent state changed:', newState);
    setSystemStatus(prev => ({
      ...prev,
      agentState: Object.keys(newState).length > 0 ? 'active' : 'ready'
    }));
  }, []);

  const handleMessageAdd = useCallback((message) => {
    console.log('New message added:', message);
  }, []);

  const handleTestProtocol = useCallback(() => {
    // This could trigger a test sequence
    console.log('Testing AG-UI protocol...');
  }, []);

  const handleExampleQuery = useCallback((query) => {
    // This would send the query to the chat interface
    // For now, just log it - in a real implementation, you'd pass this to the chat component
    console.log('Selected AG-UI example query:', query);
    // You could emit an event or use a callback to send this to the chat interface
  }, []);

  // Memoize tools and context to prevent re-initialization
  const tools = useMemo(() => [
    { name: 'query-data', description: 'Query sensor data from InfluxDB' },
    { name: 'get-status', description: 'Get system status' },
    { name: 'analyze-trends', description: 'Analyze data trends' }
  ], []);

  const context = useMemo(() => [
    { type: 'system', content: 'Digital Twin IoT Environment' },
    { type: 'protocol', content: 'AG-UI v1.0' }
  ], []);

  return (
    <Container maxWidth="xl" sx={{ mt: 2, mb: 4 }}>
      {/* Header Section */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ mb: 3 }}>
          <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 'bold' }}>
            AG-UI Digital Twin Assistant
          </Typography>
          <Typography variant="subtitle1" color="text.secondary" paragraph>
            Advanced Agent User Interaction Protocol implementation for standardized AI interactions
          </Typography>

          {/* Status Indicators */}
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
            <Chip
              icon={<CheckIcon />}
              label={`AG-UI Protocol: ${systemStatus.aguiProtocol}`}
              color="success"
              variant="outlined"
            />
            <Chip
              icon={<StreamIcon />}
              label={`Event Stream: ${systemStatus.eventStream}`}
              color="primary"
              variant="outlined"
            />
            <Chip
              icon={<PsychologyIcon />}
              label={`Agent: ${systemStatus.agentState}`}
              color="secondary"
              variant="outlined"
            />
            <Chip
              icon={<ApiIcon />}
              label={`Tools: ${systemStatus.tools}`}
              color="info"
              variant="outlined"
            />
          </Box>
        </Box>
      </motion.div>

      {/* Protocol Info Alert */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            🚀 AG-UI Protocol Active
          </Typography>
          <Typography variant="body2">
            This interface implements the Agent User Interaction Protocol for standardized,
            event-driven AI interactions. All agent communications use typed events, state
            synchronization, and real-time streaming.
          </Typography>
          <Button
            variant="outlined"
            size="small"
            onClick={handleTestProtocol}
            sx={{ mt: 1 }}
          >
            Test Protocol
          </Button>
        </Alert>
      </motion.div>

      {/* Main Content Grid */}
      <Grid container spacing={3}>
        {/* Chat Interface */}
        <Grid item xs={12} lg={8}>
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <AGUIChatInterface
              agentConfig={agentConfig}
              onStateChange={handleStateChange}
              onMessageAdd={handleMessageAdd}
              tools={tools}
              context={context}
            />
          </motion.div>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} lg={4}>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>

              {/* AG-UI Features */}
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                    <ApiIcon sx={{ mr: 1 }} />
                    AG-UI Features
                  </Typography>
                  <Grid container spacing={2}>
                    {aguiFeatures.map((feature, index) => (
                      <Grid item xs={12} key={index}>
                        <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                          <Box sx={{ mr: 2, color: 'primary.main' }}>
                            {feature.icon}
                          </Box>
                          <Box>
                            <Typography variant="subtitle2" gutterBottom>
                              {feature.title}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {feature.description}
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                    ))}
                  </Grid>
                </CardContent>
              </Card>

              {/* Protocol Events */}
              <Paper sx={{ p: 0 }}>
                <Accordion
                  expanded={expandedAccordion === 'protocol'}
                  onChange={() => setExpandedAccordion(expandedAccordion === 'protocol' ? '' : 'protocol')}
                >
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
                      <CodeIcon sx={{ mr: 1 }} />
                      Protocol Events
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List dense>
                      {protocolEvents.map((event, index) => (
                        <ListItem key={index}>
                          <ListItemIcon>
                            <DataIcon color="primary" fontSize="small" />
                          </ListItemIcon>
                          <ListItemText
                            primary={event.type}
                            secondary={event.description}
                            primaryTypographyProps={{
                              variant: 'body2',
                              fontFamily: 'monospace',
                              color: 'primary.main'
                            }}
                            secondaryTypographyProps={{
                              variant: 'caption'
                            }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              </Paper>

              {/* Example Queries */}
              <Paper sx={{ p: 0 }}>
                <Accordion
                  expanded={expandedAccordion === 'examples'}
                  onChange={() => setExpandedAccordion(expandedAccordion === 'examples' ? '' : 'examples')}
                >
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
                      <PsychologyIcon sx={{ mr: 1 }} />
                      AG-UI Examples
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    {exampleQueries.map((category, categoryIndex) => (
                      <Box key={categoryIndex} sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" gutterBottom color="primary">
                          {category.category}
                        </Typography>
                        <List dense>
                          {category.queries.map((query, queryIndex) => (
                            <ListItem
                              key={queryIndex}
                              button
                              onClick={() => handleExampleQuery(query)}
                              sx={{
                                borderRadius: 1,
                                mb: 0.5,
                                '&:hover': { bgcolor: 'action.hover' }
                              }}
                            >
                              <ListItemText
                                primary={query}
                                primaryTypographyProps={{
                                  variant: 'body2',
                                  sx: { fontSize: '0.875rem' }
                                }}
                              />
                            </ListItem>
                          ))}
                        </List>
                        {categoryIndex < exampleQueries.length - 1 && <Divider sx={{ my: 1 }} />}
                      </Box>
                    ))}
                  </AccordionDetails>
                </Accordion>
              </Paper>

              {/* Agent State Display */}
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                  <DataIcon sx={{ mr: 1 }} />
                  Agent State
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {Object.entries(agentConfig.initialState || {}).map(([key, value]) => (
                    <Chip
                      key={key}
                      label={`${key}: ${value}`}
                      size="small"
                      variant="outlined"
                      sx={{ fontSize: '0.7rem' }}
                    />
                  ))}
                </Box>
              </Paper>
            </Box>
          </motion.div>
        </Grid>
      </Grid>
    </Container>
  );
};

export default AGUIPage;
