import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Paper,
  Box,
  Alert,
  Chip,
  But<PERSON>,
  Card,
  CardContent,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  LinearProgress
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Psychology as PsychologyIcon,
  Speed as SpeedIcon,
  Storage as StorageIcon,
  Api as ApiIcon,
  TrendingUp as TrendingUpIcon,
  Help as HelpIcon,
  Lightbulb as LightbulbIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';

import EnhancedChatAgent from '../components/enhanced/EnhancedChatAgent';
import mcpService from '../services/mcpService';

// Get environment variables
const INFLUXDB_URL = process.env.REACT_APP_INFLUXDB_URL || 'http://localhost:8086';
const INFLUXDB_ORG = process.env.REACT_APP_INFLUXDB_ORG || 'ICT';
const INFLUXDB_BUCKET = process.env.REACT_APP_INFLUXDB_BUCKET || 'olive';

/**
 * Enhanced Chat Agent Page with improved layout and features
 */
const EnhancedChatAgentPage = () => {
  const [connectionStatus, setConnectionStatus] = useState('checking');
  const [availableTools, setAvailableTools] = useState([]);
  const [systemStats, setSystemStats] = useState({});
  const [expandedAccordion, setExpandedAccordion] = useState('examples');

  // Initialize MCP service on component mount
  useEffect(() => {
    const initializeMCP = async () => {
      try {
        console.log('MCP Client configured with:', {
          serverUrl: INFLUXDB_URL,
          org: INFLUXDB_ORG,
          bucket: INFLUXDB_BUCKET
        });

        const success = await mcpService.initialize();
        if (success) {
          setConnectionStatus('connected');
          setAvailableTools(mcpService.getTools());
          
          // Get system stats
          try {
            const stats = await mcpService.getSystemStats?.() || {};
            setSystemStats(stats);
          } catch (e) {
            console.warn('Could not fetch system stats:', e);
          }
        } else {
          setConnectionStatus('error');
        }
      } catch (error) {
        console.error('Error initializing MCP service:', error);
        setConnectionStatus('error');
      }
    };

    initializeMCP();

    return () => {
      mcpService.close();
    };
  }, []);

  const handleRetryConnection = async () => {
    setConnectionStatus('checking');
    try {
      const success = await mcpService.initialize();
      setConnectionStatus(success ? 'connected' : 'error');
      if (success) {
        setAvailableTools(mcpService.getTools());
      }
    } catch (error) {
      console.error('Retry connection failed:', error);
      setConnectionStatus('error');
    }
  };

  const exampleQueries = [
    {
      category: "📊 Data Analysis",
      queries: [
        "Show me temperature trends from the last 24 hours",
        "What's the current humidity level?",
        "Compare air quality data between different sensors",
        "Show me the average temperature for this week"
      ]
    },
    {
      category: "🔍 Sensor Information",
      queries: [
        "List all available sensors and their status",
        "Which sensors are currently active?",
        "Show me sensor locations and types",
        "What data fields are available for each sensor?"
      ]
    },
    {
      category: "📈 Advanced Analytics",
      queries: [
        "Identify temperature anomalies in the last week",
        "Show correlation between humidity and temperature",
        "Generate a summary report of all sensor data",
        "Predict temperature trends for the next hour"
      ]
    },
    {
      category: "⚡ Real-time Monitoring",
      queries: [
        "Show me live sensor readings",
        "Alert me if temperature exceeds 30°C",
        "Monitor air quality in real-time",
        "Display current status of all systems"
      ]
    }
  ];

  const features = [
    {
      icon: <PsychologyIcon />,
      title: "AI-Powered Analysis",
      description: "Natural language processing with advanced LLM models"
    },
    {
      icon: <SpeedIcon />,
      title: "Real-time Data",
      description: "Live sensor data with instant visualization"
    },
    {
      icon: <StorageIcon />,
      title: "Multi-format Support",
      description: "JSON, CSV, charts, tables, and more"
    },
    {
      icon: <ApiIcon />,
      title: "MCP Integration",
      description: "Extensible Model Context Protocol support"
    }
  ];

  return (
    <Container maxWidth="xl" sx={{ mt: 2, mb: 4 }}>
      {/* Header Section */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ mb: 3 }}>
          <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 'bold' }}>
            Enhanced Digital Twin Assistant
          </Typography>
          <Typography variant="subtitle1" color="text.secondary" paragraph>
            Advanced AI-powered interface for sensor data analysis and visualization
          </Typography>
          
          {/* Status Indicators */}
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
            <Chip
              label={`MCP: ${connectionStatus === 'connected' ? 'Connected' : connectionStatus === 'checking' ? 'Connecting...' : 'Disconnected'}`}
              color={connectionStatus === 'connected' ? 'success' : connectionStatus === 'checking' ? 'warning' : 'error'}
              variant="outlined"
            />
            <Chip
              label={`Database: ${INFLUXDB_BUCKET}`}
              color="primary"
              variant="outlined"
            />
            <Chip
              label={`Organization: ${INFLUXDB_ORG}`}
              color="secondary"
              variant="outlined"
            />
            <Chip
              label={`Tools: ${availableTools.length}`}
              color="info"
              variant="outlined"
            />
          </Box>
        </Box>
      </motion.div>

      {/* Connection Status Alerts */}
      {connectionStatus === 'checking' && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <Alert severity="info" sx={{ mb: 3 }}>
            <Box>
              <Typography variant="subtitle1" gutterBottom>
                Establishing connection to MCP server...
              </Typography>
              <LinearProgress sx={{ mt: 1 }} />
              <Typography variant="body2" sx={{ mt: 1 }}>
                Server: {INFLUXDB_URL}
              </Typography>
            </Box>
          </Alert>
        </motion.div>
      )}

      {connectionStatus === 'error' && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <Alert severity="error" sx={{ mb: 3 }}>
            <Typography variant="subtitle1" gutterBottom>
              Failed to connect to MCP server
            </Typography>
            <Typography variant="body2" paragraph>
              Unable to establish connection to {INFLUXDB_URL}. Please verify:
            </Typography>
            <Box component="ul" sx={{ pl: 2, mb: 2 }}>
              <li>InfluxDB server is running and accessible</li>
              <li>Environment variables are configured correctly</li>
              <li>Network connectivity is available</li>
              <li>Authentication credentials are valid</li>
            </Box>
            <Button
              variant="outlined"
              color="error"
              onClick={handleRetryConnection}
              sx={{ mt: 1 }}
            >
              Retry Connection
            </Button>
          </Alert>
        </motion.div>
      )}

      {/* Main Content Grid */}
      <Grid container spacing={3}>
        {/* Chat Interface */}
        <Grid item xs={12} lg={8}>
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <EnhancedChatAgent />
          </motion.div>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} lg={4}>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              
              {/* Features Overview */}
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                    <TrendingUpIcon sx={{ mr: 1 }} />
                    Key Features
                  </Typography>
                  <Grid container spacing={2}>
                    {features.map((feature, index) => (
                      <Grid item xs={12} key={index}>
                        <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                          <Box sx={{ mr: 2, color: 'primary.main' }}>
                            {feature.icon}
                          </Box>
                          <Box>
                            <Typography variant="subtitle2" gutterBottom>
                              {feature.title}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {feature.description}
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                    ))}
                  </Grid>
                </CardContent>
              </Card>

              {/* Example Queries */}
              <Paper sx={{ p: 0 }}>
                <Accordion 
                  expanded={expandedAccordion === 'examples'} 
                  onChange={() => setExpandedAccordion(expandedAccordion === 'examples' ? '' : 'examples')}
                >
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
                      <LightbulbIcon sx={{ mr: 1 }} />
                      Example Queries
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    {exampleQueries.map((category, categoryIndex) => (
                      <Box key={categoryIndex} sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" gutterBottom color="primary">
                          {category.category}
                        </Typography>
                        <List dense>
                          {category.queries.map((query, queryIndex) => (
                            <ListItem 
                              key={queryIndex}
                              button
                              onClick={() => {
                                // You could implement auto-fill functionality here
                                console.log('Selected query:', query);
                              }}
                              sx={{ 
                                borderRadius: 1, 
                                mb: 0.5,
                                '&:hover': { bgcolor: 'action.hover' }
                              }}
                            >
                              <ListItemText 
                                primary={query}
                                primaryTypographyProps={{ 
                                  variant: 'body2',
                                  sx: { fontSize: '0.875rem' }
                                }}
                              />
                            </ListItem>
                          ))}
                        </List>
                        {categoryIndex < exampleQueries.length - 1 && <Divider sx={{ my: 1 }} />}
                      </Box>
                    ))}
                  </AccordionDetails>
                </Accordion>
              </Paper>

              {/* Available Tools */}
              {availableTools.length > 0 && (
                <Paper sx={{ p: 0 }}>
                  <Accordion 
                    expanded={expandedAccordion === 'tools'} 
                    onChange={() => setExpandedAccordion(expandedAccordion === 'tools' ? '' : 'tools')}
                  >
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
                        <ApiIcon sx={{ mr: 1 }} />
                        Available Tools ({availableTools.length})
                      </Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <List dense>
                        {availableTools.map((tool, index) => (
                          <ListItem key={index} sx={{ flexDirection: 'column', alignItems: 'flex-start' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                              <Typography variant="subtitle2" color="primary">
                                {tool.name}
                              </Typography>
                              <Chip 
                                label="Active"
                                size="small"
                                color="success"
                                variant="outlined"
                                sx={{ ml: 'auto', fontSize: '0.7rem' }}
                              />
                            </Box>
                            <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                              {tool.description || `Tool for ${tool.name} operations`}
                            </Typography>
                          </ListItem>
                        ))}
                      </List>
                    </AccordionDetails>
                  </Accordion>
                </Paper>
              )}

              {/* Help Section */}
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                  <HelpIcon sx={{ mr: 1 }} />
                  Quick Help
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  <strong>Keyboard Shortcuts:</strong>
                </Typography>
                <Box component="ul" sx={{ pl: 2, fontSize: '0.875rem', color: 'text.secondary' }}>
                  <li>Ctrl+Enter: Send message</li>
                  <li>Ctrl+L: Clear chat</li>
                  <li>Esc: Close dialogs</li>
                </Box>
              </Paper>
            </Box>
          </motion.div>
        </Grid>
      </Grid>
    </Container>
  );
};

export default EnhancedChatAgentPage;
