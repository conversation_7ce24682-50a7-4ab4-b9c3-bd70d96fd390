/**
 * AG-UI Compatible Chat Interface
 * Implements the Agent User Interaction Protocol for standardized AI interactions
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  TextField,
  Button,
  Paper,
  Typography,
  Avatar,
  CircularProgress,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  LinearProgress
} from '@mui/material';
import {
  Send as SendIcon,
  SmartToy as BotIcon,
  Person as PersonIcon,
  Settings as SettingsIcon,
  Download as DownloadIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { v4 as uuidv4 } from 'uuid';

import AGUIAgent, { EventType } from '../../services/agui/AGUIAgent';
import AGUIMessage from './AGUIMessage';

/**
 * AG-UI Compatible Chat Interface Component
 */
const AGUIChatInterface = ({
  agentConfig = {},
  onStateChange,
  onMessageAdd,
  tools = [],
  context = []
}) => {
  // Agent instance
  const [agent, setAgent] = useState(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Chat state
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [isRunning, setIsRunning] = useState(false);
  const [currentRun, setCurrentRun] = useState(null);
  const [agentState, setAgentState] = useState({});

  // UI state
  const [streamingMessage, setStreamingMessage] = useState(null);
  const [currentStep, setCurrentStep] = useState(null);
  const [error, setError] = useState(null);

  // Refs
  const messagesEndRef = useRef(null);
  const runSubscriptionRef = useRef(null);

  // Initialize agent
  useEffect(() => {
    const newAgent = new AGUIAgent({
      agentId: agentConfig.agentId || 'digital-twin-assistant',
      threadId: agentConfig.threadId || uuidv4(),
      description: agentConfig.description || 'Digital Twin Assistant',
      baseUrl: agentConfig.baseUrl || 'http://localhost:5000',
      initialState: agentConfig.initialState || {},
      initialMessages: agentConfig.initialMessages || [
        {
          id: uuidv4(),
          role: 'assistant',
          content: `Hello! I'm your Digital Twin Assistant powered by the AG-UI protocol. I can help you with:

• **Sensor Data Analysis** - Query and analyze your IoT sensor data
• **Real-time Monitoring** - Get live updates from your devices
• **Data Visualization** - View data in charts, tables, and graphs
• **Natural Language Queries** - Ask questions in plain English

What would you like to explore today?`,
          timestamp: new Date().toISOString()
        }
      ]
    });

    // Set available tools
    newAgent.setTools(tools);

    // Subscribe to agent state changes (with proper cleanup)
    const stateSubscription = newAgent.state.subscribe(state => {
      setAgentState(prevState => {
        // Only update if state actually changed
        if (JSON.stringify(prevState) !== JSON.stringify(state)) {
          onStateChange?.(state);
          return state;
        }
        return prevState;
      });
    });

    // Subscribe to message changes (with proper cleanup)
    const messageSubscription = newAgent.messages.subscribe(msgs => {
      setMessages(prevMessages => {
        // Only update if messages actually changed
        if (JSON.stringify(prevMessages) !== JSON.stringify(msgs)) {
          // Call onMessageAdd only for new messages
          const newMessages = msgs.slice(prevMessages.length);
          newMessages.forEach(msg => onMessageAdd?.(msg));
          return msgs;
        }
        return prevMessages;
      });
    });

    setAgent(newAgent);
    setIsInitialized(true);

    return () => {
      stateSubscription?.unsubscribe();
      messageSubscription?.unsubscribe();
      newAgent.disconnect();
    };
  }, []); // Remove dependencies that cause re-initialization

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, streamingMessage]);

  // Handle sending a message
  const handleSend = useCallback(async () => {
    if (!input.trim() || isRunning || !agent) return;

    const userMessage = {
      id: uuidv4(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date().toISOString()
    };

    // Add user message
    agent.addMessage(userMessage);
    setInput('');
    setIsRunning(true);
    setError(null);
    setStreamingMessage(null);
    setCurrentStep(null);

    const runId = uuidv4();
    setCurrentRun(runId);

    try {
      // Run the agent
      const runObservable = agent.runAgent({
        runId,
        tools,
        context
      });

      // Subscribe to events
      runSubscriptionRef.current = runObservable.subscribe({
        next: (event) => {
          handleAgentEvent(event);
        },
        error: (error) => {
          console.error('Agent run error:', error);
          setError(error.message);
          setIsRunning(false);
          setStreamingMessage(null);
          setCurrentStep(null);
        },
        complete: () => {
          setIsRunning(false);
          setStreamingMessage(null);
          setCurrentStep(null);
          setCurrentRun(null);
        }
      });

    } catch (error) {
      console.error('Error starting agent run:', error);
      setError(error.message);
      setIsRunning(false);
    }
  }, [input, isRunning, agent, tools, context]);

  // Handle AG-UI events
  const handleAgentEvent = useCallback((event) => {
    console.log('AG-UI Event:', event);

    switch (event.type) {
      case EventType.RUN_STARTED:
        setIsRunning(true);
        break;

      case EventType.RUN_FINISHED:
        setIsRunning(false);
        setStreamingMessage(null);
        setCurrentStep(null);
        break;

      case EventType.RUN_ERROR:
        setError(event.message);
        setIsRunning(false);
        break;

      case EventType.STEP_STARTED:
        setCurrentStep(event.stepName);
        break;

      case EventType.STEP_FINISHED:
        setCurrentStep(null);
        break;

      case EventType.TEXT_MESSAGE_START:
        setStreamingMessage({
          id: event.messageId,
          role: event.role,
          content: '',
          isStreaming: true,
          timestamp: new Date().toISOString()
        });
        break;

      case EventType.TEXT_MESSAGE_CONTENT:
        setStreamingMessage(prev => {
          if (prev && prev.id === event.messageId) {
            return {
              ...prev,
              content: prev.content + event.delta
            };
          }
          return prev;
        });
        break;

      case EventType.TEXT_MESSAGE_END:
        setStreamingMessage(null);
        break;

      case EventType.TOOL_CALL_START:
        console.log('Tool call started:', event.toolCallName);
        break;

      case EventType.TOOL_CALL_END:
        console.log('Tool call ended');
        break;

      case EventType.STATE_SNAPSHOT:
        setAgentState(event.snapshot);
        break;

      case EventType.MESSAGES_SNAPSHOT:
        setMessages(event.messages);
        break;

      default:
        console.log('Unhandled event type:', event.type);
    }
  }, []);

  // Handle key press
  const handleKeyPress = useCallback((e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  }, [handleSend]);

  // Clear chat
  const handleClear = useCallback(() => {
    if (agent) {
      agent.messages.next([]);
      setMessages([]);
      setStreamingMessage(null);
      setError(null);
    }
  }, [agent]);

  // Export chat
  const handleExport = useCallback(() => {
    const chatData = {
      agentId: agent?.agentId,
      threadId: agent?.threadId,
      timestamp: new Date().toISOString(),
      messages,
      state: agentState
    };

    const blob = new Blob([JSON.stringify(chatData, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `agui-chat-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [agent, messages, agentState]);

  if (!isInitialized) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Initializing AG-UI Agent...</Typography>
      </Box>
    );
  }

  return (
    <Paper elevation={3} sx={{ height: '70vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{
        p: 2,
        bgcolor: 'primary.main',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Avatar sx={{ mr: 2, bgcolor: 'secondary.main' }}>
            <BotIcon />
          </Avatar>
          <Box>
            <Typography variant="h6">AG-UI Digital Twin Assistant</Typography>
            <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
              <Chip
                label={`Thread: ${agent?.threadId?.slice(-8)}`}
                size="small"
                sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white', fontSize: '0.7rem' }}
              />
              <Chip
                label={isRunning ? 'Running' : 'Ready'}
                size="small"
                color={isRunning ? 'warning' : 'success'}
                sx={{ fontSize: '0.7rem' }}
              />
            </Box>
          </Box>
        </Box>

        <Box>
          <Tooltip title="Export Chat">
            <IconButton onClick={handleExport} sx={{ color: 'white', mr: 1 }}>
              <DownloadIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Clear Chat">
            <IconButton onClick={handleClear} sx={{ color: 'white' }}>
              <ClearIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Progress indicator */}
      {isRunning && (
        <Box>
          <LinearProgress />
          {currentStep && (
            <Box sx={{ p: 1, bgcolor: 'info.light', color: 'info.contrastText' }}>
              <Typography variant="caption">
                Processing: {currentStep.replace(/_/g, ' ')}
              </Typography>
            </Box>
          )}
        </Box>
      )}

      {/* Error display */}
      {error && (
        <Alert severity="error" onClose={() => setError(null)} sx={{ m: 1 }}>
          {error}
        </Alert>
      )}

      {/* Messages */}
      <Box sx={{
        flexGrow: 1,
        p: 2,
        overflowY: 'auto',
        backgroundColor: '#fafafa'
      }}>
        <AnimatePresence>
          {messages.map((message) => (
            <AGUIMessage
              key={message.id}
              message={message}
              agentState={agentState}
            />
          ))}
        </AnimatePresence>

        {/* Streaming message */}
        {streamingMessage && (
          <AGUIMessage
            message={streamingMessage}
            agentState={agentState}
            isStreaming={true}
          />
        )}

        <div ref={messagesEndRef} />
      </Box>

      {/* Input */}
      <Box sx={{
        p: 2,
        backgroundColor: 'background.paper',
        borderTop: '1px solid',
        borderColor: 'divider',
        display: 'flex',
        gap: 1
      }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Ask about your sensor data... (AG-UI Protocol)"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyPress={handleKeyPress}
          disabled={isRunning}
          multiline
          maxRows={4}
          sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
        />
        <Button
          variant="contained"
          color="primary"
          endIcon={isRunning ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
          onClick={handleSend}
          disabled={isRunning || !input.trim()}
          sx={{ minWidth: 100, borderRadius: 2, height: 'fit-content', alignSelf: 'flex-end' }}
        >
          Send
        </Button>
      </Box>
    </Paper>
  );
};

export default AGUIChatInterface;
