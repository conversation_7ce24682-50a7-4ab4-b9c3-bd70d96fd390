/**
 * AG-UI Data Standardizer Component
 * Automatically detects and standardizes data visualization based on AG-UI protocol
 * Handles diverse data types dynamically without hardcoded responses
 */

import React, { useMemo } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  Alert,
  Chip
} from '@mui/material';
import {
  Table<PERSON>hart as TableIcon,
  BarChart as ChartIcon,
  Code as JsonIcon,
  TextFields as TextIcon,
  DataObject as DataIcon
} from '@mui/icons-material';

import InteractiveTable from '../enhanced/InteractiveTable';
import InteractiveChart from '../enhanced/InteractiveChart';

/**
 * AG-UI Data Standardizer - Automatically handles any data type
 */
const AGUIDataStandardizer = ({ agentState, onFeedback }) => {
  // Extract data from AG-UI state
  const dataAnalysis = useMemo(() => {
    if (!agentState || !agentState.dataAvailable) {
      return { type: 'none', data: null, views: [] };
    }

    let rawData = null;
    let dataType = 'unknown';
    let availableViews = ['text'];

    // Extract data from toolResults (AG-UI standard)
    if (agentState.toolResults && agentState.toolResults.length > 0) {
      for (const tool of agentState.toolResults) {
        if (tool.result && tool.result.content) {
          if (Array.isArray(tool.result.content)) {
            // Handle array of content objects
            for (const contentItem of tool.result.content) {
              if (contentItem.text && contentItem.text.trim() && contentItem.text !== '\r\n') {
                rawData = contentItem.text;
                break;
              }
            }
          } else if (typeof tool.result.content === 'string' && tool.result.content.trim()) {
            rawData = tool.result.content;
            break;
          }
        }
      }
    }

    // Fallback to rawData from state
    if (!rawData && agentState.rawData) {
      rawData = agentState.rawData;
    }

    if (!rawData) {
      return { type: 'none', data: null, views: [] };
    }

    // Analyze data type and structure
    if (typeof rawData === 'string') {
      // Check for CSV format
      if (rawData.includes(',') && rawData.includes('\n')) {
        try {
          const lines = rawData.split('\n').filter(line => line.trim());
          if (lines.length > 1) {
            // Parse CSV
            const headers = lines[0].split(',').map(h => h.trim());
            const rows = lines.slice(1).map(line => {
              const values = line.split(',');
              const obj = {};
              headers.forEach((header, index) => {
                obj[header] = values[index]?.trim() || '';
              });
              return obj;
            });

            if (rows.length > 0) {
              dataType = 'tabular';
              rawData = rows;
              availableViews = ['table', 'chart', 'json', 'text'];
            }
          }
        } catch (e) {
          console.warn('Failed to parse CSV data:', e);
        }
      }

      // Check for JSON format
      if (dataType === 'unknown') {
        try {
          const jsonData = JSON.parse(rawData);
          if (Array.isArray(jsonData) && jsonData.length > 0) {
            dataType = 'array';
            rawData = jsonData;
            availableViews = ['table', 'json', 'text'];
            if (typeof jsonData[0] === 'object') {
              availableViews.push('chart');
            }
          } else if (typeof jsonData === 'object' && jsonData !== null) {
            dataType = 'object';
            rawData = jsonData;
            availableViews = ['json', 'text'];
          }
        } catch (e) {
          // Not JSON, keep as text
          dataType = 'text';
          availableViews = ['text'];
        }
      }
    } else if (Array.isArray(rawData)) {
      dataType = 'array';
      availableViews = ['table', 'json', 'text'];
      if (rawData.length > 0 && typeof rawData[0] === 'object') {
        availableViews.push('chart');
      }
    } else if (typeof rawData === 'object' && rawData !== null) {
      dataType = 'object';
      availableViews = ['json', 'text'];
    }

    return {
      type: dataType,
      data: rawData,
      views: availableViews,
      recordCount: Array.isArray(rawData) ? rawData.length : null
    };
  }, [agentState]);

  const [activeView, setActiveView] = React.useState(0);

  // Auto-select best view
  React.useEffect(() => {
    if (dataAnalysis.views.length > 0) {
      // Prefer table view for tabular data, otherwise first available
      const preferredView = dataAnalysis.type === 'tabular' && dataAnalysis.views.includes('table') 
        ? dataAnalysis.views.indexOf('table')
        : 0;
      setActiveView(preferredView);
    }
  }, [dataAnalysis]);

  if (dataAnalysis.type === 'none') {
    return (
      <Alert severity="info" sx={{ mt: 2 }}>
        <Typography variant="body2">
          No structured data available from this query.
        </Typography>
      </Alert>
    );
  }

  const renderView = (viewType) => {
    const { data, type } = dataAnalysis;

    switch (viewType) {
      case 'table':
        if (Array.isArray(data) && data.length > 0) {
          return (
            <InteractiveTable 
              data={data}
              title={`Data Table (${data.length} records)`}
              onFeedback={onFeedback}
            />
          );
        }
        return <Typography>No tabular data available</Typography>;

      case 'chart':
        if (Array.isArray(data) && data.length > 0 && typeof data[0] === 'object') {
          return (
            <InteractiveChart 
              data={data}
              title={`Data Visualization (${data.length} points)`}
              onFeedback={onFeedback}
            />
          );
        }
        return <Typography>No chartable data available</Typography>;

      case 'json':
        return (
          <Paper variant="outlined" sx={{ p: 2, maxHeight: 400, overflow: 'auto' }}>
            <Typography 
              component="pre" 
              variant="body2" 
              sx={{ 
                fontFamily: 'monospace',
                fontSize: '0.8rem',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word'
              }}
            >
              {JSON.stringify(data, null, 2)}
            </Typography>
          </Paper>
        );

      case 'text':
        const textData = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
        return (
          <Paper variant="outlined" sx={{ p: 2, maxHeight: 400, overflow: 'auto' }}>
            <Typography 
              variant="body2" 
              sx={{ 
                fontFamily: 'monospace',
                fontSize: '0.8rem',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word'
              }}
            >
              {textData}
            </Typography>
          </Paper>
        );

      default:
        return <Typography>Unknown view type: {viewType}</Typography>;
    }
  };

  const getViewIcon = (viewType) => {
    switch (viewType) {
      case 'table': return <TableIcon />;
      case 'chart': return <ChartIcon />;
      case 'json': return <JsonIcon />;
      case 'text': return <TextIcon />;
      default: return <DataIcon />;
    }
  };

  const getViewLabel = (viewType) => {
    switch (viewType) {
      case 'table': return 'Table';
      case 'chart': return 'Chart';
      case 'json': return 'JSON';
      case 'text': return 'Raw';
      default: return viewType;
    }
  };

  return (
    <Box sx={{ mt: 2 }}>
      {/* Data Info */}
      <Box sx={{ mb: 2, display: 'flex', gap: 1, flexWrap: 'wrap', alignItems: 'center' }}>
        <Chip 
          icon={<DataIcon />}
          label={`Type: ${dataAnalysis.type}`}
          size="small"
          color="primary"
          variant="outlined"
        />
        {dataAnalysis.recordCount && (
          <Chip 
            label={`${dataAnalysis.recordCount} records`}
            size="small"
            color="secondary"
            variant="outlined"
          />
        )}
        <Chip 
          label={`${dataAnalysis.views.length} views`}
          size="small"
          color="info"
          variant="outlined"
        />
      </Box>

      {/* View Tabs */}
      {dataAnalysis.views.length > 1 && (
        <Tabs 
          value={activeView} 
          onChange={(e, newValue) => setActiveView(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
        >
          {dataAnalysis.views.map((viewType, index) => (
            <Tab 
              key={viewType}
              icon={getViewIcon(viewType)}
              label={getViewLabel(viewType)}
              iconPosition="start"
            />
          ))}
        </Tabs>
      )}

      {/* View Content */}
      <Box>
        {dataAnalysis.views[activeView] && renderView(dataAnalysis.views[activeView])}
      </Box>
    </Box>
  );
};

export default AGUIDataStandardizer;
