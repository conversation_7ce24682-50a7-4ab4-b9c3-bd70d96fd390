/**
 * AG-UI Message Component
 * Displays messages with AG-UI protocol support
 */

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Avatar,
  Paper,
  IconButton,
  Tooltip,
  Chip,
  Collapse,
  Alert
} from '@mui/material';
import {
  SmartToy as BotIcon,
  Person as PersonIcon,
  ContentCopy as CopyIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Code as CodeIcon,
  DataObject as DataIcon,
  AccessTime as TimeIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { formatDistanceToNow } from 'date-fns';

import MarkdownRenderer from '../enhanced/MarkdownRenderer';
import UniversalResponseRenderer from '../enhanced/UniversalResponseRenderer';

/**
 * AG-UI Message Component
 */
const AGUIMessage = ({
  message,
  agentState = {},
  isStreaming = false,
  onFeedback
}) => {
  const [showMetadata, setShowMetadata] = useState(false);
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    const textToCopy = typeof message.content === 'string'
      ? message.content
      : typeof message.content === 'object'
        ? JSON.stringify(message.content, null, 2)
        : String(message.content || 'No content');

    navigator.clipboard.writeText(textToCopy).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  const renderStreamingIndicator = () => (
    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
      {[0, 1, 2].map((index) => (
        <Box
          key={index}
          sx={{
            width: 6,
            height: 6,
            borderRadius: '50%',
            backgroundColor: 'primary.main',
            animation: 'pulse 1.5s infinite',
            animationDelay: `${index * 0.2}s`,
            mr: 0.5,
            '@keyframes pulse': {
              '0%': { opacity: 0.3, transform: 'scale(1)' },
              '50%': { opacity: 1, transform: 'scale(1.2)' },
              '100%': { opacity: 0.3, transform: 'scale(1)' }
            }
          }}
        />
      ))}
      <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
        {isStreaming ? 'Streaming...' : 'Typing...'}
      </Typography>
    </Box>
  );

  const renderMessageContent = () => {
    // Ensure content is always a string
    const contentString = typeof message.content === 'string'
      ? message.content
      : typeof message.content === 'object'
        ? JSON.stringify(message.content, null, 2)
        : String(message.content || '');

    // For streaming messages, show content with cursor
    if (isStreaming) {
      return (
        <Box>
          <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
            {contentString}
            <Box
              component="span"
              sx={{
                display: 'inline-block',
                width: '2px',
                height: '1.2em',
                backgroundColor: 'primary.main',
                animation: 'blink 1s infinite',
                ml: 0.5,
                '@keyframes blink': {
                  '0%, 50%': { opacity: 1 },
                  '51%, 100%': { opacity: 0 }
                }
              }}
            />
          </Typography>
          {!contentString && renderStreamingIndicator()}
        </Box>
      );
    }

    // For completed messages, use appropriate renderer
    if (message.role === 'assistant') {
      // Check if we have structured data
      if (message.data || message.toolResults) {
        // Create a safe response object for UniversalResponseRenderer
        const safeResponse = {
          ...message,
          content: contentString, // Ensure content is always a string
          type: message.type || 'text'
        };

        return (
          <UniversalResponseRenderer
            response={safeResponse}
            onFeedback={onFeedback}
          />
        );
      } else {
        // Pure text response
        return (
          <MarkdownRenderer
            content={contentString}
            onFeedback={onFeedback}
          />
        );
      }
    } else {
      // User messages - simple text
      return (
        <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
          {contentString}
        </Typography>
      );
    }
  };

  const getMessageColor = () => {
    if (message.role === 'user') return 'primary.main';
    if (isStreaming) return 'rgba(25, 118, 210, 0.04)';
    return 'white';
  };

  const formatTimestamp = (timestamp) => {
    try {
      const date = new Date(timestamp);
      return formatDistanceToNow(date, { addSuffix: true });
    } catch (e) {
      return 'Unknown time';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start',
          mb: 3,
          position: 'relative'
        }}
      >
        {message.role === 'assistant' && (
          <Avatar
            sx={{
              mr: 2,
              bgcolor: 'primary.main',
              width: 40,
              height: 40
            }}
          >
            <BotIcon />
          </Avatar>
        )}

        <Box
          sx={{
            maxWidth: { xs: '90%', sm: '85%', md: '80%' },
            minWidth: 200,
            position: 'relative'
          }}
        >
          {/* Message Header */}
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start',
            mb: 0.5,
            gap: 1
          }}>
            <Typography variant="caption" color="text.secondary">
              {message.role === 'user' ? 'You' : 'AG-UI Assistant'}
            </Typography>

            {message.timestamp && (
              <Chip
                icon={<TimeIcon />}
                label={formatTimestamp(message.timestamp)}
                size="small"
                variant="outlined"
                sx={{ fontSize: '0.7rem', height: 20 }}
              />
            )}

            {isStreaming && (
              <Chip
                label="Streaming"
                size="small"
                color="primary"
                sx={{ fontSize: '0.7rem', height: 20 }}
              />
            )}

            {message.role === 'assistant' && !isStreaming && (
              <Tooltip title={copied ? 'Copied!' : 'Copy message'}>
                <IconButton
                  size="small"
                  onClick={handleCopy}
                  sx={{ opacity: 0.7, '&:hover': { opacity: 1 } }}
                >
                  <CopyIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </Box>

          {/* Message Content */}
          <Paper
            elevation={message.role === 'user' ? 2 : 1}
            sx={{
              p: 2,
              borderRadius: 3,
              backgroundColor: getMessageColor(),
              color: message.role === 'user' ? 'white' : 'text.primary',
              position: 'relative',
              ...(isStreaming && {
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  height: '2px',
                  width: '100%',
                  background: 'linear-gradient(90deg, transparent, primary.main, transparent)',
                  animation: 'loading 2s infinite',
                },
                '@keyframes loading': {
                  '0%': { transform: 'translateX(-100%)' },
                  '100%': { transform: 'translateX(100%)' }
                }
              })
            }}
          >
            {renderMessageContent()}
          </Paper>

          {/* Metadata */}
          {(message.metadata || message.toolResults || message.data) && !isStreaming && (
            <Box sx={{ mt: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <IconButton
                  size="small"
                  onClick={() => setShowMetadata(!showMetadata)}
                  sx={{ opacity: 0.6 }}
                >
                  {showMetadata ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
                <Typography variant="caption" color="text.secondary">
                  AG-UI Metadata
                </Typography>

                {message.toolResults && (
                  <Chip
                    icon={<CodeIcon />}
                    label={`${message.toolResults.length} tools`}
                    size="small"
                    variant="outlined"
                    sx={{ fontSize: '0.7rem' }}
                  />
                )}

                {message.data && (
                  <Chip
                    icon={<DataIcon />}
                    label="Data"
                    size="small"
                    variant="outlined"
                    sx={{ fontSize: '0.7rem' }}
                  />
                )}
              </Box>

              <Collapse in={showMetadata}>
                <Paper variant="outlined" sx={{ p: 1, mt: 1, bgcolor: 'grey.50' }}>
                  {message.metadata && (
                    <Box sx={{ mb: 1 }}>
                      <Typography variant="caption" color="text.secondary" gutterBottom>
                        Metadata:
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {Object.entries(message.metadata).map(([key, value]) => (
                          <Chip
                            key={key}
                            label={`${key}: ${value}`}
                            size="small"
                            variant="outlined"
                            sx={{ fontSize: '0.7rem' }}
                          />
                        ))}
                      </Box>
                    </Box>
                  )}

                  {message.toolResults && (
                    <Box sx={{ mb: 1 }}>
                      <Typography variant="caption" color="text.secondary" gutterBottom>
                        Tool Results:
                      </Typography>
                      {message.toolResults.map((tool, index) => (
                        <Alert key={index} severity="info" sx={{ mt: 0.5, fontSize: '0.8rem' }}>
                          <strong>{tool.name}</strong>: {tool.result || 'Executed'}
                        </Alert>
                      ))}
                    </Box>
                  )}

                  {agentState && Object.keys(agentState).length > 0 && (
                    <Box>
                      <Typography variant="caption" color="text.secondary" gutterBottom>
                        Agent State:
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {Object.entries(agentState).slice(0, 5).map(([key, value]) => (
                          <Chip
                            key={key}
                            label={`${key}: ${String(value).slice(0, 20)}${String(value).length > 20 ? '...' : ''}`}
                            size="small"
                            variant="outlined"
                            sx={{ fontSize: '0.7rem' }}
                          />
                        ))}
                      </Box>
                    </Box>
                  )}
                </Paper>
              </Collapse>
            </Box>
          )}
        </Box>

        {message.role === 'user' && (
          <Avatar sx={{ ml: 2, bgcolor: 'secondary.main', width: 40, height: 40 }}>
            <PersonIcon />
          </Avatar>
        )}
      </Box>
    </motion.div>
  );
};

export default AGUIMessage;
