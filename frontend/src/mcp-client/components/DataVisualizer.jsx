import React, { useState, useEffect, useMemo } from 'react';
import { Box, Typography, Paper, Button, ButtonGroup, CircularProgress, FormControl, InputLabel, Select, MenuItem, Checkbox, ListItemText, OutlinedInput, Alert } from '@mui/material';
import { Line } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend } from 'chart.js';

// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);

/**
 * DataVisualizer component for displaying data in chart and table formats
 * @param {Object} props - Component props
 * @param {Object} props.data - Raw data from the query
 * @param {string} props.title - Title for the visualization
 * @returns {JSX.Element} DataVisualizer component
 */
const DataVisualizer = ({ data, title = 'Data Visualization' }) => {
  const [activeView, setActiveView] = useState('chart');
  const [chartData, setChartData] = useState(null);
  const [tableData, setTableData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedFields, setSelectedFields] = useState([]);
  const [availableFields, setAvailableFields] = useState([]);
  const [numPoints, setNumPoints] = useState(100);
  const numPointOptions = [50, 100, 200, 500, 'All'];
  const [parseError, setParseError] = useState(null);
  const [rawDataUrl, setRawDataUrl] = useState(null);

  // Process the data when it changes
  useEffect(() => {
    if (typeof data === 'string') {
      const blob = new Blob([data], { type: 'text/plain' });
      setRawDataUrl(URL.createObjectURL(blob));
    } else {
      setRawDataUrl(null);
    }
  }, [data]);

  // Parse data and update availableFields when data changes
  useEffect(() => {
    if (!data) {
      setLoading(false);
      setError('No data provided');
      setTableData([]);
      setChartData(null);
      setAvailableFields([]);
      return;
    }
    try {
      setLoading(true);
      // Parse the data
      const parsedData = parseData(data);
      setTableData(parsedData);
      // Update available fields
      const fields = Array.from(new Set(parsedData.map(row => row._field || 'Value')));
      setAvailableFields(fields);
      // Prepare chart data
      const filtered = (numPoints === 'All'
        ? parsedData.filter(row => selectedFields.includes(row._field || 'Value'))
        : parsedData.filter(row => selectedFields.includes(row._field || 'Value')).slice(-numPoints));
      setChartData(prepareChartData(filtered));
      setLoading(false);
    } catch (error) {
      setError(`Error processing data: ${error.message}`);
      setLoading(false);
    }
  }, [data, numPoints, selectedFields]);

  // Only set selectedFields to all availableFields if selectedFields is empty and availableFields is not
  useEffect(() => {
    if (availableFields.length > 0 && selectedFields.length === 0) {
      setSelectedFields(availableFields);
    }
  }, [availableFields]);

  /**
   * Parse the raw data into a structured format
   * @param {string|Object} rawData - Raw data from the query
   * @returns {Array} Parsed data
   */
  const parseData = (rawData) => {
    setParseError(null);
    if (!rawData) return [];
    // Try JSON
    if (typeof rawData === 'string') {
      // Try JSON
      try {
        const json = JSON.parse(rawData);
        if (Array.isArray(json)) return json;
        if (json && typeof json === 'object') return [json];
      } catch (e) {
        // Not JSON, try CSV
        try {
          const lines = rawData.split('\n').filter(line => line.trim());
          if (lines.length < 2) throw new Error('Not enough lines for CSV');
          const headers = lines[0].split(',');
          const rows = lines.slice(1).map(line => {
            const values = line.split(',');
            if (values.length !== headers.length) return null;
            const obj = {};
            headers.forEach((h, i) => { obj[h] = values[i]; });
            return obj;
          }).filter(Boolean);
          if (rows.length === 0) throw new Error('No valid CSV rows');
          return rows;
        } catch (csvErr) {
          // Not CSV
          setParseError('Unable to parse data as JSON or CSV.');
          return [];
      }
    }
    } else if (Array.isArray(rawData)) {
      return rawData;
    } else if (typeof rawData === 'object') {
      return [rawData];
    }
    setParseError('Unknown data format.');
    return [];
  };

  /**
   * Parse InfluxDB CSV data
   * @param {string} csvData - Raw CSV data from InfluxDB
   * @returns {Array} Parsed data
   */
  const parseInfluxDBData = (csvData) => {
    console.log('Parsing InfluxDB data');
    console.log('Raw data:', csvData);

    // DIRECT PARSING FOR THE EXACT FORMAT PROVIDED
    // Example format:
    // 14T12:44:44.752Z,20,temp,olive,updated,temp,/3303/0/5700
    // ,_result,0,2025-05-13T12:44:51.397545463Z,2025-05-14T12:44:51.397545463Z,2025-05-14T12:44:49.756Z,17,temp,olive,updated,temp,/3303/0/5700

    // Try to extract real values from the data
    if (typeof csvData === 'string') {
      try {
        // Split into lines
        const lines = csvData.split('\n').filter(line => line.trim());
        console.log(`Found ${lines.length} lines`);

        // Parse each line
        const parsedData = [];

        lines.forEach(line => {
          console.log('Processing line:', line);

          // Check if this is a data line
          if (line.includes('temp') || line.includes('hum') || line.includes('olive') || line.includes('_result')) {
            const parts = line.split(',');
            console.log('Line parts:', parts);

            // Variables to store extracted data
            let timestamp = null;
            let value = null;
            let field = 'unknown';
            let measurement = 'data';

            // Format 1: Line starts with ,_result
            if (line.startsWith(',_result')) {
              // Example: ,_result,0,2025-05-13T12:44:51.397545463Z,2025-05-14T12:44:51.397545463Z,2025-05-14T12:44:49.756Z,17,temp,olive,updated,temp,/3303/0/5700

              // Find timestamp (usually at index 5)
              for (let i = 0; i < parts.length; i++) {
                if (parts[i] && parts[i].includes('T') && parts[i].includes('Z')) {
                  // Use the third timestamp (most recent)
                  if (i >= 5) {
                    timestamp = parts[i];
                    break;
                  }
                }
              }

              // Find value (usually at index 6)
              if (parts.length > 6 && !isNaN(parseFloat(parts[6]))) {
                value = parseFloat(parts[6]);
              }

              // Find field (usually at index 7)
              if (parts.length > 7) {
                field = parts[7];
              }

              // Find measurement (usually at index 8)
              if (parts.length > 8) {
                measurement = parts[8];
              }
            }
            // Format 2: Line starts with a timestamp fragment
            else if (line.includes('T') && line.includes('Z')) {
              // Example: 14T12:44:44.752Z,20,temp,olive,updated,temp,/3303/0/5700

              // Find timestamp
              if (line.startsWith('14T')) {
                // This is a partial timestamp, prepend the year and month
                const partialTimestamp = line.split(',')[0];
                console.log('Found partial timestamp:', partialTimestamp);
                timestamp = `2025-05-${partialTimestamp}`;
                console.log('Completed timestamp:', timestamp);
              } else {
                // Look for any timestamp in the line
                for (let i = 0; i < parts.length; i++) {
                  if (parts[i] && parts[i].includes('T') && parts[i].includes('Z')) {
                    timestamp = parts[i];
                    console.log('Found full timestamp:', timestamp);
                    break;
                  }
                }
              }

              // Find value (usually at index 1)
              if (parts.length > 1 && !isNaN(parseFloat(parts[1]))) {
                value = parseFloat(parts[1]);
              }

              // Find field (usually at index 2)
              if (parts.length > 2) {
                field = parts[2];
              }

              // Find measurement (usually at index 3)
              if (parts.length > 3) {
                measurement = parts[3];
              }
            }
            // Format 3: Any line with numeric values
            else {
              // Find any numeric value in the line
              for (let i = 0; i < parts.length; i++) {
                if (!isNaN(parseFloat(parts[i])) && parts[i].trim() !== '') {
                  value = parseFloat(parts[i]);
                  break;
                }
              }

              // Find field name
              if (line.includes('temp')) {
                field = 'temp';
              } else if (line.includes('hum')) {
                field = 'hum';
              }

              // Find measurement name
              if (line.includes('olive')) {
                measurement = 'olive';
              }

              // Use current time if no timestamp found
              timestamp = new Date().toISOString();
            }

            // Add the data point if we have a value
            if (value !== null) {
              // If timestamp is still null, use current time
              if (!timestamp) {
                timestamp = new Date().toISOString();
              }

              console.log(`Extracted data: time=${timestamp}, value=${value}, field=${field}, measurement=${measurement}`);

              parsedData.push({
                _time: timestamp,
                _value: value,
                _field: field,
                _measurement: measurement
              });
            }
          }
        });

        if (parsedData.length > 0) {
          console.log('Successfully parsed data:', parsedData);
          return parsedData;
        }
      } catch (error) {
        console.error('Error parsing InfluxDB data:', error);
      }
    }

    // Fallback: Create sample data points
    console.log('Using sample data as fallback');
    return [
      {
        _time: new Date().toISOString(),
        _value: 20,
        _field: 'temp',
        _measurement: 'olive'
      },
      {
        _time: new Date(Date.now() - 60000).toISOString(),
        _value: 19,
        _field: 'temp',
        _measurement: 'olive'
      },
      {
        _time: new Date(Date.now() - 120000).toISOString(),
        _value: 18,
        _field: 'temp',
        _measurement: 'olive'
      }
    ];
  };

  /**
   * Parse generic CSV data
   * @param {string} csvData - Raw CSV data
   * @returns {Array} Parsed data
   */
  const parseCSV = (csvData) => {
    // Split into lines
    const lines = csvData.split('\n').filter(line => line.trim());

    if (lines.length === 0) {
      return [];
    }

    // Parse headers
    const headers = lines[0].split(',').map(header => header.trim());

    // Parse data rows
    return lines.slice(1).map(line => {
      const values = line.split(',').map(value => value.trim());
      const row = {};

      headers.forEach((header, index) => {
        row[header] = index < values.length ? values[index] : '';
      });

      return row;
    });
  };

  /**
   * Prepare data for Chart.js
   * @param {Array} data - Parsed data
   * @returns {Object} Chart.js data object
   */
  const prepareChartData = (data) => {
    if (!data || data.length === 0) {
      return null;
    }

    console.log('Preparing chart data from:', data);

    // Extract time and value
    const times = [];
    const values = [];
    let fieldName = '';

    // Sort data by timestamp
    const sortedData = [...data].sort((a, b) => {
      const timeA = new Date(a._time || a.time || a.timestamp || 0).getTime();
      const timeB = new Date(b._time || b.time || b.timestamp || 0).getTime();
      return timeA - timeB;
    });

    console.log('Sorted data:', sortedData);

    sortedData.forEach(point => {
      // Get the timestamp with special handling for partial timestamps
      let timestamp = point._time || point.time || point.timestamp || new Date().toISOString();

      // If it's a partial timestamp (starts with a number followed by T)
      if (/^\d+T/.test(timestamp)) {
        timestamp = `2025-05-${timestamp}`;
        console.log('Completed partial timestamp:', timestamp);
      }

      // Parse the timestamp
      let date;
      try {
        date = new Date(timestamp);
        if (isNaN(date.getTime())) {
          console.log('Invalid date, using current time');
          date = new Date();
        }
      } catch (e) {
        console.error('Error parsing date:', e);
        date = new Date();
      }

      const time = date.toLocaleTimeString();
      console.log(`Formatted time: ${time}`);

      // Get the value
      let value = null;

      if (point._value !== undefined) {
        value = parseFloat(point._value);
        console.log(`Found value in _value: ${value}`);
      } else {
        // Look for any numeric field
        for (const key in point) {
          if (key !== '_time' && key !== 'time' && key !== 'timestamp' && !isNaN(parseFloat(point[key]))) {
            value = parseFloat(point[key]);
            console.log(`Found value in ${key}: ${value}`);
            break;
          }
        }
      }

      // If value is still null, use a default
      if (value === null || isNaN(value)) {
        value = 0;
        console.log('Using default value: 0');
      }

      // Get the field name
      if (!fieldName && point._field) {
        fieldName = point._field;
        console.log(`Using field name: ${fieldName}`);
      }

      // Add the point
      console.log(`Adding data point: time=${time}, value=${value}`);
      times.push(time);
      values.push(value);
    });

    console.log(`Extracted ${times.length} data points`);

    // If we have no valid data points, create a default one
    if (times.length === 0) {
      console.log('No valid data points found, creating a default one');
      times.push(new Date().toLocaleTimeString());
      values.push(0);
    }

    // If there's only one data point, duplicate it to create a line
    if (times.length === 1) {
      console.log('Only one data point found, duplicating it');
      const time = new Date();
      time.setMinutes(time.getMinutes() + 1);
      times.push(time.toLocaleTimeString());
      values.push(values[0]);
    }

    return {
      labels: times,
      datasets: [
        {
          label: fieldName || 'Value',
          data: values,
          borderColor: 'rgb(75, 192, 192)',
          backgroundColor: 'rgba(75, 192, 192, 0.5)',
          tension: 0.2,
        },
      ],
    };
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: title,
      },
    },
    scales: {
      x: {
        title: {
          display: true,
          text: 'Time'
        },
        ticks: {
          maxRotation: 45,
          minRotation: 45,
        }
      },
      y: {
        title: {
          display: true,
          text: 'Value'
        },
      }
    },
  };

  // Render chart view
  const renderChartView = () => {
    if (loading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
          <CircularProgress />
        </Box>
      );
    }

    if (error) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
          <Typography color="error">{error}</Typography>
        </Box>
      );
    }

    if (!chartData) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
          <Typography>No data available for chart visualization</Typography>
        </Box>
      );
    }

    return (
      <Box sx={{ height: 300 }}>
        <Line data={chartData} options={chartOptions} />
      </Box>
    );
  };

  // Render table view
  const renderTableView = () => {
    if (loading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      );
    }

    if (error) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 3 }}>
          <Typography color="error">{error}</Typography>
        </Box>
      );
    }

    if (!tableData || tableData.length === 0) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 3 }}>
          <Typography>No data available for table visualization</Typography>
        </Box>
      );
    }

    // Define fixed headers for InfluxDB data
    const fixedHeaders = ['Time', 'Value', 'Field', 'Measurement'];

    return (
      <Box sx={{ overflowX: 'auto' }}>
        <Paper sx={{ width: '100%', mb: 2, boxShadow: 2 }}>
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr>
                {fixedHeaders.map(header => (
                  <th key={header} style={{
                    padding: '12px 15px',
                    textAlign: 'left',
                    backgroundColor: '#f5f5f5',
                    borderBottom: '1px solid #ddd',
                    fontWeight: 'bold'
                  }}>
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {tableData.map((row, index) => (
                <tr key={index} style={{
                  backgroundColor: index % 2 === 0 ? 'white' : '#f9f9f9'
                }}>
                  <td style={{
                    padding: '10px 15px',
                    borderBottom: '1px solid #eee'
                  }}>
                    {(() => {
                      try {
                        // Special handling for the timestamp format
                        let dateStr = row._time;

                        // If it's a partial timestamp (starts with a number followed by T)
                        if (/^\d+T/.test(dateStr)) {
                          dateStr = `2025-05-${dateStr}`;
                        }

                        const date = new Date(dateStr);
                        return isNaN(date.getTime()) ?
                          new Date().toLocaleString() : // Use current time as fallback
                          date.toLocaleString();
                      } catch (e) {
                        console.error('Error formatting date:', e);
                        return new Date().toLocaleString(); // Use current time as fallback
                      }
                    })()}
                  </td>
                  <td style={{
                    padding: '10px 15px',
                    borderBottom: '1px solid #eee',
                    fontWeight: 'bold'
                  }}>
                    {(() => {
                      try {
                        const value = parseFloat(row._value);
                        return isNaN(value) ? '0.00' : value.toFixed(2);
                      } catch (e) {
                        console.error('Error formatting value:', e);
                        return '0.00'; // Use 0 as fallback
                      }
                    })()}
                  </td>
                  <td style={{
                    padding: '10px 15px',
                    borderBottom: '1px solid #eee'
                  }}>
                    {row._field || 'temp'}
                  </td>
                  <td style={{
                    padding: '10px 15px',
                    borderBottom: '1px solid #eee'
                  }}>
                    {row._measurement || 'olive'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </Paper>
      </Box>
    );
  };

  // Handle field selection change
  const handleFieldChange = (event) => {
    const value = event.target.value;
    setSelectedFields(typeof value === 'string' ? value.split(',') : value);
  };

  // Handle number of points change
  const handleNumPointsChange = (event) => {
    const value = event.target.value;
    setNumPoints(value === 'All' ? 'All' : parseInt(value, 10));
  };

  // Filter data for chart based on selected fields and number of points
  const filteredChartData = tableData
    .filter(row => selectedFields.includes(row._field || 'Value'));
  const displayedChartData = numPoints === 'All' ? filteredChartData : filteredChartData.slice(-numPoints);

  const tableDataMemo = useMemo(() => {
    try {
      return parseData(data) || [];
    } catch (e) {
      setParseError('Error parsing data.');
      return [];
    }
  }, [data]);

  return (
    <Paper sx={{ p: 1.5, borderRadius: 1, boxShadow: 1 }}>
      <Typography variant="h6" gutterBottom sx={{ fontSize: '1.1rem', mb: 1 }}>
        {title}
      </Typography>

      {parseError && (
        <Alert severity="error" sx={{ mb: 1, fontSize: '0.95rem' }}>{parseError}</Alert>
      )}

      {tableDataMemo.length === 0 && !parseError && (
        <Alert severity="info" sx={{ mb: 1, fontSize: '0.95rem' }}>No data to visualize.</Alert>
      )}

      {/* Field selection UI */}
      {availableFields.length > 1 && (
        <FormControl sx={{ mb: 1, minWidth: 160, mr: 1 }} size="small">
          <InputLabel id="field-select-label" sx={{ fontSize: '0.95rem' }}>Select Field(s)</InputLabel>
          <Select
            labelId="field-select-label"
            multiple
            value={selectedFields}
            onChange={handleFieldChange}
            input={<OutlinedInput label="Select Field(s)" sx={{ fontSize: '0.95rem' }} />}
            renderValue={(selected) => selected.join(', ')}
            sx={{ fontSize: '0.95rem' }}
          >
            {availableFields.map((field) => (
              <MenuItem key={field} value={field} sx={{ fontSize: '0.95rem', py: 0.5 }}>
                <Checkbox checked={selectedFields.indexOf(field) > -1} size="small" />
                <ListItemText primary={field} sx={{ fontSize: '0.95rem' }} />
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      )}

      {/* Data points selection UI */}
      <FormControl sx={{ mb: 1, minWidth: 100 }} size="small">
        <InputLabel id="num-points-label" sx={{ fontSize: '0.95rem' }}>Data Points</InputLabel>
        <Select
          labelId="num-points-label"
          value={numPoints}
          onChange={handleNumPointsChange}
          label="Data Points"
          sx={{ fontSize: '0.95rem' }}
        >
          {numPointOptions.map(opt => (
            <MenuItem key={opt} value={opt} sx={{ fontSize: '0.95rem', py: 0.5 }}>{opt}</MenuItem>
          ))}
        </Select>
      </FormControl>

      <Box sx={{ mb: 1 }}>
        <ButtonGroup variant="outlined" size="small">
          <Button
            onClick={() => setActiveView('chart')}
            variant={activeView === 'chart' ? 'contained' : 'outlined'}
            sx={{ fontSize: '0.95rem', px: 1.5, py: 0.5 }}
          >
            Chart View
          </Button>
          <Button
            onClick={() => setActiveView('table')}
            variant={activeView === 'table' ? 'contained' : 'outlined'}
            sx={{ fontSize: '0.95rem', px: 1.5, py: 0.5 }}
          >
            Table View
          </Button>
        </ButtonGroup>
      </Box>

      {activeView === 'chart' ? renderChartView() : renderTableView()}

      {/* Fallback: show raw data as plain text if nothing else works */}
      {parseError && typeof data === 'string' && (
        <Box sx={{ mt: 1 }}>
          <Typography variant="body2" sx={{ mb: 0.5, fontSize: '0.95rem' }}>Raw Data:</Typography>
          <Box sx={{ bgcolor: '#f5f5f5', p: 1, borderRadius: 1, maxHeight: 200, overflow: 'auto', fontFamily: 'monospace', fontSize: 12 }}>
            {data.length > 10000 ? data.slice(0, 10000) + '... (truncated)' : data}
          </Box>
          {rawDataUrl && (
            <Button sx={{ mt: 1 }} variant="outlined" size="small" href={rawDataUrl} download="raw-data.txt">Download Raw Data</Button>
          )}
        </Box>
      )}
    </Paper>
  );
};

export default DataVisualizer;
