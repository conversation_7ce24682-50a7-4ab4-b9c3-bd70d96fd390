import React, { useState, useMemo } from 'react';
import {
  Box,
  Paper,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Grid,
  Switch,
  FormControlLabel,
  Slider,
  Alert,
  Tooltip,
  IconButton
} from '@mui/material';
import {
  TuneRounded as TuneIcon,
  InfoOutlined as InfoIcon
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  ScatterChart,
  Scatter,
  Pie<PERSON>hart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { format, parseISO, isValid } from 'date-fns';

/**
 * Advanced Data Visualizer with multiple chart types and interactive controls
 */
const AdvancedDataVisualizer = ({ data, meta, onFeedback }) => {
  const [chartType, setChartType] = useState('line');
  const [xField, setXField] = useState('');
  const [yFields, setYFields] = useState([]);
  const [showGrid, setShowGrid] = useState(true);
  const [showLegend, setShowLegend] = useState(true);
  const [maxDataPoints, setMaxDataPoints] = useState(100);
  const [showControls, setShowControls] = useState(false);

  // Analyze data structure and extract field information
  const dataAnalysis = useMemo(() => {
    if (!Array.isArray(data) || data.length === 0) {
      return { fields: [], numericFields: [], dateFields: [], processedData: [] };
    }

    const firstRow = data[0];
    const fields = Object.keys(firstRow);
    const numericFields = [];
    const dateFields = [];
    const textFields = [];

    // Analyze field types
    fields.forEach(field => {
      const sampleValues = data.slice(0, 10).map(row => row[field]).filter(val => val != null);
      
      if (sampleValues.length === 0) return;

      // Check if it's a date field
      const isDateField = sampleValues.every(val => {
        if (typeof val === 'string') {
          const date = parseISO(val);
          return isValid(date) || !isNaN(Date.parse(val));
        }
        return false;
      });

      if (isDateField) {
        dateFields.push(field);
      } else {
        // Check if it's numeric
        const isNumeric = sampleValues.every(val => {
          const num = parseFloat(val);
          return !isNaN(num) && isFinite(num);
        });

        if (isNumeric) {
          numericFields.push(field);
        } else {
          textFields.push(field);
        }
      }
    });

    // Process data for visualization
    const processedData = data.slice(0, maxDataPoints).map((row, index) => {
      const processedRow = { ...row, _index: index };
      
      // Convert date fields
      dateFields.forEach(field => {
        if (row[field]) {
          try {
            const date = parseISO(row[field]) || new Date(row[field]);
            processedRow[field] = date.getTime();
            processedRow[`${field}_formatted`] = format(date, 'MMM dd, HH:mm');
          } catch (e) {
            processedRow[field] = row[field];
          }
        }
      });

      // Convert numeric fields
      numericFields.forEach(field => {
        if (row[field] != null) {
          processedRow[field] = parseFloat(row[field]) || 0;
        }
      });

      return processedRow;
    });

    return {
      fields,
      numericFields,
      dateFields,
      textFields,
      processedData,
      totalRows: data.length
    };
  }, [data, maxDataPoints]);

  // Auto-select appropriate fields
  React.useEffect(() => {
    if (dataAnalysis.fields.length > 0) {
      // Auto-select X field (prefer date/time fields, then first field)
      const autoXField = dataAnalysis.dateFields[0] || 
                        dataAnalysis.fields.find(f => f.toLowerCase().includes('time')) ||
                        dataAnalysis.fields.find(f => f.toLowerCase().includes('date')) ||
                        dataAnalysis.fields[0];
      
      if (!xField) {
        setXField(autoXField);
      }

      // Auto-select Y fields (prefer numeric fields)
      if (yFields.length === 0) {
        const autoYFields = dataAnalysis.numericFields.slice(0, 3);
        if (autoYFields.length > 0) {
          setYFields(autoYFields);
        } else {
          // If no numeric fields, use first non-x field
          const fallbackField = dataAnalysis.fields.find(f => f !== autoXField);
          if (fallbackField) {
            setYFields([fallbackField]);
          }
        }
      }
    }
  }, [dataAnalysis, xField, yFields.length]);

  const chartColors = [
    '#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00',
    '#ff00ff', '#00ffff', '#ff0000', '#0000ff', '#ffff00'
  ];

  const formatXAxisTick = (value) => {
    const field = xField;
    if (dataAnalysis.dateFields.includes(field)) {
      return format(new Date(value), 'MMM dd');
    }
    return value;
  };

  const renderChart = () => {
    if (!dataAnalysis.processedData.length || !xField || !yFields.length) {
      return (
        <Alert severity="info" sx={{ m: 2 }}>
          No data available for visualization. Please check your data structure.
        </Alert>
      );
    }

    const commonProps = {
      data: dataAnalysis.processedData,
      margin: { top: 20, right: 30, left: 20, bottom: 20 }
    };

    switch (chartType) {
      case 'line':
        return (
          <LineChart {...commonProps}>
            {showGrid && <CartesianGrid strokeDasharray="3 3" />}
            <XAxis 
              dataKey={xField} 
              tickFormatter={formatXAxisTick}
              type={dataAnalysis.dateFields.includes(xField) ? 'number' : 'category'}
              scale={dataAnalysis.dateFields.includes(xField) ? 'time' : 'auto'}
              domain={dataAnalysis.dateFields.includes(xField) ? ['dataMin', 'dataMax'] : undefined}
            />
            <YAxis />
            <RechartsTooltip 
              labelFormatter={(value) => {
                if (dataAnalysis.dateFields.includes(xField)) {
                  return format(new Date(value), 'MMM dd, yyyy HH:mm');
                }
                return value;
              }}
            />
            {showLegend && <Legend />}
            {yFields.map((field, index) => (
              <Line
                key={field}
                type="monotone"
                dataKey={field}
                stroke={chartColors[index % chartColors.length]}
                strokeWidth={2}
                dot={{ r: 3 }}
                activeDot={{ r: 5 }}
              />
            ))}
          </LineChart>
        );

      case 'area':
        return (
          <AreaChart {...commonProps}>
            {showGrid && <CartesianGrid strokeDasharray="3 3" />}
            <XAxis dataKey={xField} tickFormatter={formatXAxisTick} />
            <YAxis />
            <RechartsTooltip />
            {showLegend && <Legend />}
            {yFields.map((field, index) => (
              <Area
                key={field}
                type="monotone"
                dataKey={field}
                stackId="1"
                stroke={chartColors[index % chartColors.length]}
                fill={chartColors[index % chartColors.length]}
                fillOpacity={0.6}
              />
            ))}
          </AreaChart>
        );

      case 'bar':
        return (
          <BarChart {...commonProps}>
            {showGrid && <CartesianGrid strokeDasharray="3 3" />}
            <XAxis dataKey={xField} />
            <YAxis />
            <RechartsTooltip />
            {showLegend && <Legend />}
            {yFields.map((field, index) => (
              <Bar
                key={field}
                dataKey={field}
                fill={chartColors[index % chartColors.length]}
              />
            ))}
          </BarChart>
        );

      case 'scatter':
        return (
          <ScatterChart {...commonProps}>
            {showGrid && <CartesianGrid strokeDasharray="3 3" />}
            <XAxis dataKey={xField} type="number" />
            <YAxis dataKey={yFields[0]} type="number" />
            <RechartsTooltip cursor={{ strokeDasharray: '3 3' }} />
            <Scatter
              dataKey={yFields[0]}
              fill={chartColors[0]}
            />
          </ScatterChart>
        );

      case 'pie':
        const pieData = dataAnalysis.processedData.slice(0, 10).map((item, index) => ({
          name: item[xField] || `Item ${index + 1}`,
          value: parseFloat(item[yFields[0]]) || 0
        }));

        return (
          <PieChart {...commonProps}>
            <Pie
              data={pieData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {pieData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={chartColors[index % chartColors.length]} />
              ))}
            </Pie>
            <RechartsTooltip />
          </PieChart>
        );

      default:
        return <Alert severity="error">Unknown chart type: {chartType}</Alert>;
    }
  };

  return (
    <Box sx={{ p: 2 }}>
      {/* Header with controls toggle */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Data Visualization
          <Chip 
            label={`${dataAnalysis.processedData.length} of ${dataAnalysis.totalRows} rows`}
            size="small"
            sx={{ ml: 1 }}
          />
        </Typography>
        <Tooltip title="Chart Controls">
          <IconButton onClick={() => setShowControls(!showControls)}>
            <TuneIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Controls Panel */}
      {showControls && (
        <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Chart Type</InputLabel>
                <Select
                  value={chartType}
                  label="Chart Type"
                  onChange={(e) => setChartType(e.target.value)}
                >
                  <MenuItem value="line">Line Chart</MenuItem>
                  <MenuItem value="area">Area Chart</MenuItem>
                  <MenuItem value="bar">Bar Chart</MenuItem>
                  <MenuItem value="scatter">Scatter Plot</MenuItem>
                  <MenuItem value="pie">Pie Chart</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>X-Axis Field</InputLabel>
                <Select
                  value={xField}
                  label="X-Axis Field"
                  onChange={(e) => setXField(e.target.value)}
                >
                  {dataAnalysis.fields.map(field => (
                    <MenuItem key={field} value={field}>{field}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Y-Axis Fields</InputLabel>
                <Select
                  multiple
                  value={yFields}
                  label="Y-Axis Fields"
                  onChange={(e) => setYFields(e.target.value)}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip key={value} label={value} size="small" />
                      ))}
                    </Box>
                  )}
                >
                  {dataAnalysis.fields.map(field => (
                    <MenuItem key={field} value={field}>{field}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Typography gutterBottom>Max Data Points: {maxDataPoints}</Typography>
              <Slider
                value={maxDataPoints}
                onChange={(e, value) => setMaxDataPoints(value)}
                min={10}
                max={1000}
                step={10}
                size="small"
              />
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={<Switch checked={showGrid} onChange={(e) => setShowGrid(e.target.checked)} />}
                label="Show Grid"
              />
              <FormControlLabel
                control={<Switch checked={showLegend} onChange={(e) => setShowLegend(e.target.checked)} />}
                label="Show Legend"
                sx={{ ml: 2 }}
              />
            </Grid>
          </Grid>
        </Paper>
      )}

      {/* Chart */}
      <Paper variant="outlined" sx={{ height: 400, p: 1 }}>
        <ResponsiveContainer width="100%" height="100%">
          {renderChart()}
        </ResponsiveContainer>
      </Paper>

      {/* Data Summary */}
      <Box sx={{ mt: 2, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
        <Chip 
          icon={<InfoIcon />}
          label={`${dataAnalysis.fields.length} fields`}
          variant="outlined"
          size="small"
        />
        <Chip 
          label={`${dataAnalysis.numericFields.length} numeric`}
          variant="outlined"
          size="small"
        />
        <Chip 
          label={`${dataAnalysis.dateFields.length} date/time`}
          variant="outlined"
          size="small"
        />
      </Box>
    </Box>
  );
};

export default AdvancedDataVisualizer;
