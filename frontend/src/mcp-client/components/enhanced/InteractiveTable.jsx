import React, { useState, useMemo } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  IconButton,
  Tooltip,
  Chip,
  Menu,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Button,
  Alert
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Download as DownloadIcon,
  Sort as SortIcon,
  ViewColumn as ViewColumnIcon
} from '@mui/icons-material';
import { DataGrid } from '@mui/x-data-grid';
import { format, parseISO, isValid } from 'date-fns';

/**
 * Interactive Table with search, filter, sort, and export capabilities
 */
const InteractiveTable = ({ data, onFeedback }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortModel, setSortModel] = useState([]);
  const [filterModel, setFilterModel] = useState({ items: [] });
  const [columnVisibility, setColumnVisibility] = useState({});
  const [pageSize, setPageSize] = useState(25);
  const [filterMenuAnchor, setFilterMenuAnchor] = useState(null);
  const [selectedColumn, setSelectedColumn] = useState('');

  // Process data and create columns
  const { processedData, columns } = useMemo(() => {
    if (!Array.isArray(data) || data.length === 0) {
      return { processedData: [], columns: [] };
    }

    // Analyze data structure
    const firstRow = data[0];
    const fieldNames = Object.keys(firstRow);
    
    // Create columns with appropriate types
    const cols = fieldNames.map(field => {
      const sampleValues = data.slice(0, 10).map(row => row[field]).filter(val => val != null);
      
      let type = 'string';
      let valueFormatter = undefined;
      
      // Determine column type
      if (sampleValues.length > 0) {
        // Check if it's a date
        const isDate = sampleValues.every(val => {
          if (typeof val === 'string') {
            const date = parseISO(val);
            return isValid(date) || !isNaN(Date.parse(val));
          }
          return false;
        });

        if (isDate) {
          type = 'dateTime';
          valueFormatter = (params) => {
            try {
              const date = parseISO(params.value) || new Date(params.value);
              return format(date, 'MMM dd, yyyy HH:mm');
            } catch (e) {
              return params.value;
            }
          };
        } else {
          // Check if it's numeric
          const isNumeric = sampleValues.every(val => {
            const num = parseFloat(val);
            return !isNaN(num) && isFinite(num);
          });

          if (isNumeric) {
            type = 'number';
            valueFormatter = (params) => {
              const num = parseFloat(params.value);
              if (isNaN(num)) return params.value;
              
              // Format based on the number's characteristics
              if (num % 1 === 0) {
                return num.toLocaleString(); // Integer
              } else {
                return num.toFixed(2); // Decimal
              }
            };
          }
        }
      }

      return {
        field,
        headerName: field.charAt(0).toUpperCase() + field.slice(1).replace(/_/g, ' '),
        type,
        valueFormatter,
        flex: 1,
        minWidth: 120,
        sortable: true,
        filterable: true,
        hideable: true
      };
    });

    // Process data with IDs
    const processedRows = data.map((row, index) => ({
      id: index,
      ...row
    }));

    return { processedData: processedRows, columns: cols };
  }, [data]);

  // Filter data based on search term
  const filteredData = useMemo(() => {
    if (!searchTerm.trim()) return processedData;
    
    return processedData.filter(row =>
      Object.values(row).some(value =>
        String(value).toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  }, [processedData, searchTerm]);

  const handleExport = () => {
    // Convert data to CSV
    if (columns.length === 0) return;
    
    const headers = columns.map(col => col.headerName).join(',');
    const rows = filteredData.map(row =>
      columns.map(col => {
        const value = row[col.field];
        // Escape commas and quotes in CSV
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      }).join(',')
    );
    
    const csvContent = [headers, ...rows].join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `table-data-${Date.now()}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleColumnVisibilityChange = (field) => {
    setColumnVisibility(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const getColumnStats = (field) => {
    const values = filteredData.map(row => row[field]).filter(val => val != null);
    const column = columns.find(col => col.field === field);
    
    if (column?.type === 'number') {
      const numbers = values.map(v => parseFloat(v)).filter(n => !isNaN(n));
      if (numbers.length > 0) {
        return {
          count: numbers.length,
          min: Math.min(...numbers),
          max: Math.max(...numbers),
          avg: numbers.reduce((a, b) => a + b, 0) / numbers.length
        };
      }
    }
    
    return {
      count: values.length,
      unique: new Set(values).size
    };
  };

  if (!data || data.length === 0) {
    return (
      <Alert severity="info" sx={{ m: 2 }}>
        No data available for table view.
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 2 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Data Table
          <Chip 
            label={`${filteredData.length} rows`}
            size="small"
            sx={{ ml: 1 }}
          />
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="Export CSV">
            <IconButton onClick={handleExport} size="small">
              <DownloadIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Column Visibility">
            <IconButton 
              onClick={(e) => setFilterMenuAnchor(e.currentTarget)}
              size="small"
            >
              <ViewColumnIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Search and Controls */}
      <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
        <TextField
          placeholder="Search all columns..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          size="small"
          InputProps={{
            startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
          }}
          sx={{ minWidth: 250 }}
        />
        
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Rows per page</InputLabel>
          <Select
            value={pageSize}
            label="Rows per page"
            onChange={(e) => setPageSize(e.target.value)}
          >
            <MenuItem value={10}>10</MenuItem>
            <MenuItem value={25}>25</MenuItem>
            <MenuItem value={50}>50</MenuItem>
            <MenuItem value={100}>100</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Data Grid */}
      <Paper variant="outlined" sx={{ height: 600 }}>
        <DataGrid
          rows={filteredData}
          columns={columns}
          pageSize={pageSize}
          rowsPerPageOptions={[10, 25, 50, 100]}
          onPageSizeChange={setPageSize}
          sortModel={sortModel}
          onSortModelChange={setSortModel}
          filterModel={filterModel}
          onFilterModelChange={setFilterModel}
          columnVisibilityModel={columnVisibility}
          onColumnVisibilityModelChange={setColumnVisibility}
          disableSelectionOnClick
          density="compact"
          sx={{
            border: 'none',
            '& .MuiDataGrid-cell': {
              borderBottom: '1px solid rgba(224, 224, 224, 1)',
            },
            '& .MuiDataGrid-columnHeaders': {
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
              borderBottom: '2px solid rgba(224, 224, 224, 1)',
            },
            '& .MuiDataGrid-footerContainer': {
              borderTop: '2px solid rgba(224, 224, 224, 1)',
            }
          }}
        />
      </Paper>

      {/* Column Visibility Menu */}
      <Menu
        anchorEl={filterMenuAnchor}
        open={Boolean(filterMenuAnchor)}
        onClose={() => setFilterMenuAnchor(null)}
        PaperProps={{
          sx: { maxHeight: 300, width: 250 }
        }}
      >
        <MenuItem disabled>
          <Typography variant="subtitle2">Show/Hide Columns</Typography>
        </MenuItem>
        {columns.map(column => (
          <MenuItem
            key={column.field}
            onClick={() => handleColumnVisibilityChange(column.field)}
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
              <Typography variant="body2">{column.headerName}</Typography>
              <Chip
                label={columnVisibility[column.field] ? 'Hidden' : 'Visible'}
                size="small"
                color={columnVisibility[column.field] ? 'default' : 'primary'}
                variant="outlined"
              />
            </Box>
          </MenuItem>
        ))}
      </Menu>

      {/* Summary Statistics */}
      {selectedColumn && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Column Statistics: {selectedColumn}
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {Object.entries(getColumnStats(selectedColumn)).map(([key, value]) => (
              <Chip
                key={key}
                label={`${key}: ${typeof value === 'number' ? value.toFixed(2) : value}`}
                size="small"
                variant="outlined"
              />
            ))}
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default InteractiveTable;
