import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Paper,
  Typography,
  Avatar,
  CircularProgress,
  Divider,
  IconButton,
  Tooltip,
  Chip,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert
} from '@mui/material';
import {
  Send as SendIcon,
  SmartToy as BotIcon,
  Person as PersonIcon,
  MoreVert as MoreVertIcon,
  Download as DownloadIcon,
  Clear as ClearIcon,
  Settings as SettingsIcon,
  Feedback as FeedbackIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useHotkeys } from 'react-hotkeys-hook';
import { v4 as uuidv4 } from 'uuid';

import processPrompt from '../../services/promptProcessor';
import EnhancedChatMessage from './EnhancedChatMessage';
import ModelSelector from '../ModelSelector';

/**
 * Enhanced Chat Agent with advanced features and improved UX
 */
const EnhancedChatAgent = () => {
  const [messages, setMessages] = useState([
    {
      id: uuidv4(),
      sender: 'agent',
      content: `Hello! I'm your Digital Twin Assistant. I can help you with your sensor data and IoT systems using natural language.

**What I can help you with:**
- Query sensor data (temperature, humidity, air quality, etc.)
- Analyze trends and patterns in your data
- Get real-time status of your devices
- Explain data insights in plain English

**Just ask me questions like:**
- "What's the current temperature?"
- "Show me humidity trends from yesterday"
- "Which sensors are currently active?"
- "How has air quality changed this week?"

Feel free to ask me anything about your sensor data! 🌡️📊`,
      type: 'text',
      timestamp: new Date(),
      metadata: {
        isWelcome: true
      }
    }
  ]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [streamingMessage, setStreamingMessage] = useState(null);
  const [menuAnchor, setMenuAnchor] = useState(null);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  // Keyboard shortcuts
  useHotkeys('ctrl+enter', () => handleSend(), { enableOnFormTags: true });
  useHotkeys('ctrl+l', () => handleClearChat());
  useHotkeys('escape', () => {
    setMenuAnchor(null);
    setSettingsOpen(false);
    setExportDialogOpen(false);
  });

  // Auto-scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, streamingMessage]);

  // Focus input on mount
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  const handleSend = async () => {
    if (!input.trim() || loading) return;

    // Add user message
    const userMessage = {
      id: uuidv4(),
      sender: 'user',
      content: input,
      type: 'text',
      timestamp: new Date()
    };

    const userQuery = input;
    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setLoading(true);

    // Start streaming response
    const streamingId = uuidv4();
    setStreamingMessage({
      id: streamingId,
      sender: 'agent',
      content: '',
      type: 'streaming',
      timestamp: new Date()
    });

    try {
      console.log(`Processing prompt: "${userQuery}"`);

      // Process the prompt
      const response = await processPrompt(userQuery);
      console.log('Prompt processing response:', response);

      // Create the final message object
      const messageObj = {
        id: uuidv4(),
        sender: 'agent',
        content: response.content,
        type: response.type,
        timestamp: new Date(),
        metadata: {
          processingTime: Date.now() - userMessage.timestamp.getTime(),
          queryLength: userQuery.length
        }
      };

      // Add data and chartConfig if they exist
      if (response.data) {
        messageObj.data = response.data;
      }

      if (response.chartConfig) {
        messageObj.chartConfig = response.chartConfig;
      }

      if (response.toolResults) {
        messageObj.toolResults = response.toolResults;
      }

      // Remove streaming message and add final message
      setStreamingMessage(null);
      setMessages(prev => [...prev, messageObj]);

    } catch (error) {
      console.error('Error processing prompt:', error);

      const errorMessage = error.message || 'An unexpected error occurred';
      const errorDetails = error.response?.data?.details || error.stack;

      const formattedErrorMessage = `# ❌ Error Processing Query

**Error:** ${errorMessage}

${errorDetails ? `**Details:** ${errorDetails}` : ''}

### 💡 Suggestions:
- Try rephrasing your question
- Check if you're asking about data that exists
- Try a different time range (e.g., "last hour" or "last day")
- Make sure the MCP server is running properly

### 🔧 Troubleshooting:
- Verify your connection to the data source
- Check the browser console (F12) for detailed error information
- Try a simpler query to test connectivity`;

      // Remove streaming message and add error message
      setStreamingMessage(null);
      setMessages(prev => [...prev, {
        id: uuidv4(),
        sender: 'agent',
        content: formattedErrorMessage,
        type: 'error',
        timestamp: new Date(),
        metadata: {
          error: true,
          errorType: error.name || 'UnknownError'
        }
      }]);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleClearChat = () => {
    setMessages([messages[0]]); // Keep welcome message
    setMenuAnchor(null);
  };

  const handleExportChat = () => {
    const chatData = {
      exportDate: new Date().toISOString(),
      messageCount: messages.length,
      messages: messages.map(msg => ({
        id: msg.id,
        sender: msg.sender,
        content: msg.content,
        type: msg.type,
        timestamp: msg.timestamp.toISOString(),
        metadata: msg.metadata
      }))
    };

    const blob = new Blob([JSON.stringify(chatData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-export-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    setExportDialogOpen(false);
  };

  const handleFeedback = (messageId, feedback) => {
    console.log('Message feedback:', { messageId, feedback });
    // Here you could send feedback to analytics or improvement systems
  };

  const getMessageStats = () => {
    const userMessages = messages.filter(m => m.sender === 'user').length;
    const agentMessages = messages.filter(m => m.sender === 'agent').length;
    const errorMessages = messages.filter(m => m.type === 'error').length;

    return { userMessages, agentMessages, errorMessages, total: messages.length };
  };

  const stats = getMessageStats();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Paper
        elevation={3}
        sx={{
          height: '75vh',
          display: 'flex',
          flexDirection: 'column',
          borderRadius: 3,
          overflow: 'hidden',
          border: '1px solid',
          borderColor: 'divider'
        }}
      >
        {/* Enhanced Header */}
        <Box sx={{
          p: 2,
          backgroundColor: 'primary.main',
          color: 'white',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          minHeight: 64,
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar sx={{ mr: 2, width: 40, height: 40, bgcolor: 'secondary.main' }}>
              <BotIcon />
            </Avatar>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                Enhanced Digital Twin Assistant
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                <Chip
                  label={`${stats.total} messages`}
                  size="small"
                  sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white', fontSize: '0.7rem' }}
                />
                {stats.errorMessages > 0 && (
                  <Chip
                    label={`${stats.errorMessages} errors`}
                    size="small"
                    sx={{ bgcolor: 'error.main', color: 'white', fontSize: '0.7rem' }}
                  />
                )}
              </Box>
            </Box>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Box sx={{ backgroundColor: 'rgba(255,255,255,0.15)', p: 1, borderRadius: 2, minWidth: 140 }}>
              <ModelSelector size="small" />
            </Box>

            <Tooltip title="Chat Options">
              <IconButton
                onClick={(e) => setMenuAnchor(e.currentTarget)}
                sx={{ color: 'white' }}
              >
                <MoreVertIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Messages Area */}
        <Box sx={{
          flexGrow: 1,
          p: 2,
          overflowY: 'auto',
          backgroundColor: '#fafafa',
          position: 'relative'
        }}>
          <AnimatePresence>
            {messages.map((message) => (
              <EnhancedChatMessage
                key={message.id}
                message={message}
                onFeedback={handleFeedback}
              />
            ))}
          </AnimatePresence>

          {/* Streaming Message */}
          {streamingMessage && (
            <EnhancedChatMessage
              message={streamingMessage}
              onFeedback={handleFeedback}
            />
          )}

          <div ref={messagesEndRef} />
        </Box>

        {/* Enhanced Input Area */}
        <Box sx={{
          p: 2,
          backgroundColor: 'background.paper',
          borderTop: '1px solid',
          borderColor: 'divider',
          display: 'flex',
          gap: 1
        }}>
          <TextField
            ref={inputRef}
            fullWidth
            variant="outlined"
            placeholder="Ask about your sensor data... (Ctrl+Enter to send)"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={loading}
            multiline
            maxRows={4}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 2
              }
            }}
          />
          <Button
            variant="contained"
            color="primary"
            endIcon={loading ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
            onClick={handleSend}
            disabled={loading || !input.trim()}
            sx={{
              minWidth: 100,
              borderRadius: 2,
              height: 'fit-content',
              alignSelf: 'flex-end'
            }}
          >
            Send
          </Button>
        </Box>
      </Paper>

      {/* Options Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={() => setMenuAnchor(null)}
      >
        <MenuItem onClick={() => { setSettingsOpen(true); setMenuAnchor(null); }}>
          <SettingsIcon sx={{ mr: 1 }} />
          Settings
        </MenuItem>
        <MenuItem onClick={() => { setExportDialogOpen(true); setMenuAnchor(null); }}>
          <DownloadIcon sx={{ mr: 1 }} />
          Export Chat
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleClearChat}>
          <ClearIcon sx={{ mr: 1 }} />
          Clear Chat
        </MenuItem>
      </Menu>

      {/* Export Dialog */}
      <Dialog open={exportDialogOpen} onClose={() => setExportDialogOpen(false)}>
        <DialogTitle>Export Chat History</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" paragraph>
            Export your chat history as a JSON file containing all messages, timestamps, and metadata.
          </Typography>
          <Alert severity="info" sx={{ mt: 2 }}>
            <strong>Export includes:</strong>
            <ul>
              <li>{stats.total} total messages</li>
              <li>{stats.userMessages} user messages</li>
              <li>{stats.agentMessages} agent responses</li>
              <li>Timestamps and metadata</li>
            </ul>
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setExportDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleExportChat} variant="contained">Export</Button>
        </DialogActions>
      </Dialog>
    </motion.div>
  );
};

export default EnhancedChatAgent;
