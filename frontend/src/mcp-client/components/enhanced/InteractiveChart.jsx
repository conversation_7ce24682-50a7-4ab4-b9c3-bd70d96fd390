/**
 * Interactive Chart Component for AG-UI Data Visualization
 * Automatically detects chart type based on data structure
 */

import React, { useMemo, useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert
} from '@mui/material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  Scatter<PERSON>hart,
  Scatter,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { format, parseISO, isValid } from 'date-fns';

/**
 * Interactive Chart with automatic type detection
 */
const InteractiveChart = ({ data, title, onFeedback }) => {
  const [chartType, setChartType] = useState('auto');

  // Analyze data structure and suggest chart types
  const chartAnalysis = useMemo(() => {
    if (!Array.isArray(data) || data.length === 0) {
      return {
        suggestedType: 'none',
        availableTypes: [],
        xField: null,
        yFields: [],
        processedData: []
      };
    }

    const firstRow = data[0];
    const fields = Object.keys(firstRow);

    let xField = null;
    let yFields = [];
    let timeField = null;

    // Analyze field types
    for (const field of fields) {
      const sampleValues = data.slice(0, 5).map(row => row[field]).filter(val => val != null);

      if (sampleValues.length === 0) continue;

      // Check if it's a date/time field
      const isDateTime = sampleValues.every(val => {
        if (typeof val === 'string') {
          const date = parseISO(val);
          return isValid(date) || !isNaN(Date.parse(val));
        }
        return false;
      });

      // Check if it's numeric
      const isNumeric = sampleValues.every(val => {
        const num = parseFloat(val);
        return !isNaN(num) && isFinite(num);
      });

      if (isDateTime) {
        timeField = field;
        if (!xField) xField = field;
      } else if (isNumeric) {
        yFields.push(field);
        if (!xField && !timeField) xField = field;
      } else if (!xField && !timeField) {
        // Use first categorical field as X axis
        xField = field;
      }
    }

    // If no X field found, use first field
    if (!xField && fields.length > 0) {
      xField = fields[0];
    }

    // If no Y fields found, use numeric fields or all except X
    if (yFields.length === 0) {
      yFields = fields.filter(f => f !== xField);
    }

    // Process data for charts
    const processedData = data.map(row => {
      const processed = { ...row };

      // Format date fields for display
      if (timeField && row[timeField]) {
        try {
          const date = parseISO(row[timeField]) || new Date(row[timeField]);
          processed[`${timeField}_formatted`] = format(date, 'MMM dd HH:mm');
        } catch (e) {
          processed[`${timeField}_formatted`] = row[timeField];
        }
      }

      // Ensure numeric fields are numbers
      yFields.forEach(field => {
        if (row[field] != null) {
          const num = parseFloat(row[field]);
          if (!isNaN(num)) {
            processed[field] = num;
          }
        }
      });

      return processed;
    });

    // Suggest chart type based on data characteristics
    let suggestedType = 'line';
    let availableTypes = ['line', 'bar', 'area'];

    if (timeField) {
      suggestedType = 'line';
      availableTypes = ['line', 'area', 'bar'];
    } else if (yFields.length === 1) {
      suggestedType = 'bar';
      availableTypes = ['bar', 'line', 'scatter'];
    } else if (yFields.length > 1) {
      suggestedType = 'line';
      availableTypes = ['line', 'bar', 'area', 'scatter'];
    }

    return {
      suggestedType,
      availableTypes,
      xField,
      yFields: yFields.slice(0, 5), // Limit to 5 series for readability
      timeField,
      processedData
    };
  }, [data]);

  // Auto-select chart type
  React.useEffect(() => {
    if (chartType === 'auto' && chartAnalysis.suggestedType !== 'none') {
      setChartType(chartAnalysis.suggestedType);
    }
  }, [chartAnalysis, chartType]);

  const renderChart = () => {
    const { processedData, xField, yFields, timeField } = chartAnalysis;

    if (processedData.length === 0 || !xField || yFields.length === 0) {
      return (
        <Alert severity="info">
          No suitable data for chart visualization
        </Alert>
      );
    }

    const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00'];
    const xAxisKey = timeField ? `${timeField}_formatted` : xField;

    const commonProps = {
      data: processedData,
      margin: { top: 5, right: 30, left: 20, bottom: 5 }
    };

    switch (chartType) {
      case 'line':
        return (
          <LineChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey={xAxisKey} />
            <YAxis />
            <Tooltip />
            <Legend />
            {yFields.map((field, index) => (
              <Line
                key={field}
                type="monotone"
                dataKey={field}
                stroke={colors[index % colors.length]}
                strokeWidth={2}
                dot={{ r: 3 }}
              />
            ))}
          </LineChart>
        );

      case 'area':
        return (
          <AreaChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey={xAxisKey} />
            <YAxis />
            <Tooltip />
            <Legend />
            {yFields.map((field, index) => (
              <Area
                key={field}
                type="monotone"
                dataKey={field}
                stackId="1"
                stroke={colors[index % colors.length]}
                fill={colors[index % colors.length]}
                fillOpacity={0.6}
              />
            ))}
          </AreaChart>
        );

      case 'bar':
        return (
          <BarChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey={xAxisKey} />
            <YAxis />
            <Tooltip />
            <Legend />
            {yFields.map((field, index) => (
              <Bar
                key={field}
                dataKey={field}
                fill={colors[index % colors.length]}
              />
            ))}
          </BarChart>
        );

      case 'scatter':
        // For scatter plot, use first two numeric fields
        const xScatterField = yFields[0];
        const yScatterField = yFields[1] || yFields[0];

        return (
          <ScatterChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey={xScatterField} type="number" />
            <YAxis dataKey={yScatterField} type="number" />
            <Tooltip />
            <Scatter
              data={processedData}
              fill={colors[0]}
            />
          </ScatterChart>
        );

      default:
        return <Alert severity="warning">Unknown chart type: {chartType}</Alert>;
    }
  };

  if (chartAnalysis.suggestedType === 'none') {
    return (
      <Alert severity="info" sx={{ m: 2 }}>
        No chartable data available. Data needs numeric fields for visualization.
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 2 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          {title || 'Data Chart'}
        </Typography>

        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          <Chip
            label={`${chartAnalysis.processedData.length} points`}
            size="small"
            color="primary"
            variant="outlined"
          />

          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Chart Type</InputLabel>
            <Select
              value={chartType}
              label="Chart Type"
              onChange={(e) => setChartType(e.target.value)}
            >
              {chartAnalysis.availableTypes.map(type => (
                <MenuItem key={type} value={type}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </Box>

      {/* Chart Info */}
      <Box sx={{ mb: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
        <Chip
          label={`X: ${chartAnalysis.xField}`}
          size="small"
          variant="outlined"
        />
        {chartAnalysis.yFields.map(field => (
          <Chip
            key={field}
            label={`Y: ${field}`}
            size="small"
            color="secondary"
            variant="outlined"
          />
        ))}
      </Box>

      {/* Chart */}
      <Paper variant="outlined" sx={{ p: 2, height: 400 }}>
        <ResponsiveContainer width="100%" height="100%">
          {renderChart()}
        </ResponsiveContainer>
      </Paper>
    </Box>
  );
};

export default InteractiveChart;
