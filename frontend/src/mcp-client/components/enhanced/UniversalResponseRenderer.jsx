import React, { useState, useMemo } from 'react';
import {
  Box,
  Paper,
  Typography,
  Tabs,
  Tab,
  Alert,
  Chip,
  IconButton,
  Tooltip,
  Collapse,
  Button,
  Divider
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon,
  TableChart as TableIcon,
  BarChart as ChartIcon,
  Code as CodeIcon,
  DataObject as JsonIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';

// Import visualization components
import AdvancedDataVisualizer from './AdvancedDataVisualizer';
import InteractiveTable from './InteractiveTable';
import CodeRenderer from './CodeRenderer';
import <PERSON><PERSON>Viewer from './JsonViewer';
import MarkdownRenderer from './MarkdownRenderer';

/**
 * Universal Response Renderer - handles any type of response from MCP servers
 * Provides multiple visualization options and interactive features
 */
const UniversalResponseRenderer = ({ response, onFeedback }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [expanded, setExpanded] = useState(true);
  const [showRawData, setShowRawData] = useState(false);

  // Parse and analyze the response
  const analysisResult = useMemo(() => {
    if (!response) return { type: 'empty', data: null, availableViews: [] };

    const { type, content, data, toolResults, meta } = response;

    // PRIORITY: If there's content (text response), show it as the primary view
    if (content && content.trim()) {
      const availableViews = ['text'];

      // Only add data views if there's actual structured data AND it's not just raw text
      if (data && data !== content) {
        // Check if data is structured (not just raw text)
        if (typeof data === 'string' && data !== content) {
          // Check if it's CSV with multiple lines and headers
          if (data.includes(',') && data.includes('\n')) {
            try {
              const lines = data.split('\n').filter(line => line.trim());
              if (lines.length > 2) { // At least header + 2 data rows
                availableViews.push('table', 'chart');
              }
            } catch (e) {
              // Keep as text only
            }
          }

          // Check if it's structured JSON
          try {
            const jsonData = JSON.parse(data);
            if (Array.isArray(jsonData) && jsonData.length > 1) {
              availableViews.push('table', 'json');
              if (typeof jsonData[0] === 'object') {
                availableViews.push('chart');
              }
            } else if (typeof jsonData === 'object' && Object.keys(jsonData).length > 3) {
              availableViews.push('json');
            }
          } catch (e) {
            // Not JSON, keep as text
          }
        } else if (Array.isArray(data) && data.length > 1) {
          availableViews.push('table', 'json');
          if (typeof data[0] === 'object') {
            availableViews.push('chart');
          }
        } else if (typeof data === 'object' && data !== null && Object.keys(data).length > 3) {
          availableViews.push('json');
        }
      }

      return {
        type: 'text', // Primary type is always text for conversational responses
        data: data,
        originalData: data,
        content,
        toolResults,
        meta,
        availableViews: [...new Set(availableViews)]
      };
    }

    // If no content but there's data, treat as data-only response
    if (data) {
      const availableViews = [];
      let parsedData = data;
      let dataType = 'unknown';

      if (typeof data === 'string') {
        // Check if it's CSV
        if (data.includes(',') && data.includes('\n')) {
          try {
            const lines = data.split('\n').filter(line => line.trim());
            if (lines.length > 1) {
              const headers = lines[0].split(',');
              const rows = lines.slice(1).map(line => {
                const values = line.split(',');
                const obj = {};
                headers.forEach((h, i) => { obj[h.trim()] = values[i]?.trim(); });
                return obj;
              });
              parsedData = rows;
              dataType = 'tabular';
              availableViews.push('table', 'chart');
            }
          } catch (e) {
            dataType = 'text';
            availableViews.push('text');
          }
        } else {
          // Try JSON
          try {
            const jsonData = JSON.parse(data);
            parsedData = jsonData;
            dataType = Array.isArray(jsonData) ? 'array' : 'object';
            availableViews.push('json');
            if (Array.isArray(jsonData) && jsonData.length > 0) {
              availableViews.push('table');
              if (typeof jsonData[0] === 'object') {
                availableViews.push('chart');
              }
            }
          } catch (e) {
            dataType = 'text';
            availableViews.push('text');
          }
        }
      } else if (Array.isArray(data)) {
        parsedData = data;
        dataType = 'array';
        availableViews.push('table', 'json');
        if (data.length > 0 && typeof data[0] === 'object') {
          availableViews.push('chart');
        }
      } else if (typeof data === 'object' && data !== null) {
        parsedData = data;
        dataType = 'object';
        availableViews.push('json');
      }

      return {
        type: dataType,
        data: parsedData,
        originalData: data,
        content: null,
        toolResults,
        meta,
        availableViews: [...new Set(availableViews)]
      };
    }

    return { type: 'empty', data: null, availableViews: [] };
  }, [response]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleDownload = () => {
    const dataToDownload = analysisResult.originalData || analysisResult.content || 'No data';
    const blob = new Blob([typeof dataToDownload === 'string' ? dataToDownload : JSON.stringify(dataToDownload, null, 2)], {
      type: 'text/plain'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `response-${Date.now()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const renderTabContent = () => {
    const currentView = analysisResult.availableViews[activeTab];

    switch (currentView) {
      case 'text':
        return (
          <MarkdownRenderer
            content={analysisResult.content}
            onFeedback={onFeedback}
          />
        );

      case 'table':
        return (
          <InteractiveTable
            data={analysisResult.data}
            onFeedback={onFeedback}
          />
        );

      case 'chart':
        return (
          <AdvancedDataVisualizer
            data={analysisResult.data}
            meta={analysisResult.meta}
            onFeedback={onFeedback}
          />
        );

      case 'json':
        return (
          <JsonViewer
            data={analysisResult.data}
            onFeedback={onFeedback}
          />
        );

      case 'code':
        return (
          <CodeRenderer
            code={analysisResult.originalData}
            language="auto"
            onFeedback={onFeedback}
          />
        );

      case 'raw':
        return (
          <Box sx={{ p: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Raw Response Data
            </Typography>
            <Paper
              variant="outlined"
              sx={{
                p: 2,
                bgcolor: 'grey.50',
                maxHeight: 400,
                overflow: 'auto',
                fontFamily: 'monospace',
                fontSize: '0.875rem'
              }}
            >
              <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
                {typeof analysisResult.originalData === 'string'
                  ? analysisResult.originalData
                  : JSON.stringify(analysisResult.originalData, null, 2)
                }
              </pre>
            </Paper>
          </Box>
        );

      default:
        return (
          <Alert severity="info">
            No suitable visualization available for this data type.
          </Alert>
        );
    }
  };

  const getTabIcon = (view) => {
    switch (view) {
      case 'text': return <ViewIcon fontSize="small" />;
      case 'table': return <TableIcon fontSize="small" />;
      case 'chart': return <ChartIcon fontSize="small" />;
      case 'json': return <JsonIcon fontSize="small" />;
      case 'code': return <CodeIcon fontSize="small" />;
      case 'raw': return <CodeIcon fontSize="small" />;
      default: return <ViewIcon fontSize="small" />;
    }
  };

  const getTabLabel = (view) => {
    switch (view) {
      case 'text': return 'Text';
      case 'table': return 'Table';
      case 'chart': return 'Chart';
      case 'json': return 'JSON';
      case 'code': return 'Code';
      case 'raw': return 'Raw';
      default: return view.charAt(0).toUpperCase() + view.slice(1);
    }
  };

  if (!response || analysisResult.type === 'empty') {
    return (
      <Alert severity="info" sx={{ mt: 1 }}>
        No data to display
      </Alert>
    );
  }

  // For text-primary responses (conversational), show text prominently
  if (analysisResult.type === 'text' && analysisResult.availableViews.length === 1) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <MarkdownRenderer
          content={analysisResult.content}
          onFeedback={onFeedback}
        />
      </motion.div>
    );
  }

  // For responses with both text and data, show with minimal header
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Show text content first if it exists */}
      {analysisResult.content && (
        <MarkdownRenderer
          content={analysisResult.content}
          onFeedback={onFeedback}
        />
      )}

      {/* Show data visualizations only if there are multiple views or no text content */}
      {(analysisResult.availableViews.length > 1 || !analysisResult.content) && (
        <Paper elevation={1} sx={{ mt: analysisResult.content ? 2 : 0, overflow: 'hidden' }}>
          {/* Minimal Header for data views */}
          {analysisResult.availableViews.length > 1 && (
            <Box sx={{
              p: 1,
              bgcolor: 'grey.50',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              borderBottom: 1,
              borderColor: 'divider'
            }}>
              <Typography variant="body2" color="text.secondary">
                Data Views
              </Typography>
              <Box>
                <Tooltip title="Download Data">
                  <IconButton
                    size="small"
                    onClick={handleDownload}
                  >
                    <DownloadIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title={expanded ? "Collapse" : "Expand"}>
                  <IconButton
                    size="small"
                    onClick={() => setExpanded(!expanded)}
                  >
                    {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>
          )}

          <Collapse in={expanded}>
            {/* Tabs for multiple views */}
            {analysisResult.availableViews.length > 1 && (
              <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <Tabs
                  value={activeTab}
                  onChange={handleTabChange}
                  variant="scrollable"
                  scrollButtons="auto"
                  size="small"
                >
                  {analysisResult.availableViews.map((view, index) => (
                    <Tab
                      key={view}
                      icon={getTabIcon(view)}
                      label={getTabLabel(view)}
                      iconPosition="start"
                      sx={{ minHeight: 40, fontSize: '0.875rem' }}
                    />
                  ))}
                </Tabs>
              </Box>
            )}

            {/* Content */}
            <AnimatePresence mode="wait">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
              >
                {renderTabContent()}
              </motion.div>
            </AnimatePresence>
          </Collapse>
        </Paper>
      )}
    </motion.div>
  );
};

export default UniversalResponseRenderer;
