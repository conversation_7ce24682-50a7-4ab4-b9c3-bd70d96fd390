import React, { useMemo } from 'react';
import {
  Box,
  Alert
} from '@mui/material';
import { motion } from 'framer-motion';

import MarkdownRenderer from './MarkdownRenderer';
import AGUIDataStandardizer from '../agui/AGUIDataStandardizer';

/**
 * Universal Response Renderer - AG-UI Standardized Implementation
 * Uses AG-UI protocol for standardized data visualization
 */
const UniversalResponseRenderer = ({ response, onFeedback }) => {
  // Convert response to AG-UI agent state format
  const agentState = useMemo(() => {
    if (!response) return null;

    const { content, data, toolResults, metadata } = response;

    // Ensure content is always a string
    const safeContent = typeof content === 'string'
      ? content
      : typeof content === 'object'
        ? JSON.stringify(content, null, 2)
        : String(content || '');

    // Create AG-UI compliant agent state
    return {
      dataAvailable: !!(data || toolResults),
      toolResults: toolResults || [],
      rawData: data,
      metadata: metadata || {},
      llmProvider: metadata?.llmProvider || 'unknown',
      queryProcessed: true
    };
  }, [response]);

  // Extract content for text display
  const contentString = useMemo(() => {
    if (!response) return '';

    const { content } = response;
    return typeof content === 'string'
      ? content
      : typeof content === 'object'
        ? JSON.stringify(content, null, 2)
        : String(content || '');
  }, [response]);

  if (!response) {
    return (
      <Alert severity="info" sx={{ mt: 1 }}>
        No data to display
      </Alert>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Show text content first (AG-UI conversational response) */}
      {contentString && contentString.trim() && (
        <MarkdownRenderer
          content={contentString}
          onFeedback={onFeedback}
        />
      )}

      {/* Show standardized data visualization using AG-UI protocol */}
      {agentState && (
        <AGUIDataStandardizer
          agentState={agentState}
          onFeedback={onFeedback}
        />
      )}
    </motion.div>
  );
};

export default UniversalResponseRenderer;
