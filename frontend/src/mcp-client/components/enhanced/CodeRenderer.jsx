import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  IconButton,
  Tooltip,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip
} from '@mui/material';
import {
  ContentCopy as CopyIcon,
  Download as DownloadIcon,
  Code as CodeIcon
} from '@mui/icons-material';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { tomorrow, prism } from 'react-syntax-highlighter/dist/esm/styles/prism';

/**
 * Code Renderer with syntax highlighting and language detection
 */
const CodeRenderer = ({ code, language = 'auto', onFeedback }) => {
  const [selectedLanguage, setSelectedLanguage] = useState(language);
  const [theme, setTheme] = useState('tomorrow');
  const [copied, setCopied] = useState(false);

  // Common programming languages
  const supportedLanguages = [
    'auto', 'javascript', 'typescript', 'python', 'java', 'cpp', 'c',
    'csharp', 'php', 'ruby', 'go', 'rust', 'swift', 'kotlin',
    'html', 'css', 'scss', 'sql', 'json', 'xml', 'yaml',
    'bash', 'powershell', 'dockerfile', 'markdown', 'text'
  ];

  const themes = {
    tomorrow: tomorrow,
    prism: prism
  };

  // Auto-detect language based on code content
  const detectLanguage = (codeContent) => {
    if (!codeContent || typeof codeContent !== 'string') return 'text';

    const content = codeContent.toLowerCase().trim();

    // JavaScript/TypeScript patterns
    if (content.includes('function') || content.includes('const ') || content.includes('let ') || 
        content.includes('var ') || content.includes('=>') || content.includes('import ') ||
        content.includes('export ') || content.includes('require(')) {
      if (content.includes('interface ') || content.includes(': string') || content.includes(': number')) {
        return 'typescript';
      }
      return 'javascript';
    }

    // Python patterns
    if (content.includes('def ') || content.includes('import ') || content.includes('from ') ||
        content.includes('print(') || content.includes('if __name__')) {
      return 'python';
    }

    // Java patterns
    if (content.includes('public class') || content.includes('private ') || content.includes('public ') ||
        content.includes('System.out.println') || content.includes('import java.')) {
      return 'java';
    }

    // C/C++ patterns
    if (content.includes('#include') || content.includes('int main') || content.includes('printf(') ||
        content.includes('cout <<') || content.includes('std::')) {
      return content.includes('std::') || content.includes('cout') ? 'cpp' : 'c';
    }

    // HTML patterns
    if (content.includes('<html') || content.includes('<!doctype') || content.includes('<div') ||
        content.includes('<body') || content.includes('<head')) {
      return 'html';
    }

    // CSS patterns
    if (content.includes('{') && content.includes('}') && 
        (content.includes('color:') || content.includes('margin:') || content.includes('padding:'))) {
      return 'css';
    }

    // JSON patterns
    if ((content.startsWith('{') && content.endsWith('}')) || 
        (content.startsWith('[') && content.endsWith(']'))) {
      try {
        JSON.parse(codeContent);
        return 'json';
      } catch (e) {
        // Not valid JSON
      }
    }

    // SQL patterns
    if (content.includes('select ') || content.includes('insert ') || content.includes('update ') ||
        content.includes('delete ') || content.includes('create table') || content.includes('from ')) {
      return 'sql';
    }

    // Bash patterns
    if (content.includes('#!/bin/bash') || content.includes('echo ') || content.includes('cd ') ||
        content.includes('ls ') || content.includes('grep ') || content.includes('$')) {
      return 'bash';
    }

    // YAML patterns
    if (content.includes('---') || (content.includes(':') && content.includes('  ') && !content.includes(';'))) {
      return 'yaml';
    }

    // XML patterns
    if (content.includes('<?xml') || (content.includes('<') && content.includes('/>') && !content.includes('html'))) {
      return 'xml';
    }

    return 'text';
  };

  const getEffectiveLanguage = () => {
    if (selectedLanguage === 'auto') {
      return detectLanguage(code);
    }
    return selectedLanguage;
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(code).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  const downloadCode = () => {
    const effectiveLanguage = getEffectiveLanguage();
    const extension = {
      javascript: 'js',
      typescript: 'ts',
      python: 'py',
      java: 'java',
      cpp: 'cpp',
      c: 'c',
      csharp: 'cs',
      php: 'php',
      ruby: 'rb',
      go: 'go',
      rust: 'rs',
      swift: 'swift',
      kotlin: 'kt',
      html: 'html',
      css: 'css',
      scss: 'scss',
      sql: 'sql',
      json: 'json',
      xml: 'xml',
      yaml: 'yml',
      bash: 'sh',
      powershell: 'ps1',
      dockerfile: 'dockerfile',
      markdown: 'md'
    }[effectiveLanguage] || 'txt';

    const blob = new Blob([code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `code-${Date.now()}.${extension}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getLanguageStats = () => {
    if (!code) return { lines: 0, chars: 0, words: 0 };
    
    const lines = code.split('\n').length;
    const chars = code.length;
    const words = code.split(/\s+/).filter(word => word.length > 0).length;
    
    return { lines, chars, words };
  };

  if (!code) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography color="text.secondary">No code to display</Typography>
      </Box>
    );
  }

  const stats = getLanguageStats();
  const effectiveLanguage = getEffectiveLanguage();

  return (
    <Box sx={{ p: 2 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, flexWrap: 'wrap', gap: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
            <CodeIcon sx={{ mr: 1 }} />
            Code
          </Typography>
          <Chip 
            label={effectiveLanguage}
            size="small"
            color="primary"
            variant="outlined"
          />
        </Box>
        
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Language</InputLabel>
            <Select
              value={selectedLanguage}
              label="Language"
              onChange={(e) => setSelectedLanguage(e.target.value)}
            >
              {supportedLanguages.map(lang => (
                <MenuItem key={lang} value={lang}>
                  {lang === 'auto' ? 'Auto-detect' : lang.charAt(0).toUpperCase() + lang.slice(1)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl size="small" sx={{ minWidth: 100 }}>
            <InputLabel>Theme</InputLabel>
            <Select
              value={theme}
              label="Theme"
              onChange={(e) => setTheme(e.target.value)}
            >
              <MenuItem value="tomorrow">Dark</MenuItem>
              <MenuItem value="prism">Light</MenuItem>
            </Select>
          </FormControl>

          <Tooltip title={copied ? 'Copied!' : 'Copy code'}>
            <IconButton onClick={copyToClipboard} size="small">
              <CopyIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Download code">
            <IconButton onClick={downloadCode} size="small">
              <DownloadIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Stats */}
      <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
        <Chip label={`${stats.lines} lines`} size="small" variant="outlined" />
        <Chip label={`${stats.chars} characters`} size="small" variant="outlined" />
        <Chip label={`${stats.words} words`} size="small" variant="outlined" />
      </Box>

      {/* Code Display */}
      <Paper variant="outlined" sx={{ overflow: 'hidden' }}>
        <SyntaxHighlighter
          language={effectiveLanguage}
          style={themes[theme]}
          showLineNumbers={stats.lines > 5}
          wrapLines={true}
          customStyle={{
            margin: 0,
            fontSize: '0.875rem',
            maxHeight: '600px',
            overflow: 'auto'
          }}
          codeTagProps={{
            style: {
              fontFamily: '"Fira Code", "Consolas", "Monaco", "Courier New", monospace'
            }
          }}
        >
          {code}
        </SyntaxHighlighter>
      </Paper>
    </Box>
  );
};

export default CodeRenderer;
