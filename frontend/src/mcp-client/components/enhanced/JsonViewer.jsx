import React, { useState } from 'react';
import {
  Box,
  Typography,
  IconButton,
  Tooltip,
  Paper,
  Collapse,
  Chip
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  ContentCopy as CopyIcon,
  Download as DownloadIcon
} from '@mui/icons-material';

/**
 * Interactive JSON Viewer with collapsible nodes and copy functionality
 */
const JsonViewer = ({ data, onFeedback }) => {
  const [expandedNodes, setExpandedNodes] = useState(new Set(['root']));
  const [copiedPath, setCopiedPath] = useState('');

  const toggleNode = (path) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedNodes(newExpanded);
  };

  const copyToClipboard = (value, path = '') => {
    const textToCopy = typeof value === 'string' ? value : JSON.stringify(value, null, 2);
    navigator.clipboard.writeText(textToCopy).then(() => {
      setCopiedPath(path);
      setTimeout(() => setCopiedPath(''), 2000);
    });
  };

  const downloadJson = () => {
    const jsonString = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `data-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getValueType = (value) => {
    if (value === null) return 'null';
    if (Array.isArray(value)) return 'array';
    return typeof value;
  };

  const getValueColor = (type) => {
    switch (type) {
      case 'string': return 'success.main';
      case 'number': return 'info.main';
      case 'boolean': return 'warning.main';
      case 'null': return 'text.secondary';
      case 'array': return 'secondary.main';
      case 'object': return 'primary.main';
      default: return 'text.primary';
    }
  };

  const renderValue = (value, path = 'root', depth = 0) => {
    const type = getValueType(value);
    const isExpanded = expandedNodes.has(path);
    const hasChildren = (type === 'object' && value !== null && Object.keys(value).length > 0) ||
                       (type === 'array' && value.length > 0);

    if (type === 'object' && value !== null) {
      const keys = Object.keys(value);
      return (
        <Box key={path} sx={{ ml: depth * 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', py: 0.5 }}>
            {hasChildren && (
              <IconButton
                size="small"
                onClick={() => toggleNode(path)}
                sx={{ mr: 0.5, p: 0.25 }}
              >
                {isExpanded ? <ExpandLessIcon fontSize="small" /> : <ExpandMoreIcon fontSize="small" />}
              </IconButton>
            )}
            <Typography
              variant="body2"
              sx={{ 
                fontFamily: 'monospace',
                color: getValueColor(type),
                fontWeight: 'bold'
              }}
            >
              {depth === 0 ? 'Object' : '{'} 
              <Chip 
                label={`${keys.length} ${keys.length === 1 ? 'property' : 'properties'}`}
                size="small"
                sx={{ ml: 1, height: 20, fontSize: '0.7rem' }}
              />
            </Typography>
            <Tooltip title={copiedPath === path ? 'Copied!' : 'Copy object'}>
              <IconButton
                size="small"
                onClick={() => copyToClipboard(value, path)}
                sx={{ ml: 1, p: 0.25 }}
              >
                <CopyIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
          <Collapse in={isExpanded}>
            <Box>
              {keys.map((key, index) => (
                <Box key={key} sx={{ display: 'flex', alignItems: 'flex-start', ml: 2 }}>
                  <Typography
                    variant="body2"
                    sx={{
                      fontFamily: 'monospace',
                      color: 'text.secondary',
                      minWidth: 'fit-content',
                      mr: 1,
                      mt: 0.5
                    }}
                  >
                    "{key}":
                  </Typography>
                  <Box sx={{ flex: 1 }}>
                    {renderValue(value[key], `${path}.${key}`, depth + 1)}
                  </Box>
                </Box>
              ))}
            </Box>
          </Collapse>
          {depth === 0 && <Typography variant="body2" sx={{ fontFamily: 'monospace', color: getValueColor(type) }}>}</Typography>}
        </Box>
      );
    }

    if (type === 'array') {
      return (
        <Box key={path} sx={{ ml: depth * 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', py: 0.5 }}>
            {hasChildren && (
              <IconButton
                size="small"
                onClick={() => toggleNode(path)}
                sx={{ mr: 0.5, p: 0.25 }}
              >
                {isExpanded ? <ExpandLessIcon fontSize="small" /> : <ExpandMoreIcon fontSize="small" />}
              </IconButton>
            )}
            <Typography
              variant="body2"
              sx={{ 
                fontFamily: 'monospace',
                color: getValueColor(type),
                fontWeight: 'bold'
              }}
            >
              Array
              <Chip 
                label={`${value.length} ${value.length === 1 ? 'item' : 'items'}`}
                size="small"
                sx={{ ml: 1, height: 20, fontSize: '0.7rem' }}
              />
            </Typography>
            <Tooltip title={copiedPath === path ? 'Copied!' : 'Copy array'}>
              <IconButton
                size="small"
                onClick={() => copyToClipboard(value, path)}
                sx={{ ml: 1, p: 0.25 }}
              >
                <CopyIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
          <Collapse in={isExpanded}>
            <Box>
              {value.map((item, index) => (
                <Box key={index} sx={{ display: 'flex', alignItems: 'flex-start', ml: 2 }}>
                  <Typography
                    variant="body2"
                    sx={{
                      fontFamily: 'monospace',
                      color: 'text.secondary',
                      minWidth: 'fit-content',
                      mr: 1,
                      mt: 0.5
                    }}
                  >
                    [{index}]:
                  </Typography>
                  <Box sx={{ flex: 1 }}>
                    {renderValue(item, `${path}[${index}]`, depth + 1)}
                  </Box>
                </Box>
              ))}
            </Box>
          </Collapse>
        </Box>
      );
    }

    // Primitive values
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', py: 0.25, ml: depth * 2 }}>
        <Typography
          variant="body2"
          sx={{
            fontFamily: 'monospace',
            color: getValueColor(type),
            wordBreak: 'break-all',
            flex: 1
          }}
        >
          {type === 'string' ? `"${value}"` : String(value)}
        </Typography>
        <Chip 
          label={type}
          size="small"
          variant="outlined"
          sx={{ ml: 1, height: 18, fontSize: '0.6rem' }}
        />
        <Tooltip title={copiedPath === path ? 'Copied!' : 'Copy value'}>
          <IconButton
            size="small"
            onClick={() => copyToClipboard(value, path)}
            sx={{ ml: 0.5, p: 0.25 }}
          >
            <CopyIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>
    );
  };

  if (!data) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography color="text.secondary">No JSON data to display</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 2 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">JSON Data</Typography>
        <Box>
          <Tooltip title="Expand All">
            <IconButton
              size="small"
              onClick={() => {
                const getAllPaths = (obj, currentPath = 'root', paths = new Set()) => {
                  paths.add(currentPath);
                  if (typeof obj === 'object' && obj !== null) {
                    if (Array.isArray(obj)) {
                      obj.forEach((item, index) => {
                        getAllPaths(item, `${currentPath}[${index}]`, paths);
                      });
                    } else {
                      Object.keys(obj).forEach(key => {
                        getAllPaths(obj[key], `${currentPath}.${key}`, paths);
                      });
                    }
                  }
                  return paths;
                };
                setExpandedNodes(getAllPaths(data));
              }}
              sx={{ mr: 1 }}
            >
              <ExpandMoreIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Collapse All">
            <IconButton
              size="small"
              onClick={() => setExpandedNodes(new Set(['root']))}
              sx={{ mr: 1 }}
            >
              <ExpandLessIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Download JSON">
            <IconButton size="small" onClick={downloadJson}>
              <DownloadIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* JSON Content */}
      <Paper 
        variant="outlined" 
        sx={{ 
          p: 2, 
          backgroundColor: 'grey.50',
          maxHeight: 600,
          overflow: 'auto'
        }}
      >
        {renderValue(data)}
      </Paper>
    </Box>
  );
};

export default JsonViewer;
