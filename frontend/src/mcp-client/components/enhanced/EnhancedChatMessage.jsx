import React, { useState } from 'react';
import {
  Box,
  Typography,
  Avatar,
  Paper,
  IconButton,
  Tooltip,
  Chip,
  Menu,
  MenuItem,
  Rating,
  Collapse
} from '@mui/material';
import {
  SmartToy as BotIcon,
  Person as PersonIcon,
  ThumbUp as ThumbUpIcon,
  ThumbDown as ThumbDownIcon,
  MoreVert as MoreVertIcon,
  ContentCopy as CopyIcon,
  Share as ShareIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  AccessTime as TimeIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { formatDistanceToNow } from 'date-fns';

import UniversalResponseRenderer from './UniversalResponseRenderer';

/**
 * Enhanced Chat Message with feedback, actions, and improved visualization
 */
const EnhancedChatMessage = ({ message, onFeedback }) => {
  const [menuAnchor, setMenuAnchor] = useState(null);
  const [feedback, setFeedback] = useState(null);
  const [showMetadata, setShowMetadata] = useState(false);
  const [copied, setCopied] = useState(false);

  const handleFeedback = (type, rating = null) => {
    const feedbackData = {
      type,
      rating,
      timestamp: new Date(),
      messageId: message.id
    };
    setFeedback(feedbackData);
    onFeedback?.(message.id, feedbackData);
    setMenuAnchor(null);
  };

  const handleCopy = () => {
    const textToCopy = message.content || 'No content';
    navigator.clipboard.writeText(textToCopy).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
    setMenuAnchor(null);
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: 'Digital Twin Assistant Response',
        text: message.content,
        url: window.location.href
      });
    } else {
      handleCopy();
    }
    setMenuAnchor(null);
  };

  const getMessageVariant = () => {
    switch (message.type) {
      case 'error': return 'error';
      case 'streaming': return 'streaming';
      case 'loading': return 'loading';
      default: return 'default';
    }
  };

  const getMessageColor = () => {
    if (message.sender === 'user') return 'primary.main';
    
    switch (message.type) {
      case 'error': return '#ffebee';
      case 'streaming': return 'rgba(25, 118, 210, 0.04)';
      case 'loading': return 'rgba(0, 0, 0, 0.02)';
      default: return 'white';
    }
  };

  const renderLoadingAnimation = () => (
    <Box sx={{ display: 'flex', alignItems: 'center', py: 1 }}>
      {[0, 1, 2].map((index) => (
        <Box
          key={index}
          sx={{
            width: 8,
            height: 8,
            borderRadius: '50%',
            backgroundColor: 'primary.main',
            animation: 'pulse 1.5s infinite',
            animationDelay: `${index * 0.2}s`,
            mr: 1,
            '@keyframes pulse': {
              '0%': { opacity: 0.3, transform: 'scale(1)' },
              '50%': { opacity: 1, transform: 'scale(1.2)' },
              '100%': { opacity: 0.3, transform: 'scale(1)' }
            }
          }}
        />
      ))}
      <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
        {message.content || 'Thinking...'}
      </Typography>
    </Box>
  );

  const renderStreamingAnimation = () => (
    <Box sx={{ position: 'relative' }}>
      <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
        {message.content}
        <Box
          component="span"
          sx={{
            display: 'inline-block',
            width: '2px',
            height: '1.2em',
            backgroundColor: 'primary.main',
            animation: 'blink 1s infinite',
            ml: 0.5,
            '@keyframes blink': {
              '0%, 50%': { opacity: 1 },
              '51%, 100%': { opacity: 0 }
            }
          }}
        />
      </Typography>
    </Box>
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start',
          mb: 3,
          position: 'relative'
        }}
      >
        {message.sender === 'agent' && (
          <Avatar 
            sx={{ 
              mr: 2, 
              bgcolor: message.type === 'error' ? 'error.main' : 'primary.main',
              width: 40,
              height: 40
            }}
          >
            <BotIcon />
          </Avatar>
        )}

        <Box
          sx={{
            maxWidth: { xs: '90%', sm: '85%', md: '80%' },
            minWidth: 200,
            position: 'relative'
          }}
        >
          {/* Message Header */}
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start',
            mb: 0.5,
            gap: 1
          }}>
            <Typography variant="caption" color="text.secondary">
              {message.sender === 'user' ? 'You' : 'Assistant'}
            </Typography>
            <Chip 
              icon={<TimeIcon />}
              label={formatDistanceToNow(message.timestamp, { addSuffix: true })}
              size="small"
              variant="outlined"
              sx={{ fontSize: '0.7rem', height: 20 }}
            />
            {message.sender === 'agent' && (
              <Tooltip title="Message options">
                <IconButton 
                  size="small" 
                  onClick={(e) => setMenuAnchor(e.currentTarget)}
                  sx={{ opacity: 0.7, '&:hover': { opacity: 1 } }}
                >
                  <MoreVertIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </Box>

          {/* Message Content */}
          <Paper
            elevation={message.sender === 'user' ? 2 : 1}
            sx={{
              p: 2,
              borderRadius: 3,
              backgroundColor: getMessageColor(),
              color: message.sender === 'user' ? 'white' : 'text.primary',
              position: 'relative',
              border: message.type === 'error' ? '1px solid' : 'none',
              borderColor: message.type === 'error' ? 'error.main' : 'transparent',
              ...(message.type === 'streaming' && {
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  height: '2px',
                  width: '100%',
                  background: 'linear-gradient(90deg, transparent, primary.main, transparent)',
                  animation: 'loading 2s infinite',
                },
                '@keyframes loading': {
                  '0%': { transform: 'translateX(-100%)' },
                  '100%': { transform: 'translateX(100%)' }
                }
              })
            }}
          >
            {/* Render different content based on message type */}
            {message.type === 'loading' && renderLoadingAnimation()}
            {message.type === 'streaming' && renderStreamingAnimation()}
            
            {/* Regular content */}
            {message.type !== 'loading' && message.type !== 'streaming' && (
              <>
                {/* Text content */}
                {message.content && (
                  <UniversalResponseRenderer 
                    response={message} 
                    onFeedback={onFeedback}
                  />
                )}
              </>
            )}

            {/* Feedback indicators */}
            {feedback && (
              <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                <Chip
                  icon={feedback.type === 'positive' ? <ThumbUpIcon /> : <ThumbDownIcon />}
                  label={feedback.type === 'positive' ? 'Helpful' : 'Not helpful'}
                  size="small"
                  color={feedback.type === 'positive' ? 'success' : 'error'}
                  variant="outlined"
                />
                {feedback.rating && (
                  <Rating value={feedback.rating} size="small" readOnly />
                )}
              </Box>
            )}
          </Paper>

          {/* Metadata */}
          {message.metadata && Object.keys(message.metadata).length > 0 && (
            <Box sx={{ mt: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <IconButton 
                  size="small" 
                  onClick={() => setShowMetadata(!showMetadata)}
                  sx={{ opacity: 0.6 }}
                >
                  {showMetadata ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
                <Typography variant="caption" color="text.secondary">
                  Message details
                </Typography>
              </Box>
              <Collapse in={showMetadata}>
                <Paper variant="outlined" sx={{ p: 1, mt: 1, bgcolor: 'grey.50' }}>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {Object.entries(message.metadata).map(([key, value]) => (
                      <Chip
                        key={key}
                        label={`${key}: ${value}`}
                        size="small"
                        variant="outlined"
                        sx={{ fontSize: '0.7rem' }}
                      />
                    ))}
                  </Box>
                </Paper>
              </Collapse>
            </Box>
          )}
        </Box>

        {message.sender === 'user' && (
          <Avatar sx={{ ml: 2, bgcolor: 'secondary.main', width: 40, height: 40 }}>
            <PersonIcon />
          </Avatar>
        )}

        {/* Message Actions Menu */}
        <Menu
          anchorEl={menuAnchor}
          open={Boolean(menuAnchor)}
          onClose={() => setMenuAnchor(null)}
          PaperProps={{
            sx: { minWidth: 200 }
          }}
        >
          <MenuItem onClick={() => handleFeedback('positive')}>
            <ThumbUpIcon sx={{ mr: 1 }} />
            Mark as helpful
          </MenuItem>
          <MenuItem onClick={() => handleFeedback('negative')}>
            <ThumbDownIcon sx={{ mr: 1 }} />
            Mark as not helpful
          </MenuItem>
          <MenuItem onClick={handleCopy}>
            <CopyIcon sx={{ mr: 1 }} />
            {copied ? 'Copied!' : 'Copy content'}
          </MenuItem>
          <MenuItem onClick={handleShare}>
            <ShareIcon sx={{ mr: 1 }} />
            Share
          </MenuItem>
          {message.type === 'error' && (
            <MenuItem onClick={() => console.log('Retry functionality')}>
              <RefreshIcon sx={{ mr: 1 }} />
              Retry query
            </MenuItem>
          )}
        </Menu>
      </Box>
    </motion.div>
  );
};

export default EnhancedChatMessage;
