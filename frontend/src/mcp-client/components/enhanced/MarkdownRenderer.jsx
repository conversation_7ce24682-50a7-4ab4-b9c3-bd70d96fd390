import React from 'react';
import { Box, Typography, Paper, Divider } from '@mui/material';
import Markdown from 'markdown-to-jsx';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';

/**
 * Enhanced Markdown Renderer with syntax highlighting and custom styling
 */
const MarkdownRenderer = ({ content, onFeedback }) => {
  if (!content) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography color="text.secondary">No content to display</Typography>
      </Box>
    );
  }

  const MarkdownComponents = {
    h1: ({ children, ...props }) => (
      <Typography variant="h4" component="h1" gutterBottom sx={{ mt: 2, mb: 1, fontWeight: 'bold' }} {...props}>
        {children}
      </Typography>
    ),
    h2: ({ children, ...props }) => (
      <Typography variant="h5" component="h2" gutterBottom sx={{ mt: 2, mb: 1, fontWeight: 'bold' }} {...props}>
        {children}
      </Typography>
    ),
    h3: ({ children, ...props }) => (
      <Typography variant="h6" component="h3" gutterBottom sx={{ mt: 1.5, mb: 1, fontWeight: 'bold' }} {...props}>
        {children}
      </Typography>
    ),
    h4: ({ children, ...props }) => (
      <Typography variant="subtitle1" component="h4" gutterBottom sx={{ mt: 1.5, mb: 1, fontWeight: 'bold' }} {...props}>
        {children}
      </Typography>
    ),
    h5: ({ children, ...props }) => (
      <Typography variant="subtitle2" component="h5" gutterBottom sx={{ mt: 1, mb: 0.5, fontWeight: 'bold' }} {...props}>
        {children}
      </Typography>
    ),
    h6: ({ children, ...props }) => (
      <Typography variant="body1" component="h6" gutterBottom sx={{ mt: 1, mb: 0.5, fontWeight: 'bold' }} {...props}>
        {children}
      </Typography>
    ),
    p: ({ children, ...props }) => (
      <Typography variant="body1" paragraph sx={{ mb: 1.5, lineHeight: 1.6 }} {...props}>
        {children}
      </Typography>
    ),
    strong: ({ children, ...props }) => (
      <Typography component="strong" sx={{ fontWeight: 'bold' }} {...props}>
        {children}
      </Typography>
    ),
    em: ({ children, ...props }) => (
      <Typography component="em" sx={{ fontStyle: 'italic' }} {...props}>
        {children}
      </Typography>
    ),
    code: ({ children, className, ...props }) => {
      const language = className?.replace('lang-', '') || 'text';

      // Inline code
      if (!className) {
        return (
          <Typography
            component="code"
            sx={{
              backgroundColor: 'rgba(0, 0, 0, 0.08)',
              padding: '2px 6px',
              borderRadius: '4px',
              fontFamily: 'monospace',
              fontSize: '0.875em',
              color: 'primary.main'
            }}
            {...props}
          >
            {children}
          </Typography>
        );
      }

      // Code block
      return (
        <Box sx={{ my: 2 }}>
          <SyntaxHighlighter
            language={language}
            style={tomorrow}
            customStyle={{
              borderRadius: '8px',
              fontSize: '0.875rem',
              margin: 0
            }}
            {...props}
          >
            {String(children).replace(/\n$/, '')}
          </SyntaxHighlighter>
        </Box>
      );
    },
    pre: ({ children, ...props }) => (
      <Paper
        variant="outlined"
        sx={{
          p: 2,
          my: 2,
          backgroundColor: 'grey.50',
          overflow: 'auto',
          fontFamily: 'monospace',
          fontSize: '0.875rem'
        }}
        {...props}
      >
        {children}
      </Paper>
    ),
    blockquote: ({ children, ...props }) => (
      <Paper
        variant="outlined"
        sx={{
          p: 2,
          my: 2,
          borderLeft: '4px solid',
          borderLeftColor: 'primary.main',
          backgroundColor: 'primary.light',
          color: 'primary.contrastText'
        }}
        {...props}
      >
        {children}
      </Paper>
    ),
    ul: ({ children, ...props }) => (
      <Box component="ul" sx={{ pl: 3, my: 1 }} {...props}>
        {children}
      </Box>
    ),
    ol: ({ children, ...props }) => (
      <Box component="ol" sx={{ pl: 3, my: 1 }} {...props}>
        {children}
      </Box>
    ),
    li: ({ children, ...props }) => (
      <Typography component="li" variant="body1" sx={{ mb: 0.5 }} {...props}>
        {children}
      </Typography>
    ),
    table: ({ children, ...props }) => (
      <Paper variant="outlined" sx={{ my: 2, overflow: 'auto' }}>
        <Box component="table" sx={{ width: '100%', borderCollapse: 'collapse' }} {...props}>
          {children}
        </Box>
      </Paper>
    ),
    thead: ({ children, ...props }) => (
      <Box component="thead" sx={{ backgroundColor: 'grey.100' }} {...props}>
        {children}
      </Box>
    ),
    tbody: ({ children, ...props }) => (
      <Box component="tbody" {...props}>
        {children}
      </Box>
    ),
    tr: ({ children, ...props }) => (
      <Box component="tr" sx={{ '&:nth-of-type(even)': { backgroundColor: 'grey.50' } }} {...props}>
        {children}
      </Box>
    ),
    th: ({ children, ...props }) => (
      <Typography
        component="th"
        variant="subtitle2"
        sx={{
          p: 1.5,
          textAlign: 'left',
          fontWeight: 'bold',
          borderBottom: '2px solid',
          borderBottomColor: 'divider'
        }}
        {...props}
      >
        {children}
      </Typography>
    ),
    td: ({ children, ...props }) => (
      <Typography
        component="td"
        variant="body2"
        sx={{
          p: 1.5,
          borderBottom: '1px solid',
          borderBottomColor: 'divider'
        }}
        {...props}
      >
        {children}
      </Typography>
    ),
    hr: ({ ...props }) => (
      <Divider sx={{ my: 3 }} {...props} />
    ),
    a: ({ children, href, ...props }) => (
      <Typography
        component="a"
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        sx={{
          color: 'primary.main',
          textDecoration: 'none',
          '&:hover': {
            textDecoration: 'underline'
          }
        }}
        {...props}
      >
        {children}
      </Typography>
    ),
    img: ({ src, alt, ...props }) => (
      <Box
        component="img"
        src={src}
        alt={alt}
        sx={{
          maxWidth: '100%',
          height: 'auto',
          borderRadius: 1,
          my: 2
        }}
        {...props}
      />
    )
  };

  return (
    <Box sx={{
      py: 1,
      '& > *:first-of-type': { mt: 0 },
      '& > *:last-child': { mb: 0 }
    }}>
      <Markdown
        options={{
          overrides: MarkdownComponents,
          forceBlock: true
        }}
      >
        {content}
      </Markdown>
    </Box>
  );
};

export default MarkdownRenderer;
