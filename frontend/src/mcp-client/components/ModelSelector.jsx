import React, { useState, useEffect } from 'react';
import {
  FormControl,
  Select,
  MenuItem,
  Box,
  Chip,
  Avatar
} from '@mui/material';
import mcpService from '../services/mcpService';
import { SmartToy as BotIcon } from '@mui/icons-material';

/**
 * Component for selecting the LLM model to use for MCP queries
 */
const ModelSelector = () => {
  // Always default to Google Gemini
  const [model, setModel] = useState('google');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [availableModels, setAvailableModels] = useState([
    { id: 'anthropic', name: 'Anthrop<PERSON> <PERSON>', icon: '🤖', available: false },
    { id: 'google', name: 'Google Gemini', icon: '🔍', available: false }
  ]);

  useEffect(() => {
    // Get the current model from the backend
    const fetchCurrentModel = async () => {
      setLoading(true);
      setError(null);

      try {
        // Get the current model and available models from the backend
        const response = await mcpService.getCurrentModel();
        console.log('Current model response:', response);

        if (response) {
          // Update the active model
          if (response.activeLLM) {
            setModel(response.activeLLM);
          }

          // Update available models
          if (response.available) {
            const updatedModels = [...availableModels];

            // Update availability for each model
            updatedModels.forEach(model => {
              model.available = response.available[model.id] === true;
            });

            setAvailableModels(updatedModels);

            // If the current model is not available, default to Google
            const currentModelAvailable = updatedModels.find(m =>
              m.id === response.activeLLM && m.available
            );

            if (!currentModelAvailable && updatedModels.find(m => m.id === 'google' && m.available)) {
              console.log('Current model not available, defaulting to Google');
              setModel('google');
              await mcpService.setModel('google');
            }
          }
        }
      } catch (error) {
        console.error('Error fetching current model:', error);
        setError('Failed to load model information');
        // Default to Google if there's an error
        setModel('google');
      } finally {
        setLoading(false);
      }
    };

    fetchCurrentModel();
  }, []);

  const handleModelChange = async (event) => {
    const newModel = event.target.value;

    // Check if the model is available
    const modelInfo = availableModels.find(m => m.id === newModel);
    if (!modelInfo || !modelInfo.available) {
      setError(`Model ${newModel} is not available`);
      return;
    }

    setLoading(true);
    setError(null);

    // Optimistically update the UI
    setModel(newModel);

    try {
      // Update the model in the backend
      console.log(`Setting model to ${newModel}`);
      const response = await mcpService.setModel(newModel);
      console.log(`Model changed to ${newModel}`, response);

      // If the model change was successful, show a message
      if (response && response.success) {
        console.log(`Successfully changed model to ${newModel}`);
      } else {
        console.warn(`Failed to change model to ${newModel}`, response);
        setError(response.error || `Failed to change model to ${newModel}`);

        // If the model change failed, revert to Google
        if (newModel !== 'google') {
          console.log('Reverting to Google model');
          setModel('google');
          try {
            await mcpService.setModel('google');
          } catch (setError) {
            console.error('Error setting model to Google:', setError);
          }
        }
      }
    } catch (error) {
      console.error('Error changing model:', error);
      setError(`Error: ${error.message || 'Failed to change model'}`);

      // If there was an error, revert to Google
      if (newModel !== 'google') {
        console.log('Reverting to Google model due to error');
        setModel('google');
        try {
          await mcpService.setModel('google');
        } catch (setError) {
          console.error('Error setting model to Google:', setError);
        }
      }
    } finally {
      setLoading(false);
    }
  };

  // Get the selected model details
  const selectedModel = availableModels.find(m => m.id === model) || availableModels.find(m => m.id === 'google');

  return (
    <Box sx={{ position: 'relative' }}>
      <FormControl fullWidth size="small" variant="outlined" sx={{ m: 0, p: 0 }}>
        <Select
          id="model-select"
          value={model}
          onChange={handleModelChange}
          disabled={loading}
          sx={{
            color: 'white',
            '.MuiOutlinedInput-notchedOutline': { border: 'none' },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': { border: 'none' },
            '&:hover .MuiOutlinedInput-notchedOutline': { border: 'none' },
            '.MuiSvgIcon-root': { color: 'white' },
            opacity: loading ? 0.7 : 1
          }}
          renderValue={(selected) => (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Chip
                avatar={
                  <Avatar sx={{
                    bgcolor: loading ? 'rgba(255, 255, 255, 0.2)' : 'secondary.main',
                    animation: loading ? 'pulse 1.5s infinite' : 'none',
                    '@keyframes pulse': {
                      '0%': { opacity: 0.6 },
                      '50%': { opacity: 1 },
                      '100%': { opacity: 0.6 }
                    }
                  }}>
                    {selectedModel?.icon || '🔍'}
                  </Avatar>
                }
                label={loading ? 'Loading...' : (selectedModel?.name || 'Google Gemini')}
                variant="outlined"
                sx={{
                  color: 'white',
                  borderColor: error ? 'error.light' : 'rgba(255, 255, 255, 0.3)',
                  backgroundColor: error ? 'rgba(255, 0, 0, 0.1)' : 'transparent',
                  '& .MuiChip-label': { px: 1 }
                }}
              />
            </Box>
          )}
          MenuProps={{
            PaperProps: {
              sx: {
                bgcolor: 'background.paper',
                boxShadow: 5
              }
            }
          }}
        >
          {availableModels.map((modelOption) => (
            <MenuItem
              key={modelOption.id}
              value={modelOption.id}
              disabled={!modelOption.available}
              sx={{
                opacity: modelOption.available ? 1 : 0.5,
                position: 'relative'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                <Avatar sx={{
                  mr: 1,
                  width: 24,
                  height: 24,
                  fontSize: '0.8rem',
                  bgcolor: modelOption.available ? 'primary.light' : 'grey.300'
                }}>
                  {modelOption.icon}
                </Avatar>
                <Box sx={{ flexGrow: 1 }}>
                  {modelOption.name}
                </Box>
                {modelOption.id === model && (
                  <Box
                    component="span"
                    sx={{
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      bgcolor: 'success.main',
                      ml: 1
                    }}
                  />
                )}
              </Box>
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {error && (
        <Box
          sx={{
            position: 'absolute',
            bottom: -20,
            left: 0,
            right: 0,
            color: 'error.light',
            fontSize: '0.7rem',
            textAlign: 'center',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis'
          }}
        >
          {error}
        </Box>
      )}
    </Box>
  );
};

export default ModelSelector;
