import React from 'react';
import {
  Box,
  Typography,
  Avatar,
  Divider,
  Paper
} from '@mui/material';
import {
  SmartToy as BotIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import SensorChart from './SensorChart';
import DataVisualizer from './DataVisualizer';
import UniversalVisualizer from './UniversalVisualizer';

/**
 * Component for rendering a single chat message
 */
const ChatMessage = ({ message }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start',
        mb: 2
      }}
    >
      {message.sender === 'agent' && (
        <Avatar sx={{ mr: 1, bgcolor: 'primary.main' }}>
          <BotIcon />
        </Avatar>
      )}

      <Box
        sx={{
          maxWidth: { xs: '100%', sm: '95%' },
          minWidth: 120,
          width: 'fit-content',
          p: 2,
          borderRadius: 2,
          backgroundColor: message.sender === 'user'
            ? 'primary.main'
            : message.type === 'error'
              ? '#ffebee'
              : message.type === 'loading'
                ? 'rgba(0, 0, 0, 0.02)'
                : 'white',
          color: message.sender === 'user' ? 'white' : 'text.primary',
          boxShadow: 1,
          position: 'relative',
          overflow: 'hidden',
          ...(message.type === 'loading' && {
            '&::after': {
              content: '""',
              position: 'absolute',
              bottom: 0,
              left: 0,
              height: '2px',
              width: '100%',
              background: 'linear-gradient(90deg, transparent, primary.main, transparent)',
              animation: 'loading 1.5s infinite',
            },
            '@keyframes loading': {
              '0%': { transform: 'translateX(-100%)' },
              '100%': { transform: 'translateX(100%)' }
            }
          })
        }}
      >
        {/* Loading state */}
        {message.type === 'loading' ? (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              backgroundColor: 'primary.main',
              animation: 'pulse 1s infinite',
              mr: 1,
              '@keyframes pulse': {
                '0%': { opacity: 0.3, transform: 'scale(1)' },
                '50%': { opacity: 1, transform: 'scale(1.2)' },
                '100%': { opacity: 0.3, transform: 'scale(1)' }
              }
            }} />
            <Box sx={{
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              backgroundColor: 'primary.main',
              animation: 'pulse 1s infinite 0.2s',
              mr: 1
            }} />
            <Box sx={{
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              backgroundColor: 'primary.main',
              animation: 'pulse 1s infinite 0.4s',
              mr: 1
            }} />
            <Typography variant="body1" sx={{ ml: 1 }}>
              {message.content}
            </Typography>
          </Box>
        ) : (
          /* Regular text content */
          message.content && message.content.trim() !== 'No answer could be generated for your query.' && (
            <Typography
              variant="body1"
              component="div"
              sx={{
                whiteSpace: 'pre-line',
                '& code': {
                  backgroundColor: 'rgba(0, 0, 0, 0.05)',
                  padding: '2px 4px',
                  borderRadius: '4px',
                  fontFamily: 'monospace',
                  fontSize: '0.9em'
                },
                '& pre': {
                  backgroundColor: 'rgba(0, 0, 0, 0.05)',
                  padding: '8px 12px',
                  borderRadius: '4px',
                  overflowX: 'auto',
                  fontFamily: 'monospace',
                  fontSize: '0.9em',
                  margin: '8px 0'
                },
                '& a': {
                  color: 'primary.main',
                  textDecoration: 'none',
                  '&:hover': {
                    textDecoration: 'underline'
                  }
                },
                '& ul, & ol': {
                  paddingLeft: '20px',
                  margin: '8px 0'
                },
                '& table': {
                  borderCollapse: 'collapse',
                  width: '100%',
                  margin: '12px 0'
                },
                '& th, & td': {
                  border: '1px solid rgba(0, 0, 0, 0.1)',
                  padding: '8px 12px',
                  textAlign: 'left'
                },
                '& th': {
                  backgroundColor: 'rgba(0, 0, 0, 0.05)'
                },
                '& h1, & h2, & h3, & h4, & h5, & h6': {
                  margin: '16px 0 8px 0',
                  fontWeight: 'bold',
                  color: message.type === 'error' ? 'error.main' : 'inherit'
                },
                '& h1': { fontSize: '1.5em' },
                '& h2': { fontSize: '1.3em' },
                '& h3': { fontSize: '1.1em' },
                '& strong': {
                  fontWeight: 'bold',
                  color: message.type === 'error' ? 'error.dark' : 'inherit'
                }
              }}
            >
              {message.content}
            </Typography>
          )
        )}

        {/* Render data visualization if data or toolResults are present */}
        {(message.data || (Array.isArray(message.toolResults) && message.toolResults.length > 0)) && (
          <Box sx={{ mt: 2 }}>
            <UniversalVisualizer response={message} />
          </Box>
        )}

        {/* Render legacy chart if type is 'legacy-chart' */}
        {message.type === 'legacy-chart' && message.data && (
          <Box sx={{ mt: 2, height: 300 }}>
            <SensorChart
              sensorType={message.chartConfig?.sensorType || 'sensor'}
              timeRange={message.chartConfig?.timeRange || '1h'}
              data={message.data}
              title={message.chartConfig?.title || 'Sensor Data'}
              color={message.chartConfig?.color || '#8884d8'}
            />
          </Box>
        )}

        <Typography
          variant="caption"
          sx={{
            display: 'block',
            mt: 1,
            color: message.sender === 'user' ? 'rgba(255,255,255,0.7)' : 'text.secondary'
          }}
        >
          {message.timestamp.toLocaleTimeString()}
        </Typography>
      </Box>

      {message.sender === 'user' && (
        <Avatar sx={{ ml: 1, bgcolor: 'secondary.main' }}>
          <PersonIcon />
        </Avatar>
      )}
    </Box>
  );
};

export default ChatMessage;
