import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ton, Box } from '@mui/material';
import DataVisualizer from './DataVisualizer';

// Simple CSV parser for this use case
function parseCsvToArray(csv) {
  if (!csv || typeof csv !== 'string') return csv;
  const lines = csv.split('\n').filter(line => line.trim());
  if (lines.length < 2) return csv;
  const headers = lines[0].split(',');
  return lines.slice(1).map(line => {
    const values = line.split(',');
    const obj = {};
    headers.forEach((h, i) => { obj[h] = values[i]; });
    return obj;
  });
}

const UniversalVisualizer = ({ response }) => {
  if (!response) return <Alert severity="info">No data to display.</Alert>;

  const { type, content, data, chartConfig, meta } = response;

  // Always try to parse CSV if data is a string and looks like CSV
  let parsedData = data;
  if (typeof data === 'string' && data.includes(',') && data.includes('\n')) {
    const arr = parseCsvToArray(data);
    if (Array.isArray(arr) && arr.length > 0 && typeof arr[0] === 'object') {
      parsedData = arr;
    }
  }

  try {
    // Always use DataVisualizer if we have parsedData as array
    if (Array.isArray(parsedData) && parsedData.length > 0 && typeof parsedData[0] === 'object') {
      return <DataVisualizer data={parsedData} title={chartConfig?.title || 'Data Visualization'} />;
    }
    switch (type) {
      case 'json':
        return <pre>{JSON.stringify(data, null, 2)}</pre>;
      case 'text':
        return <Typography sx={{ whiteSpace: 'pre-line' }}>{content || data}</Typography>;
      case 'error':
        return <Alert severity="error">{content || 'An error occurred.'}</Alert>;
      default:
        return (
          <Box>
            <Alert severity="warning">Unknown data type. Showing raw data.</Alert>
            <pre>{typeof data === 'string' ? data : JSON.stringify(data, null, 2)}</pre>
            <Button
              variant="outlined"
              size="small"
              href={URL.createObjectURL(new Blob([typeof data === 'string' ? data : JSON.stringify(data)]))}
              download="raw-data.txt"
            >
              Download Raw Data
            </Button>
          </Box>
        );
    }
  } catch (e) {
    return <Alert severity="error">Error rendering data: {e.message}</Alert>;
  }
};

export default UniversalVisualizer; 