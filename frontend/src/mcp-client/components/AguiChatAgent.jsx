import React, { useState, useRef } from 'react';
import { Box, TextField, Button, Paper, Typography, Avatar, CircularProgress } from '@mui/material';
import { Send as SendIcon, SmartToy as BotIcon, Person as PersonIcon } from '@mui/icons-material';
import aguiService from '../services/aguiService';

const AguiChatAgent = () => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      sender: 'agent',
      content: 'Hello! I am your AG-UI agent. Ask me anything about your data or system.',
      type: 'text',
      timestamp: new Date()
    }
  ]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [streamBuffer, setStreamBuffer] = useState('');
  const messagesEndRef = useRef(null);
  const [subscription, setSubscription] = useState(null);

  // Scroll to bottom on new message
  React.useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, streamBuffer]);

  const handleSend = () => {
    if (!input.trim()) return;
    setLoading(true);
    setStreamBuffer('');
    // Add user message
    const userMessage = {
      id: Date.now(),
      sender: 'user',
      content: input,
      type: 'text',
      timestamp: new Date()
    };
    setMessages(prev => [...prev, userMessage]);
    // Start AG-UI agent run
    const unsub = aguiService.sendPromptToAguiAgent(input, {
      onEvent: (event) => {
        if (event.type === 'TEXT_MESSAGE_CONTENT') {
          setStreamBuffer(prev => prev + (event.delta || ''));
        } else if (event.type === 'TEXT_MESSAGE_END') {
          setMessages(prev => [...prev, {
            id: Date.now() + 1,
            sender: 'agent',
            content: streamBuffer,
            type: 'text',
            timestamp: new Date()
          }]);
          setStreamBuffer('');
        } else if (event.type === 'TEXT_MESSAGE_START') {
          setStreamBuffer('');
        } else if (event.type === 'TOOL_CALL_CHUNK') {
          setStreamBuffer(prev => prev + `\n[Tool call: ${event.toolCallName}]`);
        }
      },
      onError: (err) => {
        setMessages(prev => [...prev, {
          id: Date.now() + 2,
          sender: 'agent',
          content: `Error: ${err.message || err}`,
          type: 'error',
          timestamp: new Date()
        }]);
        setLoading(false);
      },
      onComplete: () => {
        setLoading(false);
      }
    });
    setSubscription(() => unsub);
    setInput('');
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <Paper elevation={3} sx={{ height: '70vh', display: 'flex', flexDirection: 'column', borderRadius: 2, overflow: 'hidden' }}>
      {/* Chat header */}
      <Box sx={{ p: 1, backgroundColor: 'primary.main', color: 'white', display: 'flex', alignItems: 'center', minHeight: 44, height: 44 }}>
        <Avatar sx={{ mr: 1, width: 28, height: 28, bgcolor: 'secondary.main', fontSize: 18 }}>
          <BotIcon fontSize="small" />
        </Avatar>
        <Typography variant="subtitle1" sx={{ fontSize: '1rem', fontWeight: 500 }}>
          AG-UI Assistant
        </Typography>
      </Box>
      {/* Messages area */}
      <Box sx={{ flexGrow: 1, p: 2, overflowY: 'auto', backgroundColor: '#f5f5f5' }}>
        {messages.map((message) => (
          <Box key={message.id} sx={{ display: 'flex', justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start', mb: 2 }}>
            {message.sender === 'agent' && (
              <Avatar sx={{ mr: 1, bgcolor: 'primary.main' }}><BotIcon /></Avatar>
            )}
            <Box sx={{ maxWidth: { xs: '100%', sm: '95%' }, minWidth: 120, width: 'fit-content', p: 2, borderRadius: 2, backgroundColor: message.sender === 'user' ? 'primary.main' : message.type === 'error' ? '#ffebee' : 'white', color: message.sender === 'user' ? 'white' : 'text.primary', boxShadow: 1 }}>
              <Typography variant="body1" sx={{ whiteSpace: 'pre-line' }}>{message.content}</Typography>
            </Box>
          </Box>
        ))}
        {/* Streaming buffer */}
        {loading && streamBuffer && (
          <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>
            <Avatar sx={{ mr: 1, bgcolor: 'primary.main' }}><BotIcon /></Avatar>
            <Box sx={{ maxWidth: { xs: '100%', sm: '95%' }, minWidth: 120, width: 'fit-content', p: 2, borderRadius: 2, backgroundColor: 'white', color: 'text.primary', boxShadow: 1 }}>
              <Typography variant="body1" sx={{ whiteSpace: 'pre-line' }}>{streamBuffer}</Typography>
              <CircularProgress size={16} sx={{ ml: 1, verticalAlign: 'middle' }} />
            </Box>
          </Box>
        )}
        <div ref={messagesEndRef} />
      </Box>
      {/* Input area */}
      <Box sx={{ p: 2, backgroundColor: 'background.paper', borderTop: '1px solid', borderColor: 'divider', display: 'flex' }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Ask anything..."
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={handleKeyDown}
          disabled={loading}
          size="small"
          sx={{ mr: 1 }}
        />
        <Button
          variant="contained"
          color="primary"
          endIcon={loading ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
          onClick={handleSend}
          disabled={loading || !input.trim()}
        >
          Send
        </Button>
      </Box>
    </Paper>
  );
};

export default AguiChatAgent; 