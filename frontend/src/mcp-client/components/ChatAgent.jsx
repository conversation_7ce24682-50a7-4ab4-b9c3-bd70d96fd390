import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Paper,
  Typography,
  Avatar,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  Send as SendIcon,
  SmartToy as BotIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import processPrompt from '../services/promptProcessor';
import ChatMessage from './ChatMessage';
import ModelSelector from './ModelSelector';

/**
 * Chat interface for interacting with the Digital Twin through natural language
 */
const ChatAgent = () => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      sender: 'agent',
      content: 'Hello! I can help you access sensor data from your Digital Twin. Try asking something like "Show me temperature data from the last hour" or "List all available sensors."',
      type: 'text',
      timestamp: new Date()
    }
  ]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const messagesEndRef = useRef(null);

  // Auto-scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSend = async () => {
    if (!input.trim()) return;

    // Add user message
    const userMessage = {
      id: Date.now(),
      sender: 'user',
      content: input,
      type: 'text',
      timestamp: new Date()
    };

    // Store the input before clearing it
    const userQuery = input;

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setLoading(true);

    // Add a temporary loading message
    const loadingMessageId = Date.now() + 1;
    setMessages(prev => [...prev, {
      id: loadingMessageId,
      sender: 'agent',
      content: 'Thinking...',
      type: 'loading',
      timestamp: new Date()
    }]);

    try {
      console.log(`Processing prompt: "${userQuery}"`);

      // Process the prompt
      const response = await processPrompt(userQuery);
      console.log('Prompt processing response:', response);

      // Log the response details for debugging
      console.log('Response type:', response.type);
      console.log('Response has data:', !!response.data);
      console.log('Response has chartConfig:', !!response.chartConfig);

      // Create the message object
      const messageObj = {
        id: Date.now() + 2,
        sender: 'agent',
        content: response.content,
        type: response.type,
        timestamp: new Date()
      };

      // Add data and chartConfig if they exist
      if (response.data) {
        messageObj.data = response.data;
      }

      if (response.chartConfig) {
        messageObj.chartConfig = response.chartConfig;
      }

      console.log('Final message object:', messageObj);

      // Remove the loading message and add the real response
      setMessages(prev => prev.filter(msg => msg.id !== loadingMessageId).concat(messageObj));
    } catch (error) {
      console.error('Error processing prompt:', error);
      console.error('Error details:', {
        message: error.message,
        stack: error.stack
      });

      // Extract error message from response if available
      let errorMessage = error.message;
      let errorDetails = '';

      if (error.response && error.response.data) {
        if (error.response.data.error) {
          errorMessage = error.response.data.error;
        }
        if (error.response.data.details) {
          errorDetails = error.response.data.details;
        }
      }

      // Format the error message
      const formattedErrorMessage = `
## Sorry, I encountered an error

**Error:** ${errorMessage}

${errorDetails ? `**Details:** ${errorDetails}` : ''}

### Suggestions:
- Try rephrasing your question
- Check if you're asking about data that exists
- Try a different time range (e.g., "last hour" or "last day")
- Make sure the MCP server is running properly
      `;

      // Remove the loading message and add the error message
      setMessages(prev => prev.filter(msg => msg.id !== loadingMessageId).concat({
        id: Date.now() + 2,
        sender: 'agent',
        content: formattedErrorMessage,
        type: 'error',
        timestamp: new Date()
      }));
    } finally {
      setLoading(false);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <Paper
      elevation={3}
      sx={{
        height: '70vh',
        display: 'flex',
        flexDirection: 'column',
        borderRadius: 2,
        overflow: 'hidden'
      }}
    >
      {/* Chat header */}
      <Box sx={{
        p: 1,
        backgroundColor: 'primary.main',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        minHeight: 44,
        height: 44,
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Avatar sx={{ mr: 1, width: 28, height: 28, bgcolor: 'secondary.main', fontSize: 18 }}>
            <BotIcon fontSize="small" />
          </Avatar>
          <Typography variant="subtitle1" sx={{ fontSize: '1rem', fontWeight: 500 }}>
            Digital Twin Assistant
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{ backgroundColor: 'rgba(255,255,255,0.10)', p: 0.5, borderRadius: 1, minWidth: 100 }}>
            <ModelSelector size="small" />
          </Box>
        </Box>
      </Box>

      {/* Messages area */}
      <Box sx={{
        flexGrow: 1,
        p: 2,
        overflowY: 'auto',
        backgroundColor: '#f5f5f5'
      }}>
        {messages.map((message) => (
          <ChatMessage key={message.id} message={message} />
        ))}
        <div ref={messagesEndRef} />
      </Box>

      {/* Input area */}
      <Box sx={{
        p: 2,
        backgroundColor: 'background.paper',
        borderTop: '1px solid',
        borderColor: 'divider',
        display: 'flex'
      }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Ask about your sensor data..."
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={handleKeyDown}
          disabled={loading}
          size="small"
          sx={{ mr: 1 }}
        />
        <Button
          variant="contained"
          color="primary"
          endIcon={loading ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
          onClick={handleSend}
          disabled={loading || !input.trim()}
        >
          Send
        </Button>
      </Box>
    </Paper>
  );
};

export default ChatAgent;
