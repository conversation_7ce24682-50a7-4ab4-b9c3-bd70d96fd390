import React from 'react';
import { useNavigate } from 'react-router-dom';
import { BiCog, BiCloudUpload } from 'react-icons/bi';
import { FaRocket } from 'react-icons/fa';
import { HiOutlineLightBulb } from 'react-icons/hi';
import { BiData } from 'react-icons/bi';
export default function Header() {
    const navigate = useNavigate();

    return (
        <header>
        <nav>
          <div className="logo" onClick={() => navigate('/')}>
            <BiCog className="logo-icon" />
            <span>VOMP</span>
          </div>
          <div className="nav-links">
            <a href="#features"><FaRocket className="nav-icon" /> Features</a>
            <a href="#how-it-works"><HiOutlineLightBulb className="nav-icon" /> How it Works</a>
            <a href="#contact"><BiData className="nav-icon" /> Solutions</a>
            <button className="login-btn" onClick={() => navigate('/Login')}><BiCloudUpload className="btn-icon" /> Login</button>
          </div>
        </nav>
        </header>
    );
}
