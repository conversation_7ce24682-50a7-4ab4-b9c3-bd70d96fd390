import React from 'react';
import { BiCog } from 'react-icons/bi';
import { useNavigate } from 'react-router-dom';

export default function Footer() {
    const navigate = useNavigate();
    return (
        <footer>
        <div className="footer-content">
          <div className="footer-info">
            <div className="footer-logo" onClick={() => navigate('/')}>
              <BiCog className="logo-icon" />
              <span>VOMP</span>
            </div>
            <p>Revolutionizing asset management through digital twin technology</p>
            <div className="social-icons">
            <a href="https://x.com/shaheemnaqvi" className="social-icon" target="_blank" rel="noopener noreferrer"><i className="fab fa-twitter"></i></a>
            <a href="https://linkedin.com/" className="social-icon" target="_blank" rel="noopener noreferrer"><i className="fab fa-linkedin"></i></a>
              <a href="https://github.com/ShaheemNaqvi" className="social-icon" target="_blank" rel="noopener noreferrer"><i className="fab fa-github"></i></a>
            </div>
          </div>
          <div className="footer-links">
            <div className="footer-column">
              <h4>Product</h4>
              <a href="#features">Features</a>
              <a href="#docs">Documentation</a>
              <a href="#updates">Updates</a>
            </div>
            <div className="footer-column">
              <h4>Company</h4>
              <a href="#about">About</a>
              <a href="#contact">Contact</a>
              <a href="#partners">Partners</a>
            </div>
            <div className="footer-column">
              <h4>Resources</h4>
              <a href="#blog">Blog</a>
              <a href="#support">Support</a>
              <a href="#community">Community</a>
            </div>
          </div>
        </div>
        <div className="footer-bottom">
          <p>&copy; 2025 Virtual Object Management Platform. All rights reserved.</p>
        </div>
      </footer>
    );
}
