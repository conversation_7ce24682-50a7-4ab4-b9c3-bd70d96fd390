/**
 * AG-UI Protocol Routes
 * Implements the Agent User Interaction Protocol endpoints
 */

const express = require('express');
const router = express.Router();
const llmService = require('../mcp/services/llmService');

/**
 * AG-UI Agent Query Endpoint
 * Processes queries using the AG-UI protocol with proper LLM integration
 * Flow: User Query → Google Gemini LLM → MCP Server → Standardized Response
 */
router.post('/query', async (req, res) => {
  try {
    const { query, agentId, threadId, tools, context } = req.body;

    if (!query) {
      return res.status(400).json({
        error: 'Query is required',
        code: 'MISSING_QUERY'
      });
    }

    console.log(`[AG-UI] Processing query for agent ${agentId}: "${query}"`);
    console.log(`[AG-UI] Using LLM: ${llmService.getActiveLLM()}`);

    // Process the query through LLM first, then MCP servers
    const llmResult = await llmService.processQuery(query);
    console.log(`[AG-UI] LLM Result:`, JSON.stringify(llmResult, null, 2));

    // Ensure content is always a string for AG-UI protocol compliance
    let safeContent = '';
    if (typeof llmResult.content === 'string') {
      safeContent = llmResult.content;
    } else if (typeof llmResult.content === 'object') {
      safeContent = JSON.stringify(llmResult.content, null, 2);
    } else {
      safeContent = String(llmResult.content || 'No response content');
    }

    // Ensure data is properly formatted
    let safeData = null;
    if (llmResult.data) {
      if (typeof llmResult.data === 'string') {
        safeData = llmResult.data;
      } else if (Array.isArray(llmResult.data)) {
        safeData = llmResult.data;
      } else if (typeof llmResult.data === 'object') {
        safeData = llmResult.data;
      }
    }

    // Format response according to AG-UI protocol specification
    const aguiResponse = {
      success: true,
      agentId: agentId || 'digital-twin-assistant',
      threadId: threadId || 'default-thread',
      timestamp: new Date().toISOString(),
      content: safeContent, // Always a string
      data: safeData, // Structured data if available
      toolResults: (llmResult.toolResults || []).map(tool => ({
        name: tool.name,
        result: tool.result,
        status: 'completed'
      })),
      metadata: {
        ...llmResult.metadata,
        protocolVersion: '1.0',
        processingTime: Date.now(),
        llmProvider: llmService.getActiveLLM(),
        queryProcessedAt: new Date().toISOString(),
        responseType: llmResult.type || 'text'
      }
    };

    console.log(`[AG-UI] Final response:`, JSON.stringify(aguiResponse, null, 2));
    res.json(aguiResponse);

  } catch (error) {
    console.error('[AG-UI] Error processing query:', error);

    res.status(500).json({
      success: false,
      error: error.message,
      code: error.code || 'PROCESSING_ERROR',
      timestamp: new Date().toISOString(),
      content: `Error: ${error.message}`, // Always provide string content
      data: null,
      toolResults: [],
      metadata: {
        protocolVersion: '1.0',
        errorType: error.name || 'UnknownError',
        llmProvider: llmService.getActiveLLM()
      }
    });
  }
});

/**
 * AG-UI Agent Status Endpoint
 * Returns current agent and system status
 */
router.get('/status', async (req, res) => {
  try {
    const systemStats = llmService.getSystemStats();

    const status = {
      success: true,
      timestamp: new Date().toISOString(),
      protocolVersion: '1.0',
      agent: {
        id: 'digital-twin-assistant',
        status: 'ready',
        capabilities: [
          'sensor-data-query',
          'real-time-monitoring',
          'data-visualization',
          'natural-language-processing'
        ],
        llmProvider: systemStats.activeLLM,
        availableLLMs: systemStats.availableLLMs
      },
      system: {
        mcpServers: systemStats.mcpServers,
        standardizerVersion: systemStats.standardizerVersion,
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage()
      },
      tools: [
        {
          name: 'query-data',
          description: 'Query sensor data from InfluxDB',
          status: 'available'
        },
        {
          name: 'get-status',
          description: 'Get system and sensor status',
          status: 'available'
        },
        {
          name: 'analyze-trends',
          description: 'Analyze data trends and patterns',
          status: 'available'
        }
      ]
    };

    res.json(status);

  } catch (error) {
    console.error('[AG-UI] Error getting status:', error);

    res.status(500).json({
      success: false,
      error: error.message,
      code: 'STATUS_ERROR',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * AG-UI Agent Configuration Endpoint
 * Returns agent configuration and capabilities
 */
router.get('/config', async (req, res) => {
  try {
    const config = {
      success: true,
      timestamp: new Date().toISOString(),
      protocolVersion: '1.0',
      agent: {
        id: 'digital-twin-assistant',
        name: 'Digital Twin Assistant',
        description: 'AI assistant for IoT sensor data analysis and monitoring',
        version: '1.0.0',
        capabilities: [
          'natural-language-query',
          'sensor-data-analysis',
          'real-time-monitoring',
          'data-visualization',
          'trend-analysis',
          'anomaly-detection'
        ],
        supportedEvents: [
          'RUN_STARTED',
          'RUN_FINISHED',
          'RUN_ERROR',
          'STEP_STARTED',
          'STEP_FINISHED',
          'TEXT_MESSAGE_START',
          'TEXT_MESSAGE_CONTENT',
          'TEXT_MESSAGE_END',
          'TOOL_CALL_START',
          'TOOL_CALL_ARGS',
          'TOOL_CALL_END',
          'STATE_SNAPSHOT',
          'STATE_DELTA',
          'MESSAGES_SNAPSHOT'
        ]
      },
      environment: {
        influxdb: {
          url: process.env.INFLUXDB_URL || 'http://localhost:8086',
          org: process.env.INFLUXDB_ORG || 'ICT',
          bucket: process.env.INFLUXDB_BUCKET || 'olive'
        },
        llm: {
          active: llmService.getActiveLLM(),
          available: ['google', 'anthropic']
        }
      }
    };

    res.json(config);

  } catch (error) {
    console.error('[AG-UI] Error getting config:', error);

    res.status(500).json({
      success: false,
      error: error.message,
      code: 'CONFIG_ERROR',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * AG-UI Tools Endpoint
 * Returns available tools and their schemas
 */
router.get('/tools', async (req, res) => {
  try {
    const mcpServerManager = llmService.getMCPServerManager();
    const allTools = mcpServerManager ? mcpServerManager.getAllTools() : [];

    const tools = {
      success: true,
      timestamp: new Date().toISOString(),
      protocolVersion: '1.0',
      tools: [
        {
          name: 'query-data',
          description: 'Query sensor data from InfluxDB using Flux query language',
          parameters: {
            type: 'object',
            properties: {
              query: {
                type: 'string',
                description: 'Flux query to execute'
              },
              timeRange: {
                type: 'string',
                description: 'Time range for the query (e.g., -1h, -1d)',
                default: '-1h'
              }
            },
            required: ['query']
          },
          returns: {
            type: 'object',
            properties: {
              data: {
                type: 'array',
                description: 'Query results'
              },
              metadata: {
                type: 'object',
                description: 'Query metadata'
              }
            }
          }
        },
        {
          name: 'get-status',
          description: 'Get current system and sensor status',
          parameters: {
            type: 'object',
            properties: {
              includeDetails: {
                type: 'boolean',
                description: 'Include detailed sensor information',
                default: false
              }
            }
          },
          returns: {
            type: 'object',
            properties: {
              sensors: {
                type: 'array',
                description: 'List of sensors and their status'
              },
              system: {
                type: 'object',
                description: 'System health information'
              }
            }
          }
        },
        ...allTools.map(tool => ({
          name: tool.name,
          description: tool.description,
          serverId: tool.serverId,
          serverName: tool.serverName,
          capabilities: tool.serverCapabilities
        }))
      ]
    };

    res.json(tools);

  } catch (error) {
    console.error('[AG-UI] Error getting tools:', error);

    res.status(500).json({
      success: false,
      error: error.message,
      code: 'TOOLS_ERROR',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * AG-UI Test Data Endpoint
 * Returns sample data for testing AG-UI data visualization
 */
router.post('/test-data', (req, res) => {
  try {
    const { dataType = 'temperature' } = req.body;

    // Generate sample CSV data
    const generateSampleData = (type) => {
      const now = new Date();
      const data = [];

      // CSV header
      data.push('timestamp,value,sensor,measurement,location');

      // Generate 20 sample data points
      for (let i = 0; i < 20; i++) {
        const timestamp = new Date(now.getTime() - (i * 5 * 60 * 1000)); // 5 minutes apart
        let value, sensor, measurement;

        switch (type) {
          case 'temperature':
            value = (20 + Math.random() * 10).toFixed(1); // 20-30°C
            sensor = 'temp_sensor_01';
            measurement = 'temperature';
            break;
          case 'humidity':
            value = (40 + Math.random() * 40).toFixed(1); // 40-80%
            sensor = 'humidity_sensor_01';
            measurement = 'humidity';
            break;
          case 'air_quality':
            value = Math.floor(50 + Math.random() * 100); // 50-150 AQI
            sensor = 'air_quality_sensor_01';
            measurement = 'air_quality';
            break;
          default:
            value = Math.random() * 100;
            sensor = 'generic_sensor';
            measurement = 'value';
        }

        data.push(`${timestamp.toISOString()},${value},${sensor},${measurement},lab_room_01`);
      }

      return data.join('\n');
    };

    const sampleData = generateSampleData(dataType);

    const response = {
      success: true,
      agentId: 'test-agent',
      threadId: 'test-thread',
      timestamp: new Date().toISOString(),
      content: `Here's sample ${dataType} data from your sensors. This demonstrates the AG-UI data visualization capabilities with table and chart views.`,
      data: null,
      toolResults: [
        {
          name: 'query-data',
          result: {
            content: [
              {
                type: 'text',
                text: sampleData
              }
            ]
          },
          status: 'completed'
        }
      ],
      metadata: {
        protocolVersion: '1.0',
        llmProvider: 'google',
        dataType: 'sample',
        sampleDataType: dataType,
        recordCount: 20
      }
    };

    res.json(response);
  } catch (error) {
    console.error('[AG-UI] Error generating test data:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      code: 'TEST_DATA_ERROR'
    });
  }
});

/**
 * AG-UI Health Check Endpoint
 */
router.get('/health', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    protocolVersion: '1.0',
    uptime: process.uptime()
  });
});

module.exports = router;
