/**
 * AG-UI Protocol Routes
 * Implements the Agent User Interaction Protocol endpoints
 */

const express = require('express');
const router = express.Router();
const llmService = require('../mcp/services/llmService');

/**
 * AG-UI Agent Query Endpoint - Server-Sent Events (SSE) Implementation
 * Implements proper AG-UI protocol with event streaming
 * Flow: User Query → Google Gemini LLM → MCP Server → AG-UI Events
 */
router.post('/query', async (req, res) => {
  try {
    // Parse AG-UI input format
    const { threadId, runId, messages, tools = [], context = [] } = req.body;

    // Extract the latest user message
    const userMessages = messages?.filter(m => m.role === 'user') || [];
    const latestMessage = userMessages[userMessages.length - 1];

    if (!latestMessage?.content) {
      return res.status(400).json({
        error: 'No user message found',
        code: 'MISSING_MESSAGE'
      });
    }

    const query = latestMessage.content;
    console.log(`[AG-UI] Processing query: "${query}"`);
    console.log(`[AG-UI] Thread: ${threadId}, Run: ${runId}`);
    console.log(`[AG-UI] Using LLM: ${llmService.getActiveLLM()}`);

    // Set up Server-Sent Events headers
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');

    // AG-UI Event Encoder
    const encodeEvent = (event) => {
      return `data: ${JSON.stringify(event)}\n\n`;
    };

    // Send RUN_STARTED event
    res.write(encodeEvent({
      type: 'RUN_STARTED',
      threadId,
      runId,
      timestamp: Date.now()
    }));

    // Generate message ID for assistant response
    const messageId = `msg_${Date.now()}`;

    // Send TEXT_MESSAGE_START event
    res.write(encodeEvent({
      type: 'TEXT_MESSAGE_START',
      messageId,
      role: 'assistant',
      timestamp: Date.now()
    }));

    try {
      // Process query through LLM and MCP
      const llmResult = await llmService.processQuery(query);
      console.log(`[AG-UI] LLM processing completed`);

      // Extract and standardize content
      let assistantContent = '';
      if (typeof llmResult.content === 'string') {
        assistantContent = llmResult.content;
      } else if (typeof llmResult.content === 'object') {
        assistantContent = JSON.stringify(llmResult.content, null, 2);
      } else {
        assistantContent = String(llmResult.content || 'No response available');
      }

      // Send tool call events if tools were used (BEFORE content streaming)
      if (llmResult.toolResults && llmResult.toolResults.length > 0) {
        for (const tool of llmResult.toolResults) {
          const toolCallId = `tool_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

          // TOOL_CALL_START
          res.write(encodeEvent({
            type: 'TOOL_CALL_START',
            toolCallId,
            toolCallName: tool.name,
            timestamp: Date.now()
          }));

          // TOOL_CALL_ARGS (if arguments exist)
          if (tool.arguments) {
            const argsString = JSON.stringify(tool.arguments);
            res.write(encodeEvent({
              type: 'TOOL_CALL_ARGS',
              toolCallId,
              delta: argsString,
              timestamp: Date.now()
            }));
          }

          // TOOL_CALL_END
          res.write(encodeEvent({
            type: 'TOOL_CALL_END',
            toolCallId,
            timestamp: Date.now()
          }));
        }
      }

      // Send STATE_SNAPSHOT with standardized data (BEFORE TEXT_MESSAGE_END)
      const stateSnapshot = {
        llmProvider: llmService.getActiveLLM(),
        queryProcessed: true,
        dataAvailable: !!(llmResult.data || llmResult.toolResults),
        toolResults: llmResult.toolResults || [],
        rawData: llmResult.data,
        metadata: llmResult.metadata
      };

      res.write(encodeEvent({
        type: 'STATE_SNAPSHOT',
        snapshot: stateSnapshot,
        timestamp: Date.now()
      }));

      // Stream content in chunks (AG-UI protocol)
      const chunkSize = 20; // Characters per chunk
      for (let i = 0; i < assistantContent.length; i += chunkSize) {
        const chunk = assistantContent.slice(i, i + chunkSize);

        res.write(encodeEvent({
          type: 'TEXT_MESSAGE_CONTENT',
          messageId,
          delta: chunk,
          timestamp: Date.now()
        }));

        // Small delay for realistic streaming
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      // Send TEXT_MESSAGE_END event (AFTER STATE_SNAPSHOT)
      res.write(encodeEvent({
        type: 'TEXT_MESSAGE_END',
        messageId,
        timestamp: Date.now()
      }));

      // Send RUN_FINISHED event
      res.write(encodeEvent({
        type: 'RUN_FINISHED',
        threadId,
        runId,
        timestamp: Date.now()
      }));

    } catch (processingError) {
      console.error('[AG-UI] Error during processing:', processingError);

      // Send error event
      res.write(encodeEvent({
        type: 'RUN_ERROR',
        message: processingError.message,
        code: processingError.code || 'PROCESSING_ERROR',
        timestamp: Date.now()
      }));
    }

    // End the response
    res.end();

  } catch (error) {
    console.error('[AG-UI] Error in query endpoint:', error);

    if (!res.headersSent) {
      res.status(500).json({
        error: error.message,
        code: error.code || 'ENDPOINT_ERROR',
        timestamp: new Date().toISOString()
      });
    }
  }
});

/**
 * AG-UI Agent Status Endpoint
 * Returns current agent and system status
 */
router.get('/status', async (req, res) => {
  try {
    const systemStats = llmService.getSystemStats();

    const status = {
      success: true,
      timestamp: new Date().toISOString(),
      protocolVersion: '1.0',
      agent: {
        id: 'digital-twin-assistant',
        status: 'ready',
        capabilities: [
          'sensor-data-query',
          'real-time-monitoring',
          'data-visualization',
          'natural-language-processing'
        ],
        llmProvider: systemStats.activeLLM,
        availableLLMs: systemStats.availableLLMs
      },
      system: {
        mcpServers: systemStats.mcpServers,
        standardizerVersion: systemStats.standardizerVersion,
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage()
      },
      tools: [
        {
          name: 'query-data',
          description: 'Query sensor data from InfluxDB',
          status: 'available'
        },
        {
          name: 'get-status',
          description: 'Get system and sensor status',
          status: 'available'
        },
        {
          name: 'analyze-trends',
          description: 'Analyze data trends and patterns',
          status: 'available'
        }
      ]
    };

    res.json(status);

  } catch (error) {
    console.error('[AG-UI] Error getting status:', error);

    res.status(500).json({
      success: false,
      error: error.message,
      code: 'STATUS_ERROR',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * AG-UI Agent Configuration Endpoint
 * Returns agent configuration and capabilities
 */
router.get('/config', async (req, res) => {
  try {
    const config = {
      success: true,
      timestamp: new Date().toISOString(),
      protocolVersion: '1.0',
      agent: {
        id: 'digital-twin-assistant',
        name: 'Digital Twin Assistant',
        description: 'AI assistant for IoT sensor data analysis and monitoring',
        version: '1.0.0',
        capabilities: [
          'natural-language-query',
          'sensor-data-analysis',
          'real-time-monitoring',
          'data-visualization',
          'trend-analysis',
          'anomaly-detection'
        ],
        supportedEvents: [
          'RUN_STARTED',
          'RUN_FINISHED',
          'RUN_ERROR',
          'STEP_STARTED',
          'STEP_FINISHED',
          'TEXT_MESSAGE_START',
          'TEXT_MESSAGE_CONTENT',
          'TEXT_MESSAGE_END',
          'TOOL_CALL_START',
          'TOOL_CALL_ARGS',
          'TOOL_CALL_END',
          'STATE_SNAPSHOT',
          'STATE_DELTA',
          'MESSAGES_SNAPSHOT'
        ]
      },
      environment: {
        influxdb: {
          url: process.env.INFLUXDB_URL || 'http://localhost:8086',
          org: process.env.INFLUXDB_ORG || 'ICT',
          bucket: process.env.INFLUXDB_BUCKET || 'olive'
        },
        llm: {
          active: llmService.getActiveLLM(),
          available: ['google', 'anthropic']
        }
      }
    };

    res.json(config);

  } catch (error) {
    console.error('[AG-UI] Error getting config:', error);

    res.status(500).json({
      success: false,
      error: error.message,
      code: 'CONFIG_ERROR',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * AG-UI Tools Endpoint
 * Returns available tools and their schemas
 */
router.get('/tools', async (req, res) => {
  try {
    const mcpServerManager = llmService.getMCPServerManager();
    const allTools = mcpServerManager ? mcpServerManager.getAllTools() : [];

    const tools = {
      success: true,
      timestamp: new Date().toISOString(),
      protocolVersion: '1.0',
      tools: [
        {
          name: 'query-data',
          description: 'Query sensor data from InfluxDB using Flux query language',
          parameters: {
            type: 'object',
            properties: {
              query: {
                type: 'string',
                description: 'Flux query to execute'
              },
              timeRange: {
                type: 'string',
                description: 'Time range for the query (e.g., -1h, -1d)',
                default: '-1h'
              }
            },
            required: ['query']
          },
          returns: {
            type: 'object',
            properties: {
              data: {
                type: 'array',
                description: 'Query results'
              },
              metadata: {
                type: 'object',
                description: 'Query metadata'
              }
            }
          }
        },
        {
          name: 'get-status',
          description: 'Get current system and sensor status',
          parameters: {
            type: 'object',
            properties: {
              includeDetails: {
                type: 'boolean',
                description: 'Include detailed sensor information',
                default: false
              }
            }
          },
          returns: {
            type: 'object',
            properties: {
              sensors: {
                type: 'array',
                description: 'List of sensors and their status'
              },
              system: {
                type: 'object',
                description: 'System health information'
              }
            }
          }
        },
        ...allTools.map(tool => ({
          name: tool.name,
          description: tool.description,
          serverId: tool.serverId,
          serverName: tool.serverName,
          capabilities: tool.serverCapabilities
        }))
      ]
    };

    res.json(tools);

  } catch (error) {
    console.error('[AG-UI] Error getting tools:', error);

    res.status(500).json({
      success: false,
      error: error.message,
      code: 'TOOLS_ERROR',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * AG-UI Test Data Endpoint
 * Returns sample data for testing AG-UI data visualization
 */
router.post('/test-data', (req, res) => {
  try {
    const { dataType = 'temperature' } = req.body;

    // Generate sample CSV data
    const generateSampleData = (type) => {
      const now = new Date();
      const data = [];

      // CSV header
      data.push('timestamp,value,sensor,measurement,location');

      // Generate 20 sample data points
      for (let i = 0; i < 20; i++) {
        const timestamp = new Date(now.getTime() - (i * 5 * 60 * 1000)); // 5 minutes apart
        let value, sensor, measurement;

        switch (type) {
          case 'temperature':
            value = (20 + Math.random() * 10).toFixed(1); // 20-30°C
            sensor = 'temp_sensor_01';
            measurement = 'temperature';
            break;
          case 'humidity':
            value = (40 + Math.random() * 40).toFixed(1); // 40-80%
            sensor = 'humidity_sensor_01';
            measurement = 'humidity';
            break;
          case 'air_quality':
            value = Math.floor(50 + Math.random() * 100); // 50-150 AQI
            sensor = 'air_quality_sensor_01';
            measurement = 'air_quality';
            break;
          default:
            value = Math.random() * 100;
            sensor = 'generic_sensor';
            measurement = 'value';
        }

        data.push(`${timestamp.toISOString()},${value},${sensor},${measurement},lab_room_01`);
      }

      return data.join('\n');
    };

    const sampleData = generateSampleData(dataType);

    const response = {
      success: true,
      agentId: 'test-agent',
      threadId: 'test-thread',
      timestamp: new Date().toISOString(),
      content: `Here's sample ${dataType} data from your sensors. This demonstrates the AG-UI data visualization capabilities with table and chart views.`,
      data: null,
      toolResults: [
        {
          name: 'query-data',
          result: {
            content: [
              {
                type: 'text',
                text: sampleData
              }
            ]
          },
          status: 'completed'
        }
      ],
      metadata: {
        protocolVersion: '1.0',
        llmProvider: 'google',
        dataType: 'sample',
        sampleDataType: dataType,
        recordCount: 20
      }
    };

    res.json(response);
  } catch (error) {
    console.error('[AG-UI] Error generating test data:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      code: 'TEST_DATA_ERROR'
    });
  }
});

/**
 * AG-UI Health Check Endpoint
 */
router.get('/health', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    protocolVersion: '1.0',
    uptime: process.uptime()
  });
});

module.exports = router;
