/**
 * AG-UI Protocol Routes
 * Implements the Agent User Interaction Protocol endpoints
 */

const express = require('express');
const router = express.Router();
const llmService = require('../mcp/services/llmService');

/**
 * AG-UI Agent Query Endpoint
 * Processes queries using the AG-UI protocol
 */
router.post('/query', async (req, res) => {
  try {
    const { query, agentId, threadId, tools, context } = req.body;

    if (!query) {
      return res.status(400).json({
        error: 'Query is required',
        code: 'MISSING_QUERY'
      });
    }

    console.log(`[AG-UI] Processing query for agent ${agentId}: "${query}"`);

    // Process the query using the enhanced LLM service
    const result = await llmService.processQuery(query);

    // Format response according to AG-UI protocol
    const aguiResponse = {
      success: true,
      agentId: agentId || 'digital-twin-assistant',
      threadId: threadId || 'default-thread',
      timestamp: new Date().toISOString(),
      content: result.content,
      data: result.data,
      toolResults: result.toolResults || [],
      metadata: {
        ...result.metadata,
        protocolVersion: '1.0',
        processingTime: Date.now(),
        llmProvider: llmService.getActiveLLM()
      }
    };

    console.log(`[AG-UI] Response generated successfully`);
    res.json(aguiResponse);

  } catch (error) {
    console.error('[AG-UI] Error processing query:', error);
    
    res.status(500).json({
      success: false,
      error: error.message,
      code: error.code || 'PROCESSING_ERROR',
      timestamp: new Date().toISOString(),
      metadata: {
        protocolVersion: '1.0',
        errorType: error.name || 'UnknownError'
      }
    });
  }
});

/**
 * AG-UI Agent Status Endpoint
 * Returns current agent and system status
 */
router.get('/status', async (req, res) => {
  try {
    const systemStats = llmService.getSystemStats();
    
    const status = {
      success: true,
      timestamp: new Date().toISOString(),
      protocolVersion: '1.0',
      agent: {
        id: 'digital-twin-assistant',
        status: 'ready',
        capabilities: [
          'sensor-data-query',
          'real-time-monitoring', 
          'data-visualization',
          'natural-language-processing'
        ],
        llmProvider: systemStats.activeLLM,
        availableLLMs: systemStats.availableLLMs
      },
      system: {
        mcpServers: systemStats.mcpServers,
        standardizerVersion: systemStats.standardizerVersion,
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage()
      },
      tools: [
        {
          name: 'query-data',
          description: 'Query sensor data from InfluxDB',
          status: 'available'
        },
        {
          name: 'get-status',
          description: 'Get system and sensor status',
          status: 'available'
        },
        {
          name: 'analyze-trends',
          description: 'Analyze data trends and patterns',
          status: 'available'
        }
      ]
    };

    res.json(status);

  } catch (error) {
    console.error('[AG-UI] Error getting status:', error);
    
    res.status(500).json({
      success: false,
      error: error.message,
      code: 'STATUS_ERROR',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * AG-UI Agent Configuration Endpoint
 * Returns agent configuration and capabilities
 */
router.get('/config', async (req, res) => {
  try {
    const config = {
      success: true,
      timestamp: new Date().toISOString(),
      protocolVersion: '1.0',
      agent: {
        id: 'digital-twin-assistant',
        name: 'Digital Twin Assistant',
        description: 'AI assistant for IoT sensor data analysis and monitoring',
        version: '1.0.0',
        capabilities: [
          'natural-language-query',
          'sensor-data-analysis',
          'real-time-monitoring',
          'data-visualization',
          'trend-analysis',
          'anomaly-detection'
        ],
        supportedEvents: [
          'RUN_STARTED',
          'RUN_FINISHED', 
          'RUN_ERROR',
          'STEP_STARTED',
          'STEP_FINISHED',
          'TEXT_MESSAGE_START',
          'TEXT_MESSAGE_CONTENT',
          'TEXT_MESSAGE_END',
          'TOOL_CALL_START',
          'TOOL_CALL_ARGS',
          'TOOL_CALL_END',
          'STATE_SNAPSHOT',
          'STATE_DELTA',
          'MESSAGES_SNAPSHOT'
        ]
      },
      environment: {
        influxdb: {
          url: process.env.INFLUXDB_URL || 'http://localhost:8086',
          org: process.env.INFLUXDB_ORG || 'ICT',
          bucket: process.env.INFLUXDB_BUCKET || 'olive'
        },
        llm: {
          active: llmService.getActiveLLM(),
          available: ['google', 'anthropic']
        }
      }
    };

    res.json(config);

  } catch (error) {
    console.error('[AG-UI] Error getting config:', error);
    
    res.status(500).json({
      success: false,
      error: error.message,
      code: 'CONFIG_ERROR',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * AG-UI Tools Endpoint
 * Returns available tools and their schemas
 */
router.get('/tools', async (req, res) => {
  try {
    const mcpServerManager = llmService.getMCPServerManager();
    const allTools = mcpServerManager ? mcpServerManager.getAllTools() : [];

    const tools = {
      success: true,
      timestamp: new Date().toISOString(),
      protocolVersion: '1.0',
      tools: [
        {
          name: 'query-data',
          description: 'Query sensor data from InfluxDB using Flux query language',
          parameters: {
            type: 'object',
            properties: {
              query: {
                type: 'string',
                description: 'Flux query to execute'
              },
              timeRange: {
                type: 'string',
                description: 'Time range for the query (e.g., -1h, -1d)',
                default: '-1h'
              }
            },
            required: ['query']
          },
          returns: {
            type: 'object',
            properties: {
              data: {
                type: 'array',
                description: 'Query results'
              },
              metadata: {
                type: 'object',
                description: 'Query metadata'
              }
            }
          }
        },
        {
          name: 'get-status',
          description: 'Get current system and sensor status',
          parameters: {
            type: 'object',
            properties: {
              includeDetails: {
                type: 'boolean',
                description: 'Include detailed sensor information',
                default: false
              }
            }
          },
          returns: {
            type: 'object',
            properties: {
              sensors: {
                type: 'array',
                description: 'List of sensors and their status'
              },
              system: {
                type: 'object',
                description: 'System health information'
              }
            }
          }
        },
        ...allTools.map(tool => ({
          name: tool.name,
          description: tool.description,
          serverId: tool.serverId,
          serverName: tool.serverName,
          capabilities: tool.serverCapabilities
        }))
      ]
    };

    res.json(tools);

  } catch (error) {
    console.error('[AG-UI] Error getting tools:', error);
    
    res.status(500).json({
      success: false,
      error: error.message,
      code: 'TOOLS_ERROR',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * AG-UI Health Check Endpoint
 */
router.get('/health', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    protocolVersion: '1.0',
    uptime: process.uptime()
  });
});

module.exports = router;
