/**
 * MCP Routes
 * This file defines the routes for the MCP API
 */
const express = require('express');
const router = express.Router();
const mcpControllers = require('../mcp/controllers');

// Process a natural language query
router.post('/query', mcpControllers.processQuery);

// Get available tools
router.get('/tools', mcpControllers.getTools);

// Get supported queries
router.get('/supported-queries', mcpControllers.getSupportedQueries);

// Get active LLM
router.get('/llm', mcpControllers.getActiveLLM);

// Set active LLM
router.post('/llm', mcpControllers.setActiveLLM);

module.exports = router;
