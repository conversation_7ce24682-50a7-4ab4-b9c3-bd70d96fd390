/**
 * LLM Service Layer for orchestration logic
 * Handles LLM query processing, function/tool calls, and response formatting
 * (Extracted from llm-processor.js)
 */

const mcpClient = require('../client');
const GoogleAdapter = require('./llmProviders/google');
const AnthropicAdapter = require('./llmProviders/anthropic');
const getSystemPrompt = require('../systemPrompt');

// Get configuration from environment variables
const INFLUXDB_ORG = process.env.INFLUXDB_ORG || 'ICT';
const INFLUXDB_BUCKET = process.env.INFLUXDB_BUCKET || 'test';
const GOOGLE_MODEL = process.env.GOOGLE_MODEL || 'gemini-2.0-flash';
const ANTHROPIC_MODEL = process.env.ANTHROPIC_MODEL || 'claude-3-5-sonnet-20241022';

// LLM client initialization (moved from legacy)
let googleAI = null;
if (process.env.GOOGLE_API_KEY) {
  try {
    const { GoogleGenerativeAI } = require('@google/generative-ai');
    googleAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
  } catch (error) {
    console.error('Error initializing Google Gemini client:', error.message);
  }
}

let anthropic = null;
if (process.env.ANTHROPIC_API_KEY) {
  try {
    const { Anthropic } = require('@anthropic-ai/sdk');
    anthropic = new Anthropic({ apiKey: process.env.ANTHROPIC_API_KEY });
  } catch (error) {
    console.error('Error initializing Anthropic client:', error.message);
  }
}

// Provider registry
const providers = {
  google: new GoogleAdapter(googleAI, GOOGLE_MODEL, getSystemPrompt),
  anthropic: new AnthropicAdapter(anthropic, ANTHROPIC_MODEL, getSystemPrompt),
};

// Module-level variable for active LLM
let activeLLM = 'google'; // Default to google

function getActiveLLM() {
  return activeLLM;
}

function setActiveLLM(llm) {
  if (llm !== 'google' && llm !== 'anthropic') {
    console.error(`Invalid LLM: ${llm}. Must be 'google' or 'anthropic'`);
    return false;
  }
  if (llm === 'google' && !process.env.GOOGLE_API_KEY) {
    console.error('Google API key is not set');
    return false;
  }
  if (llm === 'anthropic' && !process.env.ANTHROPIC_API_KEY) {
    console.error('Anthropic API key is not set');
    return false;
  }
  activeLLM = llm;
  return true;
}

function getActiveProvider() {
  return providers[activeLLM];
}

/**
 * Process a query using the active LLM adapter
 * @param {string} query - Natural language query
 * @returns {Promise<Object>} Processed response
 */
async function processQuery(query) {
  try {
    if (!mcpClient.isConnected) {
      throw new Error('MCP client is not connected');
    }
    const adapter = getActiveProvider();
    if (!adapter) {
      throw new Error('No active LLM adapter is available');
    }
    const llmResult = await adapter.processQuery(query, mcpClient);
    console.log('[LLM ADAPTER RESPONSE]', JSON.stringify(llmResult, null, 2));
    return llmResult;
  } catch (error) {
    return {
      type: 'error',
      content: `Error processing query: ${error.message}`,
      data: null,
      chartConfig: undefined,
      meta: {}
    };
  }
}

/**
 * Process function calls from LLM response
 * @param {Object} response - LLM response
 * @param {string} query - User query
 * @param {Array} finalText - Accumulated text
 * @param {Array} toolResults - Tool call results
 * @returns {Promise<Array>} Tool results
 */
async function processFunctionCalls(response, query, finalText, toolResults) {
  try {
    console.log('=== FULL LLM RESPONSE ===');
    console.log(JSON.stringify(response, null, 2));
    console.log('========================');
    const candidate = response.candidates[0];
    if (candidate && candidate.content && candidate.content.parts) {
      console.log('=== RESPONSE PARTS ===');
      console.log(JSON.stringify(candidate.content.parts, null, 2));
      console.log('=====================');
      const functionCallParts = candidate.content.parts.filter(
        part => part.functionCall
      );
      if (functionCallParts.length > 0) {
        for (const part of functionCallParts) {
          const functionCall = part.functionCall;
          const toolName = functionCall.name;
          const toolInput = functionCall.args || {};
          try {
            const result = await mcpClient.callTool(toolName, toolInput);
            toolResults.push({
              name: toolName,
              result
            });
            await processToolResult(query, toolName, toolInput, result, finalText);
          } catch (toolError) {
            finalText.push(`Error executing tool ${toolName}: ${toolError.message}`);
          }
        }
        return true;
      }
    }
    return false;
  } catch (error) {
    return false;
  }
}

/**
 * Process a tool result
 * @param {string} query - User query
 * @param {string} toolName - Tool name
 * @param {Object} toolInput - Tool input
 * @param {Object} result - Tool result
 * @param {Array} finalText - Accumulated text
 * @returns {Promise<void>}
 */
async function processToolResult(query, toolName, toolInput, result, finalText) {
  try {
    const resultContent = mcpClient.extractContent(result);
    if (query.toLowerCase().includes("list") && query.includes("sensor")) {
      try {
        const lines = resultContent.split('\n').filter(line => line.trim());
        const sensors = [];
        for (let i = 1; i < lines.length; i++) {
          const parts = lines[i].split(',');
          if (parts.length >= 9) {
            const sensorName = parts[8].trim();
            if (sensorName && !sensors.includes(sensorName)) {
              sensors.push(sensorName);
            }
          }
        }
        if (sensors.length > 0) {
          finalText.push(`I found ${sensors.length} sensors in your system:`);
          sensors.forEach(sensor => {
            let description = "";
            if (sensor.includes("temp")) {
              description = "Temperature sensor";
            } else if (sensor.includes("hum")) {
              description = "Humidity sensor";
            } else if (sensor.includes("air_quality")) {
              description = "Air quality sensor";
            } else if (sensor.includes("unknown")) {
              description = "Unknown sensor type";
            } else {
              description = "Sensor";
            }
            finalText.push(`- **${sensor}**: ${description}`);
          });
          finalText.push("\nYou can query data from these sensors by asking questions like:");
          finalText.push("- \"What's the current temperature?\"");
          finalText.push("- \"Show me humidity data from the last hour\"");
          finalText.push("- \"Get air quality readings for the past day\"");
          return;
        }
      } catch (parseError) {}
    }
    if (resultContent && resultContent.trim() && resultContent.trim() !== '\r\n') {
      if (toolName === 'query-data') {
        try {
          const lines = resultContent.split('\n').filter(line => line.trim());
          const headers = lines[0].split(',');
          const valueIndex = headers.findIndex(h => h.includes('_value'));
          const fieldIndex = headers.findIndex(h => h.includes('_field'));
          if (valueIndex >= 0 && lines.length > 1) {
            const values = [];
            for (let i = 1; i < lines.length; i++) {
              const parts = lines[i].split(',');
              if (parts.length > valueIndex) {
                values.push(parts[valueIndex]);
              }
            }
            let fieldName = "sensor";
            if (fieldIndex >= 0 && lines.length > 1) {
              const parts = lines[1].split(',');
              if (parts.length > fieldIndex) {
                fieldName = parts[fieldIndex];
              }
            }
            if (values.length === 1) {
              finalText.push(`The current ${fieldName} reading is ${values[0]}.`);
            } else if (values.length > 1) {
              const avg = values.reduce((sum, val) => sum + parseFloat(val), 0) / values.length;
              finalText.push(`I found ${values.length} ${fieldName} readings.`);
              finalText.push(`The average value is ${avg.toFixed(2)}.`);
              finalText.push(`The most recent reading is ${values[values.length - 1]}.`);
            }
            return;
          }
        } catch (parseError) {}
      }
      if (toolName !== 'query-data') {
        finalText.push(`Here are the results for your query about "${query}":`);
      }
    } else {
      finalText.push(`I queried for ${query} but didn't find any data matching your criteria. This could mean:\n1. There's no data in the specified time range\n2. The sensor or measurement name might be different\n3. The data might be in a different bucket\n\nYou might want to try:\n- Using a wider time range (e.g., -24h or -7d)\n- Checking the exact sensor name with \"List all available sensors\"\n- Specifying a different bucket if applicable`);
    }
  } catch (error) {
    finalText.push(`Error processing tool result: ${error.message}`);
  }
}

/**
 * Format the LLM response
 * @param {string} query - User query
 * @param {Array} finalText - Accumulated text
 * @param {Array} toolResults - Tool call results
 * @returns {Object} Formatted response
 */
function formatResponse(query, finalText, toolResults) {
  const queryToolResult = toolResults.find(result =>
    result.name === 'query-data' ||
    result.name === 'query-data_influxdb' ||
    result.name === 'write-data_influxdb'
  );
  let responseType = 'text';
  let responseData = null;
  let chartConfig = undefined;
  let meta = {};
  if (queryToolResult && queryToolResult.result && queryToolResult.result.content) {
    responseType = 'influxdb_data';
    if (typeof queryToolResult.result.content === 'string') {
      responseData = queryToolResult.result.content;
    } else if (Array.isArray(queryToolResult.result.content) && queryToolResult.result.content.length === 1 && queryToolResult.result.content[0].type === 'text') {
      responseData = queryToolResult.result.content[0].text;
    } else {
      responseData = JSON.stringify(queryToolResult.result.content);
    }
    let fieldName = 'data';
    const queryLower = query.toLowerCase();
    if (queryLower.includes('humidity') || queryLower.includes(' hum ') || queryLower.includes('hum')) {
      fieldName = 'hum';
    } else if (queryLower.includes('temperature') || queryLower.includes(' temp ') || queryLower.includes('temp')) {
      fieldName = 'temp';
    } else if (queryLower.includes('air quality')) {
      fieldName = 'esp32/air_quality';
    }
    let timeRange = '1h';
    if (queryLower.includes('day') || queryLower.includes('24h')) {
      timeRange = '24h';
    } else if (queryLower.includes('week')) {
      timeRange = '7d';
    } else if (queryLower.includes('month')) {
      timeRange = '30d';
    }
    chartConfig = {
        title: `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} Data`,
        sensorType: fieldName,
        timeRange: timeRange
    };
    meta = { tool: queryToolResult.name };
    }
  return {
    type: responseType,
    content: finalText.join('\n'),
    data: responseData,
    chartConfig,
    meta
  };
}

module.exports = {
  processQuery,
  processFunctionCalls,
  processToolResult,
  formatResponse,
  getSystemPrompt,
  providers, // Export for extensibility
  getActiveLLM,
  setActiveLLM
}; 