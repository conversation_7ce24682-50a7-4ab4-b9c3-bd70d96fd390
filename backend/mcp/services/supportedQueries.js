function getSupportedQueries() {
  return [
    {
      name: 'List sensors',
      examples: ['List all available sensors', 'Show me the sensors', 'What sensors are available?'],
      description: 'Lists all available sensors in the system'
    },
    {
      name: 'Temperature data',
      examples: ['Show me the temperature data', "What's the temperature?", 'Temperature for the past day'],
      description: 'Shows temperature data with optional time range (hour, day, week, month)'
    },
    {
      name: 'Humidity data',
      examples: ['Show me the humidity data', "What's the humidity?", 'Humidity for the past week'],
      description: 'Shows humidity data with optional time range (hour, day, week, month)'
    },
    {
      name: 'Pressure data',
      examples: ['Show me the pressure data', "What's the pressure?", 'Pressure for the past month'],
      description: 'Shows pressure data with optional time range (hour, day, week, month)'
    },
    {
      name: 'Help',
      examples: ['help', 'What can you do?', 'How to use this?'],
      description: 'Shows help information about supported queries'
    }
  ];
}

module.exports = getSupportedQueries; 