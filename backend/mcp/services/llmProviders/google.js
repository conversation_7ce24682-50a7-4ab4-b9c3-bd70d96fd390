const GoogleAdapter = class {
  constructor(googleAI, modelName, getSystemPrompt) {
    this.googleAI = googleAI;
    this.modelName = modelName;
    this.getSystemPrompt = getSystemPrompt;
  }

  async processQuery(query, toolClient) {
    if (!this.googleAI) {
      throw new Error('Google Gemini client is not initialized');
    }
    const tools = toolClient.getTools();
    const functionDeclarations = tools.map(tool => ({
      name: tool.name,
      description: tool.description || `${tool.name} tool for InfluxDB`,
      parameters: {
        type: "OBJECT",
        properties: Object.entries(tool.inputSchema.properties).reduce((acc, [key, value]) => {
          acc[key] = {
            type: value.type.toUpperCase(),
            description: value.description || `${key} parameter for ${tool.name}`
          };
          return acc;
        }, {}),
        required: tool.inputSchema.required
      }
    }));
    const googleTools = [{ functionDeclarations }];
    const functionCallingConfig = {
      mode: "ANY",
      allowedFunctionNames: tools.map(tool => tool.name)
    };
    const model = this.googleAI.getGenerativeModel({
      model: this.modelName,
      tools: googleTools,
      toolConfig: { functionCallingConfig }
    });
    const systemPrompt = this.getSystemPrompt();
    const prompt = `${systemPrompt}\n\nUser query: ${query}`;
    const result = await model.generateContent({
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 1000
      }
    });
    const response = result.response;
    const finalText = [];
    const toolResults = [];
    // Extract text from the response
    try {
      const responseText = response.text();
      if (responseText && responseText.trim()) {
        finalText.push(responseText);
      }
    } catch (error) {}
    // Handle tool calls (functionCall)
    const candidate = response.candidates && response.candidates[0];
    if (candidate && candidate.content && candidate.content.parts) {
      const functionCallParts = candidate.content.parts.filter(part => part.functionCall);
      for (const part of functionCallParts) {
        const functionCall = part.functionCall;
        const toolName = functionCall.name;
        const toolInput = functionCall.args || {};
        try {
          const result = await toolClient.callTool(toolName, toolInput);
          toolResults.push({ name: toolName, result });
        } catch (toolError) {
          finalText.push(`Error executing tool ${toolName}: ${toolError.message}`);
        }
      }
    }
    // Compose a conversational response
    let responseType = 'text';
    let responseData = null;
    let conversationalContent = finalText.join('\n');

    // Check if we have data from tools
    const queryDataResult = toolResults.find(r => r.name === 'query-data');
    if (queryDataResult && queryDataResult.result && queryDataResult.result.content) {
      responseData = queryDataResult.result.content;

      // If we have data but no conversational response, create one
      if (!conversationalContent || conversationalContent.trim() === '') {
        conversationalContent = this.generateConversationalResponse(query, responseData);
      }
    }

    // Ensure we always have some response
    if (!conversationalContent || conversationalContent.trim() === '') {
      conversationalContent = "I understand your question, but I wasn't able to retrieve the specific data you requested. Could you try rephrasing your question or check if the data source is available?";
    }

    return {
      type: responseType,
      content: conversationalContent,
      data: responseData,
      toolResults
    };
  }

  /**
   * Generate a conversational response when we have data but no LLM response
   */
  generateConversationalResponse(query, data) {
    const lowerQuery = query.toLowerCase();

    // Analyze the data to provide context
    let dataDescription = '';
    if (typeof data === 'string') {
      if (data.includes(',') && data.includes('\n')) {
        const lines = data.split('\n').filter(line => line.trim());
        dataDescription = `I found ${lines.length - 1} data points`;
      } else {
        dataDescription = 'Here\'s the data I retrieved';
      }
    } else if (Array.isArray(data)) {
      dataDescription = `I found ${data.length} data points`;
    } else {
      dataDescription = 'Here\'s the information I found';
    }

    // Create contextual responses based on query type
    if (lowerQuery.includes('temperature')) {
      return `${dataDescription} for temperature readings. The data shows the temperature measurements from your sensors.`;
    } else if (lowerQuery.includes('humidity')) {
      return `${dataDescription} for humidity levels. This shows how humidity has been changing in your environment.`;
    } else if (lowerQuery.includes('current') || lowerQuery.includes('latest')) {
      return `${dataDescription} showing the most recent readings from your sensors.`;
    } else if (lowerQuery.includes('trend') || lowerQuery.includes('over time')) {
      return `${dataDescription} showing the trends over the requested time period.`;
    } else if (lowerQuery.includes('sensor') || lowerQuery.includes('device')) {
      return `${dataDescription} from your sensor network. This includes the latest readings and status information.`;
    } else {
      return `${dataDescription} based on your query. You can view the data in different formats using the tabs below.`;
    }
  }
};

module.exports = GoogleAdapter;