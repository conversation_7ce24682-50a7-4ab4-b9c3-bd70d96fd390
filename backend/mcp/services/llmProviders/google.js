const GoogleAdapter = class {
  constructor(googleAI, modelName, getSystemPrompt) {
    this.googleAI = googleAI;
    this.modelName = modelName;
    this.getSystemPrompt = getSystemPrompt;
  }

  async processQuery(query, toolClient) {
    if (!this.googleAI) {
      throw new Error('Google Gemini client is not initialized');
    }
    const tools = toolClient.getTools();
    const functionDeclarations = tools.map(tool => ({
      name: tool.name,
      description: tool.description || `${tool.name} tool for InfluxDB`,
      parameters: {
        type: "OBJECT",
        properties: Object.entries(tool.inputSchema.properties).reduce((acc, [key, value]) => {
          acc[key] = {
            type: value.type.toUpperCase(),
            description: value.description || `${key} parameter for ${tool.name}`
          };
          return acc;
        }, {}),
        required: tool.inputSchema.required
      }
    }));
    const googleTools = [{ functionDeclarations }];
    const functionCallingConfig = {
      mode: "ANY",
      allowedFunctionNames: tools.map(tool => tool.name)
    };
    const model = this.googleAI.getGenerativeModel({
      model: this.modelName,
      tools: googleTools,
      toolConfig: { functionCallingConfig }
    });
    const systemPrompt = this.getSystemPrompt();
    const prompt = `${systemPrompt}\n\nUser query: ${query}`;
    const result = await model.generateContent({
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 1000
      }
    });
    const response = result.response;
    const finalText = [];
    const toolResults = [];
    // Extract text from the response
    try {
      const responseText = response.text();
      if (responseText && responseText.trim()) {
        finalText.push(responseText);
      }
    } catch (error) {}
    // Handle tool calls (functionCall)
    const candidate = response.candidates && response.candidates[0];
    if (candidate && candidate.content && candidate.content.parts) {
      const functionCallParts = candidate.content.parts.filter(part => part.functionCall);
      for (const part of functionCallParts) {
        const functionCall = part.functionCall;
        const toolName = functionCall.name;
        const toolInput = functionCall.args || {};
        try {
          const result = await toolClient.callTool(toolName, toolInput);
          toolResults.push({ name: toolName, result });
        } catch (toolError) {
          finalText.push(`Error executing tool ${toolName}: ${toolError.message}`);
        }
      }
    }
    // Compose a standard response
    let responseType = 'text';
    let responseData = null;
    const queryDataResult = toolResults.find(r => r.name === 'query-data');
    if (queryDataResult && queryDataResult.result && queryDataResult.result.content) {
      responseType = 'influxdb_data';
      if (typeof queryDataResult.result.content === 'string') {
        responseData = queryDataResult.result.content;
      } else if (Array.isArray(queryDataResult.result.content) && queryDataResult.result.content.length > 0) {
        const textPart = queryDataResult.result.content.find(part => part.type === 'text' && part.text);
        responseData = textPart ? textPart.text : JSON.stringify(queryDataResult.result.content);
      } else {
        responseData = JSON.stringify(queryDataResult.result.content);
      }
    }
    if (finalText.length === 0) {
      finalText.push('No answer could be generated for your query.');
    }
    return {
      type: responseType,
      content: finalText.join('\n'),
      data: responseData,
      toolResults
    };
  }
};

module.exports = GoogleAdapter; 