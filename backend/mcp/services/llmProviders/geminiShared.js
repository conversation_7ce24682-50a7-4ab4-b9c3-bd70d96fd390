const { GoogleGenerativeAI } = require('@google/generative-ai');

const GOOGLE_MODEL = process.env.GOOGLE_MODEL || 'gemini-1.5-flash';
let googleAI = null;

if (process.env.GOOGLE_API_KEY) {
  try {
    googleAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
  } catch (error) {
    console.error('Error initializing Google Gemini client:', error.message);
  }
}

module.exports = {
  googleAI,
  GOOGLE_MODEL
}; 