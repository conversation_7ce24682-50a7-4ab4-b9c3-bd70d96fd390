const AnthropicAdapter = class {
  constructor(anthropic, modelName, getSystemPrompt) {
    this.anthropic = anthropic;
    this.modelName = modelName;
    this.getSystemPrompt = getSystemPrompt;
  }

  /**
   * Process a query using Anthropic Claude
   * @param {string} query - Natural language query
   * @param {Object} toolClient - Tool client with getTools and callTool methods
   * @returns {Promise<Object>} Processed response
   */
  async processQuery(query, toolClient) {
    if (!this.anthropic) {
      throw new Error('Anthropic client is not initialized');
    }
    const systemPrompt = this.getSystemPrompt();
    const prompt = `${systemPrompt}\n\nUser query: ${query}`;
    const response = await this.anthropic.messages.create({
      model: this.modelName,
      max_tokens: 1000,
      messages: [
        { role: 'user', content: prompt }
      ]
    });
    const finalText = [];
    if (response && response.content) {
      if (typeof response.content === 'string') {
        finalText.push(response.content);
      } else if (Array.isArray(response.content)) {
        response.content.forEach(part => {
          if (typeof part === 'string') finalText.push(part);
          else if (part.text) finalText.push(part.text);
        });
      }
    }
    // Future: handle tool calls if Anthropic supports it
    let responseType = 'text';
    let responseData = null;
    const queryDataResult = (toolClient && toolClient.lastToolResults) ? toolClient.lastToolResults.find(r => r.name === 'query-data') : null;
    if (queryDataResult && queryDataResult.result && queryDataResult.result.content) {
      responseType = 'influxdb_data';
      if (typeof queryDataResult.result.content === 'string') {
        responseData = queryDataResult.result.content;
      } else if (Array.isArray(queryDataResult.result.content) && queryDataResult.result.content.length > 0) {
        const textPart = queryDataResult.result.content.find(part => part.type === 'text' && part.text);
        responseData = textPart ? textPart.text : JSON.stringify(queryDataResult.result.content);
      } else {
        responseData = JSON.stringify(queryDataResult.result.content);
      }
    }
    if (finalText.length === 0) {
      finalText.push('No answer could be generated for your query.');
    }
    return {
      type: responseType,
      content: finalText.join('\n'),
      data: responseData,
      toolResults: []
    };
  }
};

module.exports = AnthropicAdapter; 