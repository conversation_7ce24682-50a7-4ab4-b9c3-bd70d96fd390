/**
 * Bridge between the MCP client and application
 * This file provides a bridge to the MCP client
 */
const mcpClient = require('./client');
const llmService = require('./services/llmService');

/**
 * Initialize the MCP client
 * @returns {Promise<boolean>} Whether the initialization was successful
 */
async function initialize() {
  try {
    // Check if MCP_SERVER_PATH is set
    if (!process.env.MCP_SERVER_PATH) {
      throw new Error('MCP_SERVER_PATH environment variable is not set');
    }

    // Try to initialize the MCP client
    const success = await mcpClient.initialize();

    if (success) {
      console.log('MCP client initialized successfully');
      console.log('Available tools:', mcpClient.getTools().map(tool => tool.name).join(', '));
      return true;
    } else {
      console.error('MCP client initialization failed');
      return false;
    }
  } catch (error) {
    console.error('Error in bridge initialization:', error);
    return false;
  }
}

/**
 * Get the list of available tools
 * @returns {Array} List of tools
 */
function getTools() {
  // Ensure MCP client is connected
  if (!mcpClient.isConnected) {
    throw new Error('MCP client is not connected');
  }

  return mcpClient.getTools();
}

/**
 * Call a tool
 * @param {string} toolName - Name of the tool to call
 * @param {Object} params - Parameters for the tool
 * @returns {Promise<Object>} Result of the tool call
 */
async function callTool(toolName, params) {
  try {
    // Ensure MCP client is connected
    if (!mcpClient.isConnected) {
      throw new Error('MCP client is not connected');
    }

    console.log(`Calling tool ${toolName} with input:`, params);
    const result = await mcpClient.callTool(toolName, params);
    console.log(`Tool ${toolName} response:`, JSON.stringify(result).substring(0, 200) + '...');
    return result;
  } catch (error) {
    console.error(`Error calling tool ${toolName}:`, error);
    throw error;
  }
}

/**
 * Process a query using the LLM service layer
 * @param {string} query - Natural language query
 * @returns {Promise<Object>} Processed response
 */
async function processQuery(query) {
  try {
    // Ensure MCP client is connected
    if (!mcpClient.isConnected) {
      throw new Error('MCP client is not connected');
    }

    console.log(`Using MCP client to process query: "${query}"`);
    const result = await llmService.processQuery(query);
    
    return result;
  } catch (error) {
    console.error('Error processing query:', error);
    return {
      type: 'error',
      content: `Error processing query: ${error.message}`
    };
  }
}

/**
 * Get the active LLM
 * @returns {string} Active LLM ('google' or 'anthropic')
 */
function getActiveLLM() {
  return llmService.getActiveLLM();
}

/**
 * Set the active LLM
 * @param {string} llm - LLM to set as active ('google' or 'anthropic')
 * @returns {boolean} Whether the operation was successful
 */
function setActiveLLM(llm) {
  return llmService.setActiveLLM(llm);
}

/**
 * Close the MCP client
 * @returns {Promise<void>}
 */
async function close() {
  if (mcpClient.isConnected) {
    await mcpClient.close();
  }
}

// Export the functions
module.exports = {
  initialize,
  getTools,
  callTool,
  processQuery,
  getActiveLLM,
  setActiveLLM,
  close,
  client: mcpClient
};
