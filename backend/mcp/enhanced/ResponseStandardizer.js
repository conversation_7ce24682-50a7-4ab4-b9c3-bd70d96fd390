/**
 * Enhanced Response Standardizer
 * Standardizes responses from different MCP servers and LLM providers
 * Provides consistent data format and metadata enrichment
 */

const moment = require('moment');

class ResponseStandardizer {
  constructor() {
    this.dataTypeDetectors = new Map();
    this.formatters = new Map();
    this.validators = new Map();
    
    this.initializeDetectors();
    this.initializeFormatters();
    this.initializeValidators();
  }

  /**
   * Initialize data type detectors
   */
  initializeDetectors() {
    // CSV detector
    this.dataTypeDetectors.set('csv', (data) => {
      if (typeof data !== 'string') return false;
      const lines = data.split('\n').filter(line => line.trim());
      return lines.length > 1 && lines[0].includes(',');
    });

    // JSON detector
    this.dataTypeDetectors.set('json', (data) => {
      if (typeof data === 'object') return true;
      if (typeof data === 'string') {
        try {
          JSON.parse(data);
          return true;
        } catch (e) {
          return false;
        }
      }
      return false;
    });

    // Time series detector
    this.dataTypeDetectors.set('timeseries', (data) => {
      if (!Array.isArray(data)) return false;
      if (data.length === 0) return false;
      
      const firstItem = data[0];
      if (typeof firstItem !== 'object') return false;
      
      // Look for time-related fields
      const timeFields = ['time', 'timestamp', '_time', 'date', 'datetime'];
      return timeFields.some(field => field in firstItem);
    });

    // Tabular data detector
    this.dataTypeDetectors.set('tabular', (data) => {
      if (!Array.isArray(data)) return false;
      if (data.length === 0) return false;
      
      const firstItem = data[0];
      return typeof firstItem === 'object' && firstItem !== null;
    });

    // Numeric data detector
    this.dataTypeDetectors.set('numeric', (data) => {
      if (Array.isArray(data)) {
        return data.every(item => typeof item === 'number' || !isNaN(parseFloat(item)));
      }
      return typeof data === 'number' || !isNaN(parseFloat(data));
    });

    // Text detector
    this.dataTypeDetectors.set('text', (data) => {
      return typeof data === 'string' && !this.dataTypeDetectors.get('csv')(data);
    });
  }

  /**
   * Initialize data formatters
   */
  initializeFormatters() {
    // CSV formatter
    this.formatters.set('csv', (data) => {
      const lines = data.split('\n').filter(line => line.trim());
      if (lines.length < 2) return { type: 'text', data };

      const headers = lines[0].split(',').map(h => h.trim());
      const rows = lines.slice(1).map(line => {
        const values = line.split(',').map(v => v.trim());
        const obj = {};
        headers.forEach((header, index) => {
          obj[header] = values[index] || '';
        });
        return obj;
      });

      return {
        type: 'tabular',
        data: rows,
        metadata: {
          originalFormat: 'csv',
          rowCount: rows.length,
          columnCount: headers.length,
          columns: headers
        }
      };
    });

    // JSON formatter
    this.formatters.set('json', (data) => {
      let parsedData = data;
      if (typeof data === 'string') {
        try {
          parsedData = JSON.parse(data);
        } catch (e) {
          return { type: 'text', data };
        }
      }

      const metadata = {
        originalFormat: 'json',
        dataType: Array.isArray(parsedData) ? 'array' : typeof parsedData
      };

      if (Array.isArray(parsedData)) {
        metadata.itemCount = parsedData.length;
        if (parsedData.length > 0) {
          metadata.itemType = typeof parsedData[0];
          if (typeof parsedData[0] === 'object') {
            metadata.columns = Object.keys(parsedData[0]);
          }
        }
      }

      return {
        type: Array.isArray(parsedData) ? 'tabular' : 'object',
        data: parsedData,
        metadata
      };
    });

    // Time series formatter
    this.formatters.set('timeseries', (data) => {
      const timeFields = ['time', 'timestamp', '_time', 'date', 'datetime'];
      const timeField = timeFields.find(field => field in data[0]);

      const processedData = data.map(item => {
        const processed = { ...item };
        if (timeField && processed[timeField]) {
          // Standardize time format
          const time = moment(processed[timeField]);
          processed._standardTime = time.toISOString();
          processed._timestamp = time.valueOf();
        }
        return processed;
      });

      return {
        type: 'timeseries',
        data: processedData,
        metadata: {
          originalFormat: 'timeseries',
          timeField,
          dataPoints: processedData.length,
          timeRange: this.getTimeRange(processedData, timeField),
          columns: Object.keys(data[0])
        }
      };
    });

    // Tabular formatter
    this.formatters.set('tabular', (data) => {
      const columns = Object.keys(data[0]);
      const columnTypes = this.analyzeColumnTypes(data, columns);

      return {
        type: 'tabular',
        data,
        metadata: {
          originalFormat: 'tabular',
          rowCount: data.length,
          columnCount: columns.length,
          columns,
          columnTypes
        }
      };
    });

    // Numeric formatter
    this.formatters.set('numeric', (data) => {
      const numbers = Array.isArray(data) ? data : [data];
      const stats = this.calculateNumericStats(numbers);

      return {
        type: 'numeric',
        data: numbers,
        metadata: {
          originalFormat: 'numeric',
          count: numbers.length,
          statistics: stats
        }
      };
    });

    // Text formatter
    this.formatters.set('text', (data) => {
      const stats = this.analyzeText(data);

      return {
        type: 'text',
        data,
        metadata: {
          originalFormat: 'text',
          ...stats
        }
      };
    });
  }

  /**
   * Initialize validators
   */
  initializeValidators() {
    this.validators.set('tabular', (data) => {
      if (!Array.isArray(data)) return { valid: false, error: 'Data must be an array' };
      if (data.length === 0) return { valid: false, error: 'Data array is empty' };
      if (typeof data[0] !== 'object') return { valid: false, error: 'Array items must be objects' };
      return { valid: true };
    });

    this.validators.set('timeseries', (data) => {
      const tabularValidation = this.validators.get('tabular')(data);
      if (!tabularValidation.valid) return tabularValidation;

      const timeFields = ['time', 'timestamp', '_time', 'date', 'datetime'];
      const hasTimeField = timeFields.some(field => field in data[0]);
      if (!hasTimeField) {
        return { valid: false, error: 'Time series data must have a time field' };
      }

      return { valid: true };
    });

    this.validators.set('numeric', (data) => {
      const numbers = Array.isArray(data) ? data : [data];
      const allNumeric = numbers.every(item => typeof item === 'number' || !isNaN(parseFloat(item)));
      if (!allNumeric) {
        return { valid: false, error: 'All values must be numeric' };
      }
      return { valid: true };
    });
  }

  /**
   * Standardize a response from any source
   * @param {*} rawData - Raw response data
   * @param {Object} context - Additional context information
   * @returns {Object} Standardized response
   */
  standardizeResponse(rawData, context = {}) {
    try {
      // Detect data type
      const detectedType = this.detectDataType(rawData);
      
      // Format data according to detected type
      const formatted = this.formatData(rawData, detectedType);
      
      // Validate formatted data
      const validation = this.validateData(formatted.data, formatted.type);
      
      // Enrich with metadata
      const enriched = this.enrichMetadata(formatted, context);
      
      return {
        success: true,
        type: formatted.type,
        data: formatted.data,
        metadata: {
          ...formatted.metadata,
          ...enriched.metadata,
          validation,
          detectedType,
          processingTime: Date.now(),
          standardizerVersion: '1.0.0'
        }
      };

    } catch (error) {
      console.error('Error standardizing response:', error);
      return {
        success: false,
        type: 'error',
        data: null,
        error: error.message,
        metadata: {
          originalData: rawData,
          processingTime: Date.now(),
          standardizerVersion: '1.0.0'
        }
      };
    }
  }

  /**
   * Detect the type of data
   * @param {*} data - Data to analyze
   * @returns {string} Detected data type
   */
  detectDataType(data) {
    for (const [type, detector] of this.dataTypeDetectors) {
      if (detector(data)) {
        return type;
      }
    }
    return 'unknown';
  }

  /**
   * Format data according to its type
   * @param {*} data - Data to format
   * @param {string} type - Data type
   * @returns {Object} Formatted data
   */
  formatData(data, type) {
    const formatter = this.formatters.get(type);
    if (formatter) {
      return formatter(data);
    }
    
    return {
      type: 'unknown',
      data,
      metadata: { originalFormat: 'unknown' }
    };
  }

  /**
   * Validate formatted data
   * @param {*} data - Data to validate
   * @param {string} type - Data type
   * @returns {Object} Validation result
   */
  validateData(data, type) {
    const validator = this.validators.get(type);
    if (validator) {
      return validator(data);
    }
    
    return { valid: true, message: 'No validator available for this type' };
  }

  /**
   * Enrich metadata with additional context
   * @param {Object} formatted - Formatted data object
   * @param {Object} context - Additional context
   * @returns {Object} Enriched object
   */
  enrichMetadata(formatted, context) {
    const enrichedMetadata = {
      ...formatted.metadata,
      context,
      enrichedAt: new Date().toISOString()
    };

    // Add visualization suggestions
    enrichedMetadata.visualizationSuggestions = this.getVisualizationSuggestions(formatted);

    return {
      ...formatted,
      metadata: enrichedMetadata
    };
  }

  /**
   * Get visualization suggestions based on data type and structure
   * @param {Object} formatted - Formatted data object
   * @returns {Array} Visualization suggestions
   */
  getVisualizationSuggestions(formatted) {
    const suggestions = [];

    switch (formatted.type) {
      case 'timeseries':
        suggestions.push('line-chart', 'area-chart', 'time-series-table');
        break;
      case 'tabular':
        suggestions.push('data-table', 'bar-chart', 'scatter-plot');
        if (formatted.metadata.columnTypes) {
          const numericColumns = Object.entries(formatted.metadata.columnTypes)
            .filter(([col, type]) => type === 'numeric').length;
          if (numericColumns >= 2) {
            suggestions.push('correlation-matrix', 'multi-axis-chart');
          }
        }
        break;
      case 'numeric':
        suggestions.push('metric-card', 'gauge-chart', 'histogram');
        break;
      case 'text':
        suggestions.push('text-display', 'markdown-renderer');
        break;
    }

    return suggestions;
  }

  /**
   * Analyze column types in tabular data
   * @param {Array} data - Tabular data
   * @param {Array} columns - Column names
   * @returns {Object} Column type mapping
   */
  analyzeColumnTypes(data, columns) {
    const types = {};
    
    columns.forEach(column => {
      const values = data.slice(0, 10).map(row => row[column]).filter(val => val != null);
      
      if (values.length === 0) {
        types[column] = 'unknown';
        return;
      }

      // Check for dates
      const isDate = values.every(val => {
        if (typeof val === 'string') {
          return moment(val, moment.ISO_8601, true).isValid() || !isNaN(Date.parse(val));
        }
        return false;
      });

      if (isDate) {
        types[column] = 'date';
        return;
      }

      // Check for numbers
      const isNumeric = values.every(val => !isNaN(parseFloat(val)) && isFinite(val));
      if (isNumeric) {
        types[column] = 'numeric';
        return;
      }

      // Default to string
      types[column] = 'string';
    });

    return types;
  }

  /**
   * Calculate numeric statistics
   * @param {Array} numbers - Array of numbers
   * @returns {Object} Statistics
   */
  calculateNumericStats(numbers) {
    const validNumbers = numbers.map(n => parseFloat(n)).filter(n => !isNaN(n));
    
    if (validNumbers.length === 0) {
      return { count: 0, min: null, max: null, mean: null, median: null };
    }

    const sorted = validNumbers.sort((a, b) => a - b);
    const sum = validNumbers.reduce((a, b) => a + b, 0);
    
    return {
      count: validNumbers.length,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      mean: sum / validNumbers.length,
      median: sorted[Math.floor(sorted.length / 2)],
      sum
    };
  }

  /**
   * Analyze text content
   * @param {string} text - Text to analyze
   * @returns {Object} Text statistics
   */
  analyzeText(text) {
    const words = text.split(/\s+/).filter(word => word.length > 0);
    const lines = text.split('\n').length;
    const characters = text.length;

    return {
      wordCount: words.length,
      lineCount: lines,
      characterCount: characters,
      averageWordLength: words.length > 0 ? words.reduce((sum, word) => sum + word.length, 0) / words.length : 0
    };
  }

  /**
   * Get time range from time series data
   * @param {Array} data - Time series data
   * @param {string} timeField - Time field name
   * @returns {Object} Time range information
   */
  getTimeRange(data, timeField) {
    if (!data || data.length === 0) return null;

    const times = data.map(item => moment(item[timeField])).filter(time => time.isValid());
    if (times.length === 0) return null;

    const sorted = times.sort((a, b) => a.valueOf() - b.valueOf());
    
    return {
      start: sorted[0].toISOString(),
      end: sorted[sorted.length - 1].toISOString(),
      duration: sorted[sorted.length - 1].diff(sorted[0], 'milliseconds'),
      dataPoints: times.length
    };
  }
}

module.exports = ResponseStandardizer;
