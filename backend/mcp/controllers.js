/**
 * Controllers for MCP-related API endpoints
 */
const mcp = require('./index');

/**
 * Process a natural language query using the MCP client
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.processQuery = async (req, res) => {
  try {
    const { query } = req.body;

    if (!query) {
      return res.status(400).json({ error: 'Query is required' });
    }

    // Make sure the MCP client is initialized
    if (!mcp.client.isConnected) {
      await mcp.initialize();
    }

    // Process the query
    try {
      const result = await mcp.processQuery(query);
      res.json(result);
    } catch (error) {
      console.error('Error processing query:', error);

      // Return error response
      res.json({
        type: 'error',
        content: `Error processing query: ${error.message}. Please try a simpler query.`
      });
    }
  } catch (error) {
    console.error('Error processing query:', error);
    res.status(500).json({ error: error.message });
  }
};

/**
 * Get available MCP tools
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getTools = async (req, res) => {
  try {
    if (!mcp.client.isConnected) {
      await mcp.initialize();
    }

    const tools = mcp.getTools();

    res.json({
      connected: mcp.client.isConnected,
      tools: tools.map(tool => ({
        name: tool.name,
        description: tool.description || `${tool.name} tool`,
        inputSchema: tool.inputSchema
      })),
      activeLLM: mcp.getActiveLLM(),
      availableLLMs: {
        anthropic: !!process.env.ANTHROPIC_API_KEY,
        google: !!process.env.GOOGLE_API_KEY
      }
    });
  } catch (error) {
    console.error('Error getting MCP tools:', error);
    res.status(500).json({
      connected: false,
      error: error.message
    });
  }
};

/**
 * Get supported predefined queries
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSupportedQueries = async (req, res) => {
  try {
    // Get supported queries from the client
    const supportedQueries = mcp.client.getSupportedQueries();

    res.json({
      supportedQueries: supportedQueries
    });
  } catch (error) {
    console.error('Error getting supported queries:', error);
    res.status(500).json({
      error: error.message
    });
  }
};

/**
 * Initialize the MCP client
 * @returns {Promise<boolean>} Whether the initialization was successful
 */
exports.initializeMCPClient = async () => {
  try {
    return await mcp.initialize();
  } catch (error) {
    console.error('Failed to initialize MCP client:', error);
    return false;
  }
};

/**
 * Close the MCP client
 */
exports.closeMCPClient = async () => {
  try {
    await mcp.close();
  } catch (error) {
    console.error('Error closing MCP client:', error);
  }
};

/**
 * Get the active LLM
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getActiveLLM = async (req, res) => {
  try {
    // Get the active LLM
    const activeLLM = mcp.getActiveLLM();

    res.json({
      activeLLM,
      available: {
        anthropic: !!process.env.ANTHROPIC_API_KEY,
        google: !!process.env.GOOGLE_API_KEY
      }
    });
  } catch (error) {
    console.error('Error getting active LLM:', error);
    res.status(500).json({
      error: error.message
    });
  }
};

/**
 * Set the active LLM
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setActiveLLM = async (req, res) => {
  try {
    // Get the LLM from the request body (support both 'llm' and 'model' parameters)
    const llm = req.body.llm || req.body.model;

    if (!llm) {
      return res.status(400).json({ error: 'LLM name is required' });
    }

    if (llm !== 'anthropic' && llm !== 'google') {
      return res.status(400).json({ error: 'Invalid LLM name. Must be "anthropic" or "google"' });
    }

    // Check if the required API key is set
    if (llm === 'anthropic' && !process.env.ANTHROPIC_API_KEY) {
      return res.status(400).json({
        success: false,
        error: 'Anthropic API key is not set. Please set the ANTHROPIC_API_KEY environment variable.'
      });
    }

    if (llm === 'google' && !process.env.GOOGLE_API_KEY) {
      return res.status(400).json({
        success: false,
        error: 'Google API key is not set. Please set the GOOGLE_API_KEY environment variable.'
      });
    }

    // Set the active LLM
    const success = mcp.setActiveLLM(llm);

    res.json({
      success,
      activeLLM: success ? llm : mcp.getActiveLLM()
    });
  } catch (error) {
    console.error('Error setting active LLM:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};
