/**
 * MCP Module
 * This module provides access to the MCP client and related functionality
 */
const mcpClient = require('./client');
const mcpBridge = require('./bridge');
const llmService = require('./services/llmService');

// Export the modules
module.exports = {
  client: mcpClient,
  bridge: mcpBridge,
  llmService: llmService,

  // Export key functions directly for convenience
  initialize: mcpBridge.initialize,
  close: mcpBridge.close,
  getTools: mcpBridge.getTools,
  callTool: mcpBridge.callTool,
  processQuery: mcpBridge.processQuery,
  getActiveLLM: mcpBridge.getActiveLLM,
  setActiveLLM: mcpBridge.setActiveLLM
};
