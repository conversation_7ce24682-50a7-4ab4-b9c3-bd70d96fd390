[{"id": "f8b85561e970734e", "type": "tab", "label": "Activation", "disabled": false, "info": "", "env": []}, {"id": "3be9b1a60b182eeb", "type": "tab", "label": "Flow 1", "disabled": false, "info": "", "env": []}, {"id": "cac464ba.946338", "type": "tab", "label": "Registration", "disabled": false, "info": ""}, {"id": "e61d7807.20aac8", "type": "tab", "label": "Dashboard", "disabled": false, "info": ""}, {"id": "b63afd598e923224", "type": "tab", "label": "Flow 2", "disabled": false, "info": "", "env": []}, {"id": "e345437326b47dd1", "type": "lorawan-keys", "name": "ed", "keys": "{\"id\":{\"nsw\":\"\",\"asw\":\"\"}}"}, {"id": "8a5089a.03c5078", "type": "ui_base", "theme": {"name": "theme-light", "lightTheme": {"default": "#0094CE", "baseColor": "#0094CE", "baseFont": "-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif", "edited": true, "reset": false}, "darkTheme": {"default": "#097479", "baseColor": "#097479", "baseFont": "-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif", "edited": false}, "customTheme": {"name": "Untitled Theme 1", "default": "#4B7930", "baseColor": "#4B7930", "baseFont": "-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif"}, "themeState": {"base-color": {"default": "#0094CE", "value": "#0094CE", "edited": false}, "page-titlebar-backgroundColor": {"value": "#0094CE", "edited": false}, "page-backgroundColor": {"value": "#fafafa", "edited": false}, "page-sidebar-backgroundColor": {"value": "#ffffff", "edited": false}, "group-textColor": {"value": "#1bbfff", "edited": false}, "group-borderColor": {"value": "#ffffff", "edited": false}, "group-backgroundColor": {"value": "#ffffff", "edited": false}, "widget-textColor": {"value": "#111111", "edited": false}, "widget-backgroundColor": {"value": "#0094ce", "edited": false}, "widget-borderColor": {"value": "#ffffff", "edited": false}, "base-font": {"value": "-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif"}}, "angularTheme": {"primary": "indigo", "accents": "blue", "warn": "red", "background": "grey", "palette": "light"}}, "site": {"name": "Node-RED Dashboard", "hideToolbar": "false", "allowSwipe": "false", "lockMenu": "false", "allowTempTheme": "true", "dateFormat": "DD/MM/YYYY", "sizes": {"sx": 48, "sy": 48, "gx": 6, "gy": 6, "cx": 6, "cy": 6, "px": 0, "py": 0}}}, {"id": "43ae93e4.3fd91c", "type": "ui_tab", "name": "Sensor History", "icon": "fa-history", "order": 3, "disabled": false, "hidden": false}, {"id": "2d59a220.05900e", "type": "ui_tab", "name": "Sensor Data", "icon": "fa-thermometer-quarter", "order": 1, "disabled": false, "hidden": false}, {"id": "5721af64.a940a", "type": "ui_group", "name": "Group 1", "tab": "43ae93e4.3fd91c", "order": 1, "disp": false, "width": "20", "collapse": false}, {"id": "d347e8e2.369048", "type": "ui_group", "name": "Start / Stop", "tab": "2d59a220.05900e", "order": 1, "disp": true, "width": "8", "collapse": false}, {"id": "4dd3faf7.5231c4", "type": "ui_group", "name": "Sensor Readings", "tab": "2d59a220.05900e", "order": 2, "disp": true, "width": "6", "collapse": false}, {"id": "4f1e03619c75f770", "type": "http request", "z": "f8b85561e970734e", "name": "Washing", "method": "GET", "ret": "txt", "paytoqs": "ignore", "url": "http://washing-0.washing-headless.default.svc.cluster.local:1880/start/washing", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 780, "y": 280, "wires": [["b7bf4d860a0b0d8b"]]}, {"id": "738c07485435ce86", "type": "inject", "z": "f8b85561e970734e", "name": "Start", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "date", "x": 250, "y": 320, "wires": [["e6a892e1e25c939c"]]}, {"id": "e6a892e1e25c939c", "type": "http request", "z": "f8b85561e970734e", "name": "Defoliator", "method": "GET", "ret": "txt", "paytoqs": "ignore", "url": "http://defoliator-0.defoliator-headless.default.svc.cluster.local:1880/start/defoliator", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 420, "y": 280, "wires": [["44d80fbe020be0e5"]]}, {"id": "e6d29e91ea42814b", "type": "http request", "z": "f8b85561e970734e", "name": "Crushing", "method": "GET", "ret": "txt", "paytoqs": "ignore", "url": "http://crushing-0.crushing-headless.default.svc.cluster.local:1880/start/crush", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 1140, "y": 280, "wires": [["7c8e6956f5043cbc"]]}, {"id": "44d80fbe020be0e5", "type": "function", "z": "f8b85561e970734e", "name": "Start Washing", "func": "let res= msg.statusCode;\nif (res == 200){\n    msg.payload=\"start\";\n} else {\n    return null;\n}\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 600, "y": 280, "wires": [["4f1e03619c75f770"]]}, {"id": "b7bf4d860a0b0d8b", "type": "function", "z": "f8b85561e970734e", "name": "Start Crushing", "func": "let res= msg.statusCode;\nif (res == 200){\n    msg.payload=\"start\";\n} else {\n    return null;\n}\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 960, "y": 280, "wires": [["e6d29e91ea42814b"]]}, {"id": "d03c5190958ad88c", "type": "http in", "z": "f8b85561e970734e", "name": "Start", "url": "/start/grafana", "method": "get", "upload": false, "swaggerDoc": "", "x": 250, "y": 280, "wires": [["e6a892e1e25c939c", "cac5d0a17d654218", "0543c40f14a5b686"]]}, {"id": "6aa683a5a45cf3fc", "type": "http in", "z": "f8b85561e970734e", "name": "Temp", "url": "/temp", "method": "put", "upload": false, "swaggerDoc": "", "x": 550, "y": 400, "wires": [["cc9213f12a5c4153", "f56dcf3abaeee733"]]}, {"id": "cc9213f12a5c4153", "type": "http response", "z": "f8b85561e970734e", "name": "<PERSON><PERSON>", "statusCode": "", "headers": {}, "x": 820, "y": 400, "wires": []}, {"id": "f56dcf3abaeee733", "type": "function", "z": "f8b85561e970734e", "name": "function 4", "func": "delete msg.req;\ndelete msg.res;\nmsg.payload;\nmsg.topic = \"/3302/0/5500\";\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 560, "y": 440, "wires": [["01e2fcdf5858fb93"]]}, {"id": "01e2fcdf5858fb93", "type": "debug", "z": "f8b85561e970734e", "name": "debug 12", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 820, "y": 440, "wires": []}, {"id": "cac5d0a17d654218", "type": "http response", "z": "f8b85561e970734e", "name": "<PERSON><PERSON>", "statusCode": "", "headers": {}, "x": 420, "y": 240, "wires": []}, {"id": "0543c40f14a5b686", "type": "debug", "z": "f8b85561e970734e", "name": "debug 13", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 420, "y": 200, "wires": []}, {"id": "7c8e6956f5043cbc", "type": "http response", "z": "f8b85561e970734e", "name": "Res", "statusCode": "", "headers": {}, "x": 1150, "y": 320, "wires": []}, {"id": "7f8789b9.253048", "type": "http request", "z": "cac464ba.946338", "name": "Enroll Admin HTTP Request", "method": "POST", "ret": "txt", "paytoqs": "ignore", "url": "http://rest-api:3000/api/enrollAdmin", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 540, "y": 100, "wires": [["eeb423fe.c7207"]]}, {"id": "f20b8cdc.e002", "type": "inject", "z": "cac464ba.946338", "name": "POST", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "true", "payloadType": "bool", "x": 130, "y": 100, "wires": [["5dd7185d.b222"]]}, {"id": "eeb423fe.c7207", "type": "debug", "z": "cac464ba.946338", "name": "Result", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "x": 770, "y": 100, "wires": []}, {"id": "132183c.1a40d7c", "type": "http request", "z": "cac464ba.946338", "name": "Register User HTTP Request", "method": "POST", "ret": "txt", "url": "http://rest-api:3000/api/registerUser", "tls": "", "x": 540, "y": 200, "wires": [["1b18c390.d5f72c"]]}, {"id": "f38377bb.b0c6c8", "type": "inject", "z": "cac464ba.946338", "name": "POST", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "true", "payloadType": "bool", "x": 130, "y": 200, "wires": [["57f3f30c.7b938c"]]}, {"id": "1b18c390.d5f72c", "type": "debug", "z": "cac464ba.946338", "name": "Result", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "x": 770, "y": 200, "wires": []}, {"id": "6c18a509.9ab23c", "type": "http request", "z": "cac464ba.946338", "name": "Register Sensor HTTP Request", "method": "POST", "ret": "txt", "url": "http://rest-api:3000/api/registerSensor", "tls": "", "x": 550, "y": 300, "wires": [["ef80848f.68ea48"]]}, {"id": "fd2b0988.6b8da8", "type": "inject", "z": "cac464ba.946338", "name": "POST", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "true", "payloadType": "bool", "x": 130, "y": 300, "wires": [["cbcaabe9.f36e48"]]}, {"id": "ef80848f.68ea48", "type": "debug", "z": "cac464ba.946338", "name": "Result", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "x": 770, "y": 300, "wires": []}, {"id": "5dd7185d.b222", "type": "change", "z": "cac464ba.946338", "name": "Body / Headers", "rules": [{"t": "set", "p": "payload", "pt": "msg", "to": "{\"adminName\": \"admin\", \"password\":\"adminpw\"}", "tot": "json"}, {"t": "set", "p": "headers", "pt": "msg", "to": "{\"content-type\":\"application/json\"}", "tot": "json"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 300, "y": 100, "wires": [["7f8789b9.253048"]]}, {"id": "57f3f30c.7b938c", "type": "change", "z": "cac464ba.946338", "name": "Body / Headers", "rules": [{"t": "set", "p": "payload", "pt": "msg", "to": "{\"adminName\": \"admin\", \"username\":\"user1\"}", "tot": "json"}, {"t": "set", "p": "headers", "pt": "msg", "to": "{\"content-type\":\"application/json\"}", "tot": "json"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 300, "y": 200, "wires": [["132183c.1a40d7c"]]}, {"id": "cbcaabe9.f36e48", "type": "change", "z": "cac464ba.946338", "name": "Body / Headers", "rules": [{"t": "set", "p": "payload", "pt": "msg", "to": "{\"username\": \"user1\", \"channel\":\"channel1\", \"smartcontract\":\"cc\", \"args\":{\"sensorID\": \"sensor1\"} }", "tot": "json"}, {"t": "set", "p": "headers", "pt": "msg", "to": "{\"content-type\":\"application/json\"}", "tot": "json"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 300, "y": 300, "wires": [["6c18a509.9ab23c"]]}, {"id": "633bae1e.008508", "type": "inject", "z": "e61d7807.20aac8", "name": "", "props": [{"p": "payload"}], "repeat": "5", "crontab": "", "once": false, "onceDelay": "", "topic": "", "payload": "", "payloadType": "date", "x": 130, "y": 160, "wires": [["c58c5119.827ab"]]}, {"id": "c15ea1c5.969918", "type": "function", "z": "e61d7807.20aac8", "name": "Generate Temperature Readings", "func": "if( msg.payload === false ) {\n    return null;\n}\nmsg.payload = \"\" + Math.round(Math.random()*100) ;\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 780, "y": 160, "wires": [["5a6a66f1.d94"]]}, {"id": "dbc47c80.0aca78", "type": "inject", "z": "e61d7807.20aac8", "name": "Instructions", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": true, "onceDelay": "", "topic": "", "payload": "Use this toggle switch to start / stop the generation of IoT temperature readings to be written to the Hyperledger Fabric blockchain.", "payloadType": "str", "x": 130, "y": 80, "wires": [["2de29a96.96fa9e"]]}, {"id": "5a6a66f1.d94", "type": "link out", "z": "e61d7807.20aac8", "name": "", "links": ["fc9c2af3.67052"], "x": 975, "y": 160, "wires": []}, {"id": "fc9c2af3.67052", "type": "link in", "z": "e61d7807.20aac8", "name": "", "links": ["5a6a66f1.d94"], "x": 95, "y": 300, "wires": [["d08893cd.a4efd8", "30763357.5cb88c", "77b4a2f.a8e2e5c"]]}, {"id": "eb97c32a.39353", "type": "comment", "z": "e61d7807.20aac8", "name": "If you can't connect an IoT sensor, get data from the random generator", "info": "", "x": 270, "y": 40, "wires": []}, {"id": "4bcd1e2e.86bc3", "type": "inject", "z": "e61d7807.20aac8", "name": "MQTT", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": true, "onceDelay": "", "topic": "", "payload": "Preferability, connect an IoT sensor and transmit MQTT data into this Node-RED flow.", "payloadType": "str", "x": 110, "y": 120, "wires": [["97a6e001.617bb"]]}, {"id": "77b4a2f.a8e2e5c", "type": "function", "z": "e61d7807.20aac8", "name": "Body / Headers", "func": "var temp = msg.payload;\n\nvar date = new Date();\nvar options = { timeZone: 'Europe/Istanbul', year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit' };\nvar time = date.toLocaleDateString('tr-TR', options);\n\n\nmsg.payload = {'username':'user1','channel':'channel1', 'smartcontract':'cc', 'args': {'sensorID':'sensor1', 'temp':temp,'time':time} };\nmsg.headers = {'content-type':'application/json'};\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 300, "y": 260, "wires": [["c7870641.3974e"]]}, {"id": "b76fa7a7.50bf7", "type": "debug", "z": "e61d7807.20aac8", "name": "Result", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "x": 810, "y": 260, "wires": []}, {"id": "c7870641.3974e", "type": "http request", "z": "e61d7807.20aac8", "name": "Add Temperature HTTP Request", "method": "POST", "ret": "txt", "url": "http://rest-api:3000/api/addTemp", "tls": "", "x": 560, "y": 260, "wires": [["b76fa7a7.50bf7", "bbb8e6b6.242d38"]]}, {"id": "7c0ac50.df3b4bc", "type": "http request", "z": "e61d7807.20aac8", "name": "Get History HTTP Request", "method": "POST", "ret": "obj", "paytoqs": "ignore", "url": "http://rest-api:3000/api/getHistory", "tls": "", "persist": false, "proxy": "", "authType": "", "x": 540, "y": 480, "wires": [["59a77b16.08c6dc", "ea26e03a.c05d"]]}, {"id": "59a77b16.08c6dc", "type": "debug", "z": "e61d7807.20aac8", "name": "Result", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "x": 750, "y": 500, "wires": []}, {"id": "e1a0aa3b.ffe1c8", "type": "comment", "z": "e61d7807.20aac8", "name": "Read history from the blockchain into a table", "info": "", "x": 210, "y": 440, "wires": []}, {"id": "ea26e03a.c05d", "type": "function", "z": "e61d7807.20aac8", "name": "Format Table", "func": "if( msg.payload.length === 0 ) {\n    return;\n}\n\nvar TxChain = [];\nfor( i=0; i < msg.payload.length; i++ ) {\n    var TransactionRecord = { \"TxId\":msg.payload[i].TxId, \n                              \"sensorID\":msg.payload[i].Value.sensorID,\n                              \"temp\":msg.payload[i].Value.temp,\n                              \"time\":msg.payload[i].Value.time\n                            };\n    TxChain.push( TransactionRecord );    \n}\nmsg.payload = TxChain;\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 770, "y": 460, "wires": [["51182072.afefb8"]]}, {"id": "a79d0286.cd9288", "type": "change", "z": "e61d7807.20aac8", "name": "Body / Headers", "rules": [{"t": "set", "p": "payload", "pt": "msg", "to": "{\"username\": \"user1\", \"channel\":\"channel1\", \"smartcontract\":\"cc\", \"args\":{\"sensorID\": \"sensor1\"} }", "tot": "json"}, {"t": "set", "p": "headers", "pt": "msg", "to": "{\"content-type\":\"application/json\"}", "tot": "json"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 300, "y": 480, "wires": [["7c0ac50.df3b4bc"]]}, {"id": "b9d5af6d.c3728", "type": "delay", "z": "e61d7807.20aac8", "name": "Rate Limit", "pauseType": "rate", "timeout": "5", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "5", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": true, "outputs": 1, "x": 540, "y": 160, "wires": [["c15ea1c5.969918"]]}, {"id": "6bcd4294.f37834", "type": "ui_button", "z": "e61d7807.20aac8", "name": "", "group": "5721af64.a940a", "order": 1, "width": 0, "height": 0, "passthru": false, "label": "Get History", "tooltip": "", "color": "", "bgcolor": "", "icon": "", "payload": "", "payloadType": "str", "topic": "", "x": 110, "y": 480, "wires": [["a79d0286.cd9288"]]}, {"id": "c58c5119.827ab", "type": "ui_switch", "z": "e61d7807.20aac8", "name": "", "label": "Generate IoT readings", "tooltip": "", "group": "d347e8e2.369048", "order": 2, "width": 0, "height": 0, "passthru": true, "decouple": "false", "topic": "", "topicType": "str", "style": "", "onvalue": "true", "onvalueType": "bool", "onicon": "", "oncolor": "", "offvalue": "false", "offvalueType": "bool", "officon": "", "offcolor": "", "animate": true, "x": 340, "y": 160, "wires": [["b9d5af6d.c3728"]]}, {"id": "2de29a96.96fa9e", "type": "ui_text", "z": "e61d7807.20aac8", "group": "d347e8e2.369048", "order": 1, "width": "8", "height": "2", "name": "", "label": "", "format": "{{msg.payload}}", "layout": "row-spread", "x": 290, "y": 80, "wires": []}, {"id": "97a6e001.617bb", "type": "ui_text", "z": "e61d7807.20aac8", "group": "d347e8e2.369048", "order": 3, "width": "8", "height": "3", "name": "", "label": "", "format": "{{msg.payload}}", "layout": "row-spread", "className": "", "style": false, "font": "", "fontSize": "", "color": "#000000", "x": 290, "y": 120, "wires": []}, {"id": "30763357.5cb88c", "type": "ui_gauge", "z": "e61d7807.20aac8", "name": "Gauge", "group": "4dd3faf7.5231c4", "order": 2, "width": 0, "height": 0, "gtype": "gage", "title": "", "label": "°", "format": "{{value}}", "min": 0, "max": "100", "colors": ["#00b500", "#e6e600", "#ca3838"], "seg1": "", "seg2": "", "x": 270, "y": 340, "wires": []}, {"id": "d08893cd.a4efd8", "type": "ui_chart", "z": "e61d7807.20aac8", "name": "Chart", "group": "4dd3faf7.5231c4", "order": 1, "width": 0, "height": 0, "label": "Temperature Generator", "chartType": "line", "legend": "false", "xformat": "HH:mm:ss", "interpolate": "linear", "nodata": "Querying Entropy", "dot": false, "ymin": "0", "ymax": "100", "removeOlder": "1", "removeOlderPoints": "", "removeOlderUnit": "60", "cutout": "", "useOneColor": false, "useUTC": false, "colors": ["#1f77b4", "#aec7e8", "#ff7f0e", "#2ca02c", "#98df8a", "#d62728", "#ff9896", "#9467bd", "#c5b0d5"], "outputs": 1, "useDifferentColor": false, "x": 270, "y": 300, "wires": [[]]}, {"id": "bbb8e6b6.242d38", "type": "ui_toast", "z": "e61d7807.20aac8", "position": "top right", "displayTime": "3", "highlight": "", "sendall": true, "outputs": 0, "ok": "OK", "cancel": "", "raw": false, "topic": "Success", "name": "Notification", "x": 830, "y": 320, "wires": []}, {"id": "51182072.afefb8", "type": "ui_table", "z": "e61d7807.20aac8", "group": "5721af64.a940a", "name": "", "order": 2, "width": "20", "height": "14", "columns": [{"field": "TxId", "title": "Transaction ID", "width": "", "align": "left", "formatter": "plaintext", "formatterParams": {"target": "_blank"}}, {"field": "sensorID", "title": "Sensor ID", "width": "", "align": "left", "formatter": "plaintext", "formatterParams": {"target": "_blank"}}, {"field": "time", "title": "Time", "width": "", "align": "left", "formatter": "plaintext", "formatterParams": {"target": "_blank"}}, {"field": "temp", "title": "Temperature", "width": "", "align": "left", "formatter": "plaintext", "formatterParams": {"target": "_blank"}}], "outputs": 0, "cts": false, "x": 1010, "y": 460, "wires": []}, {"id": "6d176ae4be9315d3", "type": "http request", "z": "b63afd598e923224", "name": "Washing", "method": "GET", "ret": "txt", "paytoqs": "ignore", "url": "http://washing-0.washing-headless.default.svc.cluster.local:1880/start/washing", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 720, "y": 280, "wires": [["bf8e7d9a257a2f96"]]}, {"id": "c953d31f617779a5", "type": "inject", "z": "b63afd598e923224", "name": "Start", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "date", "x": 190, "y": 320, "wires": [["9564f12c508621b8"]]}, {"id": "9564f12c508621b8", "type": "http request", "z": "b63afd598e923224", "name": "Dry", "method": "GET", "ret": "txt", "paytoqs": "ignore", "url": "http://defoliator-0.defoliator-headless.default.svc.cluster.local:1880/start/process", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 350, "y": 280, "wires": [["b8c28259413549da"]]}, {"id": "22c7a2581314bb4c", "type": "http request", "z": "b63afd598e923224", "name": "Crushing", "method": "GET", "ret": "txt", "paytoqs": "ignore", "url": "http://crushing-0.crushing-headless.default.svc.cluster.local:1880/start/crush", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 1080, "y": 280, "wires": [["77504fe509acca76"]]}, {"id": "b8c28259413549da", "type": "function", "z": "b63afd598e923224", "name": "Start Washing", "func": "let res= msg.statusCode;\nif (res == 200){\n    msg.payload=\"start\";\n} else {\n    return null;\n}\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 540, "y": 280, "wires": [["6d176ae4be9315d3"]]}, {"id": "bf8e7d9a257a2f96", "type": "function", "z": "b63afd598e923224", "name": "Start Crushing", "func": "let res= msg.statusCode;\nif (res == 200){\n    msg.payload=\"start\";\n} else {\n    return null;\n}\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 900, "y": 280, "wires": [["22c7a2581314bb4c"]]}, {"id": "b4510f501480f832", "type": "http in", "z": "b63afd598e923224", "name": "Start", "url": "/start/grafana", "method": "get", "upload": false, "swaggerDoc": "", "x": 190, "y": 280, "wires": [["9564f12c508621b8", "d7367f09041e7b5c", "5c39dab13877b32f"]]}, {"id": "d7367f09041e7b5c", "type": "http response", "z": "b63afd598e923224", "name": "<PERSON><PERSON>", "statusCode": "", "headers": {}, "x": 360, "y": 240, "wires": []}, {"id": "5c39dab13877b32f", "type": "debug", "z": "b63afd598e923224", "name": "debug 13", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 360, "y": 200, "wires": []}, {"id": "77504fe509acca76", "type": "http response", "z": "b63afd598e923224", "name": "Res", "statusCode": "", "headers": {}, "x": 1090, "y": 320, "wires": []}]