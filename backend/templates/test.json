[{"id": "d095b447b72f6f05", "type": "tab", "label": "Crushing Machine", "disabled": false, "info": "", "env": []}, {"id": "820e03b4edc56a3b", "type": "group", "z": "d095b447b72f6f05", "name": "HW PART S P3", "style": {"label": true, "stroke": "#3f93cf"}, "nodes": ["da7829adcf0a18ce"], "x": 574, "y": 319, "w": 252, "h": 82}, {"id": "95a4316bd54453a2", "type": "group", "z": "d095b447b72f6f05", "name": "LESHAN CLIENT ", "style": {"stroke": "#92d04f", "label": true}, "nodes": ["821f23ee99700a64"], "x": 134, "y": 459, "w": 192, "h": 82}, {"id": "e2d85ce4ca3a5de1", "type": "group", "z": "d095b447b72f6f05", "name": "LESHAN CLIENT", "style": {"stroke": "#92d04f", "label": true}, "nodes": ["961fe3453e4104a4", "c6efe39c93357b59", "3c210049e890fb76", "c784b302013b50ca"], "x": 1094, "y": 179, "w": 352, "h": 642}, {"id": "96c1fbebaa21d02c", "type": "group", "z": "d095b447b72f6f05", "name": "HW Part S P3", "style": {"label": true, "stroke": "#0070c0"}, "nodes": ["6c4a3571c6761e07", "025f756135bddf61", "cd7df029acca2bed", "95ff54d520ebf4cf"], "x": 154, "y": 139, "w": 492, "h": 162}, {"id": "b4636300a19d3aa8", "type": "group", "z": "d095b447b72f6f05", "name": "Influx DB", "style": {"stroke": "#3f5787", "label": true}, "nodes": ["70b248719309ec30", "d834819a9f52e2f5"], "x": 154, "y": 599, "w": 232, "h": 122}, {"id": "7292f08658d5f018", "type": "function", "z": "d095b447b72f6f05", "name": "Only send if the value is different from the previous one", "func": "// Get the previous value from context\nlet previousValue = context.get('previousValue');\n\n// Check if the current payload is different from the previous one\nif (msg.payload !== previousValue) {\n    // Save the current value as the new previous value\n    context.set('previousValue', msg.payload);\n    msg.topic = \"/3302/2/5500\"\n    \n    // Forward the message since the value is different\n    return msg;\n} else {\n    // Don't forward the message if the value is the same\n    return null;\n}\n\n\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 860, "y": 220, "wires": [["c784b302013b50ca"]]}, {"id": "76c24df7e687d877", "type": "comment", "z": "d095b447b72f6f05", "name": "CRUSING MACHINE", "info": "# CRUSH", "x": 820, "y": 60, "wires": []}, {"id": "da47b757b5faea47", "type": "function", "z": "d095b447b72f6f05", "name": "Viscosity AVG", "func": "// Get the stored values array from context, or initialize a new one\nlet values = context.get('values') || [];\n\n// Add the current payload value to the array\nvalues.push(msg.payload);\n\n// If we have 10 values, calculate the average\nif (values.length === 5) {\n    let sum = values.reduce((a, b) => a + b, 0);\n    let average = sum / values.length;\n    \n    // Set the average as the payload\n    msg.payload = average;\n    msg.topic = \"/3300/1/5700\";\n    \n    // Clear the stored values\n    context.set('values', []);\n    \n    // Send the message with the average to the next node\n    return msg;\n} else {\n    // Save the updated values array in context\n    context.set('values', values);\n    \n    // Don't send a message until we have 10 values\n    return null;\n}", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 920, "y": 360, "wires": [["3c210049e890fb76"]]}, {"id": "44c9f7a47bb0b3a5", "type": "comment", "z": "d095b447b72f6f05", "name": "\"/3300/1/5700\"", "info": "", "x": 920, "y": 320, "wires": []}, {"id": "6b861108db4d2cf5", "type": "function", "z": "d095b447b72f6f05", "name": "Update Actuator", "func": "// Step 1: Filter based on the URI\nif (msg.payload.uri === \"/3302/2/5500\") {\n    \n    // Step 2: Check the value to determine if it's true or false\n    if (msg.payload.value && msg.payload.value.value === true) {\n        // If the value is true, set the payload to \"GO\" and send immediately\n        setTimeout(function() {\n        msg.payload = true;\n        msg.topic = \"/3306/4/5850\"\n        node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 1000);\n    } else if (msg.payload.value && msg.payload.value.value === false) {\n        // If the value is false, delay for 5 seconds before sending \"STOP\"\n        setTimeout(function() {\n            msg.payload = false;\n            msg.topic = \"/3306/4/5850\"\n            node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 5000);\n    } else {\n        // If the value is neither true nor false, handle it accordingly\n        msg.payload = \"UNKNOWN\";\n        node.send(msg);  // Send the message if the value is unknown\n    }\n\n} \n\nelse if (msg.payload.uri === \"/3300/1/5700\") {\n    \n    // Step 2: Check the value to determine if it's true or false\n    if (msg.payload.value && msg.payload.value.value > 6.0) {\n        // If the value is true, set the payload to \"GO\" and send immediately\n        setTimeout(function() {\n        msg.payload = 50;\n        msg.topic = \"/3306/4/5851\"\n        node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 1000);\n    } else {\n        setTimeout(function() {\n        msg.payload = 20;\n        msg.topic = \"/3306/4/5851\"\n        node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 1000);\n    }\n\n}\n\n\n\n\nelse {\n    // If the URI doesn't match, ignore the message\n    return null;\n}", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 580, "y": 780, "wires": [["961fe3453e4104a4"]]}, {"id": "11b49e89f47ad9f2", "type": "function", "z": "d095b447b72f6f05", "name": "Activate Actuator", "func": "// Step 1: Filter based on the URI\nif (msg.payload.uri === \"/3302/2/5500\") {\n    \n    // Step 2: Check the value to determine if it's true or false\n    if (msg.payload.value && msg.payload.value.value === true) {\n        // If the value is true, set the payload to \"GO\" and send immediately\n        setTimeout(function() {\n        msg.payload = \"GO\";\n        node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 1000);\n    } else if (msg.payload.value && msg.payload.value.value === false) {\n        // If the value is false, delay for 5 seconds before sending \"STOP\"\n        setTimeout(function() {\n            msg.payload = \"STOP\";\n            node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 5000);\n    } else {\n        // If the value is neither true nor false, handle it accordingly\n        msg.payload = \"UNKNOWN\";\n        node.send(msg);  // Send the message if the value is unknown\n    }\n\n} else {\n    // If the URI doesn't match, ignore the message\n    return null;\n}\n\n", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 590, "y": 660, "wires": [[]]}, {"id": "5370c8be70315f9f", "type": "comment", "z": "d095b447b72f6f05", "name": "/3306/4/5850", "info": "", "x": 570, "y": 820, "wires": []}, {"id": "1339583289bfadf4", "type": "comment", "z": "d095b447b72f6f05", "name": "\"/3302/2/5500\"", "info": "", "x": 920, "y": 180, "wires": []}, {"id": "35ee0dfe3ce7dd69", "type": "function", "z": "d095b447b72f6f05", "name": "Activate Actuator", "func": "// Step 1: Filter based on the URI\nif (msg.payload.uri === \"/3302/2/5500\") {\n    \n    // Step 2: Check the value to determine if it's true or false\n    if (msg.payload.value && msg.payload.value.value === true) {\n        // If the value is true, set the payload to \"GO\" and send immediately\n        setTimeout(function() {\n        msg.payload = \"GO\";\n        node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 1000);\n    } else if (msg.payload.value && msg.payload.value.value === false) {\n        // If the value is false, delay for 5 seconds before sending \"STOP\"\n        setTimeout(function() {\n            msg.payload = \"STOP\";\n            node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 5000);\n    } else {\n        // If the value is neither true nor false, handle it accordingly\n        msg.payload = \"UNKNOWN\";\n        node.send(msg);  // Send the message if the value is unknown\n    }\n\n} else {\n    // If the URI doesn't match, ignore the message\n    return null;\n}", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 590, "y": 540, "wires": [[]]}, {"id": "7f61a9971f6f7248", "type": "function", "z": "d095b447b72f6f05", "name": "Update Actuator", "func": "// Step 1: Filter based on the URI\nif (msg.payload.uri === \"/3302/2/5500\") {\n    \n    // Step 2: Check the value to determine if it's true or false\n    if (msg.payload.value && msg.payload.value.value === true) {\n        // If the value is true, set the payload to \"GO\" and send immediately\n        setTimeout(function() {\n        msg.payload = true;\n        msg.topic = \"/3306/5/5850\"\n        node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 1000);\n    } else if (msg.payload.value && msg.payload.value.value === false) {\n        // If the value is false, delay for 5 seconds before sending \"STOP\"\n        setTimeout(function() {\n            msg.payload = false;\n            msg.topic = \"/3306/5/5850\"\n            node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 5000);\n    } else {\n        // If the value is neither true nor false, handle it accordingly\n        msg.payload = \"UNKNOWN\";\n        node.send(msg);  // Send the message if the value is unknown\n    }\n\n} else {\n    // If the URI doesn't match, ignore the message\n    return null;\n}", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 580, "y": 480, "wires": [["c6efe39c93357b59"]]}, {"id": "13bc91b3ece12db0", "type": "debug", "z": "d095b447b72f6f05", "name": "Sensor Output", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 580, "y": 440, "wires": []}, {"id": "70b248719309ec30", "type": "function", "z": "d095b447b72f6f05", "g": "b4636300a19d3aa8", "name": "Send Data to DB", "func": "let payload = msg.payload;\nlet value1= \"default\";\nlet value = payload.value.value\nlet uri = payload.uri;\nswitch (uri) {\n    case \"/3306/0/5850\":\n        value1 = \"M-defoliator-conveyor\";\n        break;\n    case \"/3306/1/5850\":\n        value1 = \"M-defoliator\";\n        break;\n    case \"/3306/2/5850\":\n        value1 = \"M-wm-conveyor\";\n        break;\n    case \"/3306/3/5850\":\n        value1 = \"M-waterPump\";\n        break;\n    case \"/3306/3/5851\":\n        value1 = \"M-waterpump-dimmer\";\n        break;\n    case \"/3306/4/5850\":\n        value1 = \"M-cm\";\n        break;\n    case \"/3306/4/5851\":\n        value1 = \"M-cm-dimmer\";\n        break;\n    case \"/3306/5/5850\":\n        value1 = \"M-cm-conveyor\";\n        break;\n    case \"/3303/0/5700\":\n        value1 = \"S-Temperature\";\n        break;\n    case \"/3302/0/5500\":\n        value1 = \"S-Presence-one\";\n        break;\n    case \"/3302/1/5500\":\n        value1 = \"S-Presence-two\";\n        break;\n    case \"/3302/2/5500\":\n        value1 = \"S-Presence-three\";\n        break;\n    case \"/3300/0/5700\":\n        value1 = \"S-water-flow\";\n        break;   \n    case \"/3300/1/5700\":\n        value1 = \"S-viscosity-p\";\n        break;    \n    default:\n       value1 = value1;  \n}\nswitch (value) {\n    case true:\n        value = 1;\n        break;\n    case false:\n        value = 0;\n        break;\n    //default:\n     //   value = value;\n}\nlet timestamp = payload.ts; \n//milliseconds to seconds (InfluxDB uses seconds by default)\ntimestamp = Math.floor(timestamp / 1000);\nmsg.payload = {\n    [value1]: value,  \n    uri: uri,         \n    timestamp: timestamp \n};\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 270, "y": 640, "wires": [["d834819a9f52e2f5"]]}, {"id": "d834819a9f52e2f5", "type": "influxdb out", "z": "d095b447b72f6f05", "g": "b4636300a19d3aa8", "influxdb": "3fc00e2ac08b5bfd", "name": "Influxdb", "measurement": "nodered", "precision": "", "retentionPolicy": "", "database": "database", "precisionV18FluxV20": "ms", "retentionPolicyV18Flux": "", "org": "ICT", "bucket": "data2", "x": 260, "y": 680, "wires": []}, {"id": "c8b530194b4b52db", "type": "http in", "z": "d095b447b72f6f05", "name": "Start crushing Machine", "url": "/start/crush", "method": "get", "upload": false, "swaggerDoc": "", "x": 140, "y": 120, "wires": [["ca31643023aa1604", "cd7df029acca2bed"]]}, {"id": "6c4a3571c6761e07", "type": "function", "z": "d095b447b72f6f05", "g": "96c1fbebaa21d02c", "name": "loop", "func": "function getRandomInt(min, max) {\n  return Math.floor(Math.random() * (max - min +1)) + min;\n}\n\ncontext.loop = context.loop || \"stop\";\ncontext.loops = context.loops || 0;\n\nswitch (msg.payload) {\n\tcase \"stop\":\n\t\tcontext.loops = context.loops + 1;\n\t\tmsg.payload = \"stopped\";\n\t\tcontext.loop = \"stop\";\n\t\treturn [msg,null];\n\tcase \"toggle\":\n\t\tif (context.loop == \"start\") {\n\t\t\tmsg.payload = \"stopped\";\n\t\t\tcontext.loop = \"stop\";\n\t\t\treturn [msg,null];\n\t\t} else {\n\t\t\tmsg.payload = \"started\";\n\t\t\tcontext.loop = \"loop\";\n\t\t\tcontext.loops = 1;\n\t\t\treturn [msg,msg];\n\t\t}\n\tcase \"start\":\n\t\tmsg.payload = 20;\n\t\tcontext.loop = \"loop\";\n\t\tcontext.loops = 1;\n\t\treturn [msg,msg];\n\tdefault:\n\t\tif (context.loop == \"loop\") {\n\t\t\tcontext.loops = context.loops + 1;\n\t\t\tif (msg.payload > 40) {\n\t\t\t    msg.payload = msg.payload + getRandomInt(-4, 0)\n\t\t\t}\n\t\t\telse if (msg.payload < -20) {\n\t\t\t    msg.payload = msg.payload + getRandomInt(-4, 0)\n\t\t\t}\n\t\t\telse {\n\t\t\t    msg.payload = msg.payload + getRandomInt(-3, 3);\n\t\t\t}\n\t\t\treturn [msg,msg];\n\t\t} else {\n\t\t\treturn [null,null]; \n\t\t}\n}", "outputs": "2", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 250, "y": 220, "wires": [["95ff54d520ebf4cf"], ["025f756135bddf61"]]}, {"id": "025f756135bddf61", "type": "delay", "z": "d095b447b72f6f05", "g": "96c1fbebaa21d02c", "name": "", "pauseType": "delay", "timeout": "5", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "outputs": 1, "x": 240, "y": 260, "wires": [["6c4a3571c6761e07"]]}, {"id": "ca31643023aa1604", "type": "function", "z": "d095b447b72f6f05", "name": "Return Flow Data", "func": "msg.payload = 'start';  // Replace with actual sensor logic\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 350, "y": 120, "wires": [["6c4a3571c6761e07", "908cfc8d78aa4df6"]]}, {"id": "908cfc8d78aa4df6", "type": "http response", "z": "d095b447b72f6f05", "name": "Airflow Server", "statusCode": "", "headers": {}, "x": 540, "y": 120, "wires": []}, {"id": "da7829adcf0a18ce", "type": "function", "z": "d095b447b72f6f05", "g": "820e03b4edc56a3b", "name": "Viscosity Sensor P3", "func": "\nif (msg.payload === true) {\n    // Generate a random float number between 1.0 and 10.0\n    msg.payload = Math.random() * (10.0 - 1.0) + 1.0;\n} else {\n    // msg.payload = 0;\n    return null;\n}\n\n// Set the topic value\nmsg.topic = \"/3300/0/5700\";\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 700, "y": 360, "wires": [["da47b757b5faea47"]]}, {"id": "821f23ee99700a64", "type": "lwm2m client in", "z": "d095b447b72f6f05", "g": "95a4316bd54453a2", "name": "", "lwm2mClient": "c23f657dbf953847", "subscribeObjectEvents": true, "outputAsObject": false, "x": 230, "y": 500, "wires": [["11b49e89f47ad9f2", "6b861108db4d2cf5", "7f61a9971f6f7248", "35ee0dfe3ce7dd69", "13bc91b3ece12db0", "70b248719309ec30"]]}, {"id": "961fe3453e4104a4", "type": "lwm2m client out", "z": "d095b447b72f6f05", "g": "e2d85ce4ca3a5de1", "name": "Write or Execute LwM2M Resources", "lwm2mClient": "c23f657dbf953847", "x": 1270, "y": 780, "wires": []}, {"id": "c6efe39c93357b59", "type": "lwm2m client out", "z": "d095b447b72f6f05", "g": "e2d85ce4ca3a5de1", "name": "Write or Execute LwM2M Resources", "lwm2mClient": "c23f657dbf953847", "x": 1270, "y": 480, "wires": []}, {"id": "3c210049e890fb76", "type": "lwm2m client out", "z": "d095b447b72f6f05", "g": "e2d85ce4ca3a5de1", "name": "Write or Execute LwM2M Resources", "lwm2mClient": "c23f657dbf953847", "x": 1270, "y": 360, "wires": []}, {"id": "c784b302013b50ca", "type": "lwm2m client out", "z": "d095b447b72f6f05", "g": "e2d85ce4ca3a5de1", "name": "Write or Execute LwM2M Resources", "lwm2mClient": "c23f657dbf953847", "x": 1270, "y": 220, "wires": []}, {"id": "cd7df029acca2bed", "type": "function", "z": "d095b447b72f6f05", "g": "96c1fbebaa21d02c", "name": "Reset Flow", "func": "msg.payload = 'reset';  // Replace with actual sensor logic\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 450, "y": 180, "wires": [["95ff54d520ebf4cf"]]}, {"id": "95ff54d520ebf4cf", "type": "function", "z": "d095b447b72f6f05", "g": "96c1fbebaa21d02c", "name": "Presence Sensor Process 3", "func": "// Reset timer when signal is received (from an HTTP node or similar)\nif (msg.payload === 'reset') {\n    // Set new startTime to current time\n    context.set('startTime', Date.now());\n    msg.payload = 'Timer reset and started again';\n    return msg;  // Return message immediately after reset\n}\n\n// If no reset signal, calculate elapsed time\nif (context.get('startTime') === undefined) {\n    // If startTime hasn't been set, initialize it\n    context.set('startTime', Date.now());\n}\n\nlet elapsed = (Date.now() - context.get('startTime')) / 1000;\nmsg.payload = false;\nif (elapsed > 100) {\n    msg.payload = true;\n\n} if (elapsed > 380) {\n    msg.payload = false;\n}\n\nmsg.topic = \"/3302/2/5500\"\n\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 500, "y": 220, "wires": [["7292f08658d5f018", "da7829adcf0a18ce"]]}, {"id": "b8547612ba5d8c78", "type": "inject", "z": "d095b447b72f6f05", "name": "", "props": [{"p": "payload"}, {"p": "topic", "vt": "str"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "date", "x": 160, "y": 60, "wires": [["ca31643023aa1604"]]}, {"id": "3fc00e2ac08b5bfd", "type": "influxdb", "hostname": "127.0.0.1", "port": "8086", "protocol": "http", "database": "database", "name": "ICT", "usetls": false, "tls": "", "influxdbVersion": "2.0", "url": "http://influxdb-0.influxdb-headless.default.svc.cluster.local:8086", "timeout": "10", "rejectUnauthorized": true}, {"id": "c23f657dbf953847", "type": "lwm2m client", "disabled": false, "lazyStart": false, "clientName": "crush1213", "enableDTLS": false, "clientPort": "56832", "lifetimeSec": "300", "reconnectSec": "60", "bootstrapIntervalSec": "3600", "maxRecvPacketSize": "16486", "requestBootstrap": false, "saveProvisionedConfig": false, "useIPv4": true, "serverHost": "**********", "serverPort": "5683", "redirectLwm2mClientLog": false, "dumpLwm2mMessages": false, "hideSensitiveInfo": true, "propagateInternalEvents": false, "objects": "{\"3300\":{\"0\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0}},\"1\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0}}},\"3302\":{\"0\":{\"2\":true,\"5500\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}},\"1\":{\"2\":true,\"5500\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}},\"2\":{\"2\":true,\"5500\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}}},\"3303\":{\"0\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0}}},\"3306\":{\"0\":{\"2\":true,\"5850\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}},\"1\":{\"2\":true,\"5850\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}},\"2\":{\"2\":true,\"5850\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}},\"3\":{\"2\":true,\"5850\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false},\"5851\":{\"type\":\"INTEGER\",\"acl\":\"RW\",\"value\":0}},\"4\":{\"2\":true,\"5850\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false},\"5851\":{\"type\":\"INTEGER\",\"acl\":\"RW\",\"value\":0}},\"5\":{\"2\":true,\"5850\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}}}}"}]