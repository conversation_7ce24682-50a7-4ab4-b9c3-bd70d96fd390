[{"id": "dad37defea4837f5", "type": "tab", "label": "Defoliator", "disabled": false, "info": "", "env": []}, {"id": "12a013e7919087d2", "type": "tab", "label": "Dry", "disabled": false, "info": "", "env": []}, {"id": "674535e0813040d4", "type": "tab", "label": "Sensors MQTT", "disabled": false, "info": "", "env": []}, {"id": "384c213e7099162d", "type": "tab", "label": "Flow 1", "disabled": true, "info": "", "env": []}, {"id": "f3434801fe70a0c7", "type": "tab", "label": "Flow 3", "disabled": true, "info": "", "env": []}, {"id": "e4ec64359dfe605c", "type": "group", "z": "dad37defea4837f5", "name": "HW PART A P1", "style": {"stroke": "#3f93cf", "label": true}, "nodes": ["be6ad87698e638c7", "eb0a164d3c7ab3ea"], "x": 774, "y": 379, "w": 292, "h": 282}, {"id": "cc7ab9eba74713e9", "type": "group", "z": "dad37defea4837f5", "name": "LESHAN CLIENT ", "style": {"stroke": "#92d04f", "label": true}, "nodes": ["b4b01bf042517adf"], "x": 304, "y": 339, "w": 152, "h": 82}, {"id": "7ea7cde03aad366b", "type": "group", "z": "dad37defea4837f5", "name": "LESHAN CLIENT ", "style": {"label": true, "stroke": "#92d04f"}, "nodes": ["bb706e6e9b1b498a", "0e952e2750f8010f", "6f249d089e96300d"], "x": 1094, "y": 179, "w": 352, "h": 542}, {"id": "8214d5cdd1fc6e4b", "type": "group", "z": "dad37defea4837f5", "name": "HW Part S P1", "style": {"stroke": "#0070c0", "label": true}, "nodes": ["9208dc60e569af57", "de2ee986dc9123df", "9e77ba484b78b8cf", "b617df4dac3f3b32"], "x": 34, "y": 139, "w": 512, "h": 162}, {"id": "d7159b59d49fc49d", "type": "group", "z": "dad37defea4837f5", "name": "InFlux DB", "style": {"stroke": "#6f2fa0", "label": true}, "nodes": ["282e3babd3554c11", "ea22e848df8494e7"], "x": 234, "y": 479, "w": 232, "h": 122}, {"id": "753503cb939e2954", "type": "group", "z": "12a013e7919087d2", "name": "InFlux DB", "style": {"stroke": "#6f2fa0", "label": true}, "nodes": ["0a990723ce7432c8", "29a7d077176891a3"], "x": 214, "y": 619, "w": 232, "h": 122}, {"id": "d316f7f3772ed69f", "type": "group", "z": "674535e0813040d4", "name": "", "style": {"label": true}, "nodes": ["56634b3247a2e675", "a5e0a3ca6ae13e1f", "3dbb81499cd88655", "6a224dd9191ae610", "c871ca0824b8029d", "8b0f26b238da8e19", "a7da024289aaca81", "46ca3abe80aca0a8", "75e54dc680c30d09", "422fab61599c0d51", "3db7e98ceb9bb344", "f43eb7b1d331025c", "9636197923c29c99"], "x": 34, "y": 19, "w": 1232, "h": 202}, {"id": "8c7fb152801a7b0c", "type": "group", "z": "674535e0813040d4", "style": {"stroke": "#999999", "stroke-opacity": "1", "fill": "none", "fill-opacity": "1", "label": true, "label-position": "nw", "color": "#a4a4a4"}, "nodes": ["1030808ed2def1d2", "23c525b8784f31dd", "6afa8c2b1d4089f9", "961d445328aa32a4", "efebe386672db8ac", "a8fe44a05ce6818c", "2db4568e8de4ef41", "8ae29fcb75b99928", "a77c7ba0e4a936f4", "145c1eb087fb77fc", "c11450e27b053ba1", "ce6ad6172302ac38", "ddc12573296d17e9", "dd5cb416d91412ac", "bad929ae98821397", "b58f7d6d59b2aa9d", "a85f85d277f30320", "faaa5209fda3e4d5", "d5a869a1db4f924e", "07fe1aef2e50a341", "96bbffecf8bf0ad3"], "x": 34, "y": 239, "w": 1232, "h": 282}, {"id": "fc2dbccb6257a01c", "type": "group", "z": "674535e0813040d4", "style": {"stroke": "#999999", "stroke-opacity": "1", "fill": "none", "fill-opacity": "1", "label": true, "label-position": "nw", "color": "#a4a4a4"}, "nodes": ["c0f4de2d7fe06326", "a61858d718689649", "d2adc87b2d8d9dd3", "dc70892583d2fd94", "adf196aedaf2df4f", "3efcc8aaf9150be1", "7d137c76a5e06beb", "ef21238aac8ce12b", "a82db578e860893b", "ebb905f1879fb12f"], "x": 34, "y": 539, "w": 1232, "h": 202}, {"id": "c23f657dbf953847", "type": "lwm2m client", "disabled": false, "lazyStart": false, "clientName": "Dry", "enableDTLS": false, "clientPort": "56832", "lifetimeSec": "300", "reconnectSec": "60", "bootstrapIntervalSec": "3600", "maxRecvPacketSize": "16486", "requestBootstrap": false, "saveProvisionedConfig": false, "useIPv4": true, "serverHost": "leshan-0.leshan-headless.default.svc.cluster.local", "serverPort": "5683", "redirectLwm2mClientLog": false, "dumpLwm2mMessages": false, "hideSensitiveInfo": true, "propagateInternalEvents": false, "objects": "{\"3300\":{\"0\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0}},\"1\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0}},\"2\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0}},\"3\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0}},\"4\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0}}},\"3303\":{\"0\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0}},\"1\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0}}},\"3304\":{\"0\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0}},\"1\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0}}},\"3308\":{\"0\":{\"2\":true,\"5900\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0}}}}"}, {"id": "3fc00e2ac08b5bfd", "type": "influxdb", "hostname": "127.0.0.1", "port": "8086", "protocol": "http", "database": "database", "name": "ICT", "usetls": false, "tls": "", "influxdbVersion": "2.0", "url": "http://influxdb-0.influxdb-headless.default.svc.cluster.local:8086", "timeout": "10", "rejectUnauthorized": true}, {"id": "407a01e4.6b637", "type": "mqtt-broker", "name": "m<PERSON><PERSON><PERSON>", "broker": "*************", "port": "1883", "clientid": "", "autoConnect": true, "usetls": false, "protocolVersion": "4", "keepalive": "60", "cleansession": true, "autoUnsubscribe": true, "birthTopic": "", "birthQos": "0", "birthPayload": "", "birthMsg": {}, "closeTopic": "", "closePayload": "", "closeMsg": {}, "willTopic": "", "willQos": "0", "willPayload": "", "willMsg": {}, "userProps": "", "sessionExpiry": ""}, {"id": "e09022ccd84710a0", "type": "mqtt-broker", "name": "", "broker": "*************", "port": "1883", "clientid": "", "autoConnect": true, "usetls": false, "protocolVersion": "4", "keepalive": "60", "cleansession": true, "autoUnsubscribe": true, "birthTopic": "", "birthQos": "0", "birthRetain": "false", "birthPayload": "", "birthMsg": {}, "closeTopic": "", "closeQos": "0", "closeRetain": "false", "closePayload": "", "closeMsg": {}, "willTopic": "", "willQos": "0", "willRetain": "false", "willPayload": "", "willMsg": {}, "userProps": "", "sessionExpiry": ""}, {"id": "0eaea711b6b3b72c", "type": "lwm2m client", "d": true, "disabled": false, "lazyStart": false, "clientName": "def", "enableDTLS": false, "clientPort": "56832", "lifetimeSec": "300", "reconnectSec": "60", "bootstrapIntervalSec": "3600", "maxRecvPacketSize": "16486", "requestBootstrap": false, "saveProvisionedConfig": false, "useIPv4": true, "serverHost": "leshan-0.leshan-headless.default.svc.cluster.local", "serverPort": "5683", "redirectLwm2mClientLog": false, "dumpLwm2mMessages": false, "hideSensitiveInfo": true, "propagateInternalEvents": false, "objects": "{\"3300\":{\"0\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0}},\"1\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0}}},\"3302\":{\"0\":{\"2\":true,\"5500\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}},\"1\":{\"2\":true,\"5500\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}},\"2\":{\"2\":true,\"5500\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}}},\"3303\":{\"0\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0}}},\"3306\":{\"0\":{\"2\":true,\"5850\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}},\"1\":{\"2\":true,\"5850\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}},\"2\":{\"2\":true,\"5850\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}},\"3\":{\"2\":true,\"5850\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false},\"5851\":{\"type\":\"INTEGER\",\"acl\":\"RW\",\"value\":0}},\"4\":{\"2\":true,\"5850\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false},\"5851\":{\"type\":\"INTEGER\",\"acl\":\"RW\",\"value\":0}},\"5\":{\"2\":true,\"5850\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}}}}"}, {"id": "9571bffdf4883999", "type": "lwm2m client", "d": true, "disabled": false, "lazyStart": false, "clientName": "test", "enableDTLS": false, "clientPort": "56830", "lifetimeSec": "300", "reconnectSec": "60", "bootstrapIntervalSec": "3600", "maxRecvPacketSize": "16486", "requestBootstrap": false, "saveProvisionedConfig": false, "useIPv4": true, "serverHost": "leshan-0.leshan-headless.default.svc.cluster.local", "serverPort": "5683", "redirectLwm2mClientLog": false, "dumpLwm2mMessages": false, "hideSensitiveInfo": true, "propagateInternalEvents": false, "objects": "{\"3303\":{\"0\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0}}}}"}, {"id": "mqttBrokerConfig", "type": "mqtt-broker", "name": "", "broker": "http://localhost.com", "port": "1450", "clientid": "", "autoConnect": true, "usetls": false, "protocolVersion": "4", "keepalive": "60", "cleansession": true, "autoUnsubscribe": true, "birthTopic": "", "birthQos": "0", "birthRetain": "false", "birthPayload": "", "birthMsg": {}, "closeTopic": "", "closeQos": "0", "closeRetain": "false", "closePayload": "", "closeMsg": {}, "willTopic": "", "willQos": "0", "willRetain": "false", "willPayload": "", "willMsg": {}, "userProps": "", "sessionExpiry": ""}, {"id": "fa7772faa665ac1b", "type": "debug", "z": "dad37defea4837f5", "name": "Sub Leshan Server", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 670, "y": 300, "wires": []}, {"id": "09713d5dbc474dc5", "type": "comment", "z": "dad37defea4837f5", "name": "Olive Oil Process ", "info": "", "x": 820, "y": 60, "wires": []}, {"id": "5cd6cd04112a3d2c", "type": "function", "z": "dad37defea4837f5", "name": "Only send if the value is different from the previous one", "func": "// Get the previous value from context\nlet previousValue = context.get('previousValue');\n\n// Check if the current payload is different from the previous one\nif (msg.payload !== previousValue) {\n    // Save the current value as the new previous value\n    context.set('previousValue', msg.payload);\n    msg.topic = \"/3302/0/5500\"\n    \n    // Forward the message since the value is different\n    return msg;\n} else {\n    // Don't forward the message if the value is the same\n    return null;\n}\n\n\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 840, "y": 220, "wires": [["6f249d089e96300d"]]}, {"id": "72581d8ccc86e11f", "type": "comment", "z": "dad37defea4837f5", "name": "/3302/0/5500", "info": "", "x": 830, "y": 180, "wires": []}, {"id": "771d1be098511cee", "type": "comment", "z": "dad37defea4837f5", "name": "Defloiator", "info": "", "x": 820, "y": 100, "wires": []}, {"id": "b6b1484ba64f8ac2", "type": "function", "z": "dad37defea4837f5", "name": "Activate Actuator", "func": "// Step 1: Filter based on the URI\nif (msg.payload.uri === \"/3302/0/5500\") {\n    \n    // Step 2: Check the value to determine if it's true or false\n    if (msg.payload.value && msg.payload.value.value === true) {\n        // If the value is true, set the payload to \"GO\" and send immediately\n        setTimeout(function() {\n        msg.payload = \"GO\";\n        node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 1000);\n    } else if (msg.payload.value && msg.payload.value.value === false) {\n        // If the value is false, delay for 5 seconds before sending \"STOP\"\n        setTimeout(function() {\n            msg.payload = \"STOP\";\n            node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 5000);\n    } else {\n        // If the value is neither true nor false, handle it accordingly\n        msg.payload = \"UNKNOWN\";\n        node.send(msg);  // Send the message if the value is unknown\n    }\n\n} else {\n    // If the URI doesn't match, ignore the message\n    return null;\n}", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 650, "y": 420, "wires": [["eb0a164d3c7ab3ea"]]}, {"id": "d9fe9a7d1db7cfac", "type": "function", "z": "dad37defea4837f5", "name": "Update Actuator", "func": "// Step 1: Filter based on the URI\nif (msg.payload.uri === \"/3302/0/5500\") {\n    \n    // Step 2: Check the value to determine if it's true or false\n    if (msg.payload.value && msg.payload.value.value === true) {\n        // If the value is true, set the payload to \"GO\" and send immediately\n        setTimeout(function() {\n        msg.payload = true;\n        msg.topic = \"/3306/0/5850\"\n        node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 1000);\n    } else if (msg.payload.value && msg.payload.value.value === false) {\n        // If the value is false, delay for 5 seconds before sending \"STOP\"\n        setTimeout(function() {\n            msg.payload = false;\n            msg.topic = \"/3306/0/5850\"\n            node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 5000);\n    } else {\n        // If the value is neither true nor false, handle it accordingly\n        msg.payload = \"UNKNOWN\";\n        node.send(msg);  // Send the message if the value is unknown\n    }\n\n} else {\n    // If the URI doesn't match, ignore the message\n    return null;\n}", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 640, "y": 340, "wires": [["0e952e2750f8010f"]]}, {"id": "201a17ac74540fd8", "type": "comment", "z": "dad37defea4837f5", "name": "/3306/0/5850", "info": "", "x": 630, "y": 460, "wires": []}, {"id": "d71609745f138b30", "type": "function", "z": "dad37defea4837f5", "name": "Activate Actuator", "func": "// Step 1: Filter based on the URI\nif (msg.payload.uri === \"/3302/0/5500\") {\n    \n    // Step 2: Check the value to determine if it's true or false\n    if (msg.payload.value && msg.payload.value.value === true) {\n        // If the value is true, set the payload to \"GO\" and send immediately\n        setTimeout(function() {\n        msg.payload = \"GO\";\n        node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 1000);\n    } else if (msg.payload.value && msg.payload.value.value === false) {\n        // If the value is false, delay for 5 seconds before sending \"STOP\"\n        setTimeout(function() {\n            msg.payload = \"STOP\";\n            node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 5000);\n    } else {\n        // If the value is neither true nor false, handle it accordingly\n        msg.payload = \"UNKNOWN\";\n        node.send(msg);  // Send the message if the value is unknown\n    }\n\n} else {\n    // If the URI doesn't match, ignore the message\n    return null;\n}", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 650, "y": 620, "wires": [["be6ad87698e638c7"]]}, {"id": "a1c7499cf847f54b", "type": "function", "z": "dad37defea4837f5", "name": "Update Actuator", "func": "// Step 1: Filter based on the URI\nif (msg.payload.uri === \"/3302/0/5500\") {\n    \n    // Step 2: Check the value to determine if it's true or false\n    if (msg.payload.value && msg.payload.value.value === true) {\n        // If the value is true, set the payload to \"GO\" and send immediately\n        setTimeout(function() {\n        msg.payload = true;\n        msg.topic = \"/3306/1/5850\"\n        node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 1000);\n    } else if (msg.payload.value && msg.payload.value.value === false) {\n        // If the value is false, delay for 5 seconds before sending \"STOP\"\n        setTimeout(function() {\n            msg.payload = false;\n            msg.topic = \"/3306/1/5850\"\n            node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 5000);\n    } else {\n        // If the value is neither true nor false, handle it accordingly\n        msg.payload = \"UNKNOWN\";\n        node.send(msg);  // Send the message if the value is unknown\n    }\n\n} else {\n    // If the URI doesn't match, ignore the message\n    return null;\n}", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 640, "y": 680, "wires": [["bb706e6e9b1b498a"]]}, {"id": "355de369bc6a2338", "type": "comment", "z": "dad37defea4837f5", "name": "/3306/1/5850", "info": "", "x": 630, "y": 720, "wires": []}, {"id": "be6ad87698e638c7", "type": "traffic", "z": "dad37defea4837f5", "g": "e4ec64359dfe605c", "name": "Defoliator's Air Pump", "property_allow": "payload", "filter_allow": "GO", "ignore_case_allow": false, "negate_allow": false, "send_allow": false, "property_stop": "payload", "filter_stop": "stop", "ignore_case_stop": true, "negate_stop": false, "send_stop": true, "default_start": false, "differ": false, "x": 920, "y": 620, "wires": [[]]}, {"id": "eb0a164d3c7ab3ea", "type": "traffic", "z": "dad37defea4837f5", "g": "e4ec64359dfe605c", "name": "Defoliator's Conveyor Motor ", "property_allow": "payload", "filter_allow": "GO", "ignore_case_allow": false, "negate_allow": false, "send_allow": false, "property_stop": "payload", "filter_stop": "stop", "ignore_case_stop": true, "negate_stop": false, "send_stop": true, "default_start": false, "differ": false, "x": 920, "y": 420, "wires": [[]]}, {"id": "b4b01bf042517adf", "type": "lwm2m client in", "z": "dad37defea4837f5", "g": "cc7ab9eba74713e9", "name": "", "lwm2mClient": "0eaea711b6b3b72c", "subscribeObjectEvents": true, "outputAsObject": false, "x": 380, "y": 380, "wires": [["fa7772faa665ac1b", "b6b1484ba64f8ac2", "d9fe9a7d1db7cfac", "d71609745f138b30", "a1c7499cf847f54b", "ea22e848df8494e7"]]}, {"id": "bb706e6e9b1b498a", "type": "lwm2m client out", "z": "dad37defea4837f5", "g": "7ea7cde03aad366b", "name": "Write or Execute LwM2M Resources", "lwm2mClient": "0eaea711b6b3b72c", "x": 1270, "y": 680, "wires": []}, {"id": "0e952e2750f8010f", "type": "lwm2m client out", "z": "dad37defea4837f5", "g": "7ea7cde03aad366b", "name": "Write or Execute LwM2M Resources", "lwm2mClient": "0eaea711b6b3b72c", "x": 1270, "y": 340, "wires": []}, {"id": "6f249d089e96300d", "type": "lwm2m client out", "z": "dad37defea4837f5", "g": "7ea7cde03aad366b", "name": "Write or Execute LwM2M Resources", "lwm2mClient": "0eaea711b6b3b72c", "x": 1270, "y": 220, "wires": []}, {"id": "ea22e848df8494e7", "type": "function", "z": "dad37defea4837f5", "g": "d7159b59d49fc49d", "name": "Send Data to DB", "func": "let payload = msg.payload;\nlet value1= \"default\";\nlet value = payload.value.value\nlet uri = payload.uri;\nswitch (uri) {\n    case \"/3306/0/5850\":\n        value1 = \"M-defoliator-conveyor\";\n        break;\n    case \"/3306/1/5850\":\n        value1 = \"M-defoliator\";\n        break;\n    case \"/3306/2/5850\":\n        value1 = \"M-wm-conveyor\";\n        break;\n    case \"/3306/3/5850\":\n        value1 = \"M-waterPump\";\n        break;\n    case \"/3306/3/5851\":\n        value1 = \"M-waterpump-dimmer\";\n        break;\n    case \"/3306/4/5850\":\n        value1 = \"M-cm\";\n        break;\n    case \"/3306/4/5851\":\n        value1 = \"M-cm-dimmer\";\n        break;\n    case \"/3306/5/5850\":\n        value1 = \"M-cm-conveyor\";\n        break;\n    case \"/3303/0/5700\":\n        value1 = \"S-Temperature\";\n        break;\n    case \"/3302/0/5500\":\n        value1 = \"S-Presence-one\";\n        break;\n    case \"/3302/1/5500\":\n        value1 = \"S-Presence-two\";\n        break;\n    case \"/3302/2/5500\":\n        value1 = \"S-Presence-three\";\n        break;\n    case \"/3300/0/5700\":\n        value1 = \"S-water-flow\";\n        break;   \n    case \"/3300/1/5700\":\n        value1 = \"S-viscosity-p\";\n        break;    \n    default:\n       value1 = value1;  \n}\nswitch (value) {\n    case true:\n        value = 1;\n        break;\n    case false:\n        value = 0;\n        break;\n    //default:\n     //   value = value;\n}\nlet timestamp = payload.ts; \n//milliseconds to seconds (InfluxDB uses seconds by default)\ntimestamp = Math.floor(timestamp / 1000);\nmsg.payload = {\n    [value1]: value,  \n    uri: uri,         \n    timestamp: timestamp \n};\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 350, "y": 520, "wires": [["282e3babd3554c11"]]}, {"id": "282e3babd3554c11", "type": "influxdb out", "z": "dad37defea4837f5", "g": "d7159b59d49fc49d", "influxdb": "3fc00e2ac08b5bfd", "name": "Influxdb", "measurement": "nodered", "precision": "", "retentionPolicy": "", "database": "database", "precisionV18FluxV20": "ms", "retentionPolicyV18Flux": "", "org": "ICT", "bucket": "data2", "x": 340, "y": 560, "wires": []}, {"id": "98e08210a0da65c9", "type": "http in", "z": "dad37defea4837f5", "name": "Start defoliator", "url": "/start/defoliator", "method": "get", "upload": false, "swaggerDoc": "", "x": 100, "y": 120, "wires": [["14998bac02546e6c", "b617df4dac3f3b32"]]}, {"id": "9208dc60e569af57", "type": "function", "z": "dad37defea4837f5", "g": "8214d5cdd1fc6e4b", "name": "loop", "func": "function getRandomInt(min, max) {\n  return Math.floor(Math.random() * (max - min +1)) + min;\n}\n\ncontext.loop = context.loop || \"stop\";\ncontext.loops = context.loops || 0;\n\nswitch (msg.payload) {\n\tcase \"stop\":\n\t\tcontext.loops = context.loops + 1;\n\t\tmsg.payload = \"stopped\";\n\t\tcontext.loop = \"stop\";\n\t\treturn [msg,null];\n\tcase \"toggle\":\n\t\tif (context.loop == \"start\") {\n\t\t\tmsg.payload = \"stopped\";\n\t\t\tcontext.loop = \"stop\";\n\t\t\treturn [msg,null];\n\t\t} else {\n\t\t\tmsg.payload = \"started\";\n\t\t\tcontext.loop = \"loop\";\n\t\t\tcontext.loops = 1;\n\t\t\treturn [msg,msg];\n\t\t}\n\tcase \"start\":\n\t\tmsg.payload = 20;\n\t\tcontext.loop = \"loop\";\n\t\tcontext.loops = 1;\n\t\treturn [msg,msg];\n\tdefault:\n\t\tif (context.loop == \"loop\") {\n\t\t\tcontext.loops = context.loops + 1;\n\t\t\tif (msg.payload > 40) {\n\t\t\t    msg.payload = msg.payload + getRandomInt(-4, 0)\n\t\t\t}\n\t\t\telse if (msg.payload < -20) {\n\t\t\t    msg.payload = msg.payload + getRandomInt(-4, 0)\n\t\t\t}\n\t\t\telse {\n\t\t\t    msg.payload = msg.payload + getRandomInt(-3, 3);\n\t\t\t}\n\t\t\treturn [msg,msg];\n\t\t} else {\n\t\t\treturn [null,null]; \n\t\t}\n}", "outputs": "2", "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 110, "y": 220, "wires": [["9e77ba484b78b8cf"], ["de2ee986dc9123df"]]}, {"id": "de2ee986dc9123df", "type": "delay", "z": "dad37defea4837f5", "g": "8214d5cdd1fc6e4b", "name": "", "pauseType": "delay", "timeout": "5", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "outputs": 1, "x": 120, "y": 260, "wires": [["9208dc60e569af57"]]}, {"id": "14998bac02546e6c", "type": "function", "z": "dad37defea4837f5", "name": "Return Flow Data", "func": "msg.payload = 'start';  // Replace with actual sensor logic\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 290, "y": 120, "wires": [["9208dc60e569af57", "b64b0cacacf26ceb"]]}, {"id": "b64b0cacacf26ceb", "type": "http response", "z": "dad37defea4837f5", "name": "Airflow Server", "statusCode": "", "headers": {"start": "yes"}, "x": 480, "y": 120, "wires": []}, {"id": "9e77ba484b78b8cf", "type": "function", "z": "dad37defea4837f5", "g": "8214d5cdd1fc6e4b", "name": "Presence Sensor Process 1 (S1)", "func": "// Reset timer when signal is received (from an HTTP node or similar)\nif (msg.payload === 'reset') {\n    // Set new startTime to current time\n    context.set('startTime', Date.now());\n    msg.payload = 'Timer reset and started again';\n    return msg;  // Return message immediately after reset\n}\n\n// If no reset signal, calculate elapsed time\nif (context.get('startTime') === undefined) {\n    // If startTime hasn't been set, initialize it\n    context.set('startTime', Date.now());\n}\n\nlet elapsed = (Date.now() - context.get('startTime')) / 1000;\n\nif (elapsed < 200) {\n    msg.payload = true;\n} else {\n    msg.payload = false;\n}\n\nmsg.topic = \"/3302/0/5500\";\n\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 380, "y": 220, "wires": [["5cd6cd04112a3d2c"]]}, {"id": "b617df4dac3f3b32", "type": "function", "z": "dad37defea4837f5", "g": "8214d5cdd1fc6e4b", "name": "Reset Flow", "func": "msg.payload = 'reset';  // Replace with actual sensor logic\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 350, "y": 180, "wires": [["9e77ba484b78b8cf"]]}, {"id": "9ee4c076c45539ce", "type": "function", "z": "12a013e7919087d2", "name": "Only send if the value is different from the previous one", "func": "// Get the previous value from context\nlet previousValue = context.get('previousValue');\n\n// Check if the current payload is different from the previous one\nif (msg.payload !== previousValue) {\n    // Save the current value as the new previous value\n    context.set('previousValue', msg.payload);\n    msg.topic = \"/3303/0/5700\"\n    \n    // Forward the message since the value is different\n    return msg;\n} else {\n    // Don't forward the message if the value is the same\n    return null;\n}\n\n\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 700, "y": 300, "wires": [["f9a64815929725ef", "568b8bc85b3cd37c"]]}, {"id": "f9a64815929725ef", "type": "lwm2m client out", "z": "12a013e7919087d2", "name": "Write or Execute LwM2M Resources", "lwm2mClient": "c23f657dbf953847", "x": 1150, "y": 380, "wires": []}, {"id": "55fb83f7b15f26c4", "type": "comment", "z": "12a013e7919087d2", "name": "/3304/0/5700: Humidity Sensor Value", "info": "", "x": 570, "y": 200, "wires": []}, {"id": "31930b526816c968", "type": "comment", "z": "12a013e7919087d2", "name": "/3303/0/5700: Temp Sensor Value", "info": "", "x": 550, "y": 160, "wires": []}, {"id": "ee2ed6844f1dfca8", "type": "comment", "z": "12a013e7919087d2", "name": "/330/0/5700: Weight Scale Value", "info": "Weight Scale Value", "x": 550, "y": 120, "wires": []}, {"id": "d6e348483c01dfd8", "type": "function", "z": "12a013e7919087d2", "name": "Temperature Sensor S1", "func": "\n\n    // Generate a random float number between 1.0 and 10.0\n//msg.payload = Math.random() * (10.0 - 1.0) + 1.0;\nmsg.payload = 15\n    // msg.payload = 0;\n\n\n// Set the topic value\nmsg.topic = \"/3303/0/5700\";\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 370, "y": 300, "wires": [["9ee4c076c45539ce"]]}, {"id": "c9861d23274359f1", "type": "function", "z": "12a013e7919087d2", "name": "Only send if the value is different from the previous one", "func": "// Get the previous value from context\nlet previousValue = context.get('previousValue');\n\n// Check if the current payload is different from the previous one\nif (msg.payload !== previousValue) {\n    // Save the current value as the new previous value\n    context.set('previousValue', msg.payload);\n    msg.topic = \"/3304/0/5700\"\n    \n    // Forward the message since the value is different\n    return msg;\n} else {\n    // Don't forward the message if the value is the same\n    return null;\n}\n\n\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 700, "y": 360, "wires": [["f9a64815929725ef"]]}, {"id": "f7a2417239ae8d7e", "type": "function", "z": "12a013e7919087d2", "name": "Humidity Sensor S1", "func": "\n\n    // Generate a random float number between 1.0 and 10.0\n//msg.payload = Math.random() * (10.0 - 1.0) + 1.0;\nmsg.payload = 19\n    // msg.payload = 0;\n\n\n// Set the topic value\nmsg.topic = \"/3304/0/5700\";\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 360, "y": 360, "wires": [["c9861d23274359f1"]]}, {"id": "21211f6dc493147a", "type": "lwm2m client in", "z": "12a013e7919087d2", "name": "", "lwm2mClient": "c23f657dbf953847", "subscribeObjectEvents": true, "outputAsObject": false, "x": 130, "y": 660, "wires": [["319d96c85a0a39ae", "29a7d077176891a3"]]}, {"id": "319d96c85a0a39ae", "type": "debug", "z": "12a013e7919087d2", "name": "debug 2", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 280, "y": 600, "wires": []}, {"id": "0a990723ce7432c8", "type": "influxdb out", "z": "12a013e7919087d2", "g": "753503cb939e2954", "influxdb": "3fc00e2ac08b5bfd", "name": "Influxdb", "measurement": "wine1", "precision": "", "retentionPolicy": "", "database": "database", "precisionV18FluxV20": "ms", "retentionPolicyV18Flux": "", "org": "ICT", "bucket": "wine", "x": 320, "y": 700, "wires": []}, {"id": "29a7d077176891a3", "type": "function", "z": "12a013e7919087d2", "g": "753503cb939e2954", "name": "Send Data to DB", "func": "let payload = msg.payload;\nlet value1= \"default\";\nlet value = payload.value.value\nlet uri = payload.uri;\nswitch (uri) {\n    case \"/3300/0/5700\":\n        value1 = \"Storage-Weight\";\n        break;\n    case \"/3303/0/5700\":\n        value1 = \"Storage-Temp\";\n        break;\n    case \"/3304/0/5700\":\n        value1 = \"storage-Humidity\";\n        break;\n    case \"/3308/0/5900\":\n        value1 = \"Storage-AC\";\n        break; \n    case \"/3302/0/5500\":\n        value1 = \"Conveyor-presence\";\n        break;\n    case \"/3300/0/5700\":\n        value1 = \"Conveyor-Weight\";\n        break;\n    case \"/3306/0/5850\":\n        value1 = \"Conver-Actuator\";\n        break;\n    case \"/3302/0/5500\":\n        value1 = \"Fermen-presence\";\n        break;\n    case \"/3303/0/5700\":\n        value1 = \"Fermen-Temp\";\n        break;\n    case \"/3304/0/5700\":\n        value1 = \"Fermen-Humidity\";\n        break;\n    case \"/3306/0/5850\":\n        value1 = \"Fermen-Actuator-3\";\n        break;\n    case \"/3306/1/5850\":\n        value1 = \"Fermen-Actuator-4\";\n        break; \n    case \"/3306/2/5850\":\n        value1 = \"Fermen-Actuator-5\";\n        break;    \n    case \"/3306/3/5850\":\n        value1 = \"Fermen-basic-Actuator\";\n        break; \n    case \"/3306/4/5850\":\n        value1 = \"Fermen-Acidic-Actuator\";\n        break; \n    case \"/3300/4/5700\":\n        value1 = \"Fermen-PH-Sensor\";\n        break; \n    default:\n        //return null; \n       value1 = \"unknown\"; \n}\nswitch (value) {\n    case true:\n        value = 1;\n        break;\n    case false:\n        value = 0;\n        break;\n    //default:\n    //    return null;\n}\nlet timestamp = payload.ts; \n//milliseconds to seconds (InfluxDB uses seconds by default)\ntimestamp = Math.floor(timestamp / 1000);\nmsg.payload = {\n    [value1]: value,  \n   // topic: topic,         \n    timestamp: timestamp \n};\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 330, "y": 660, "wires": [["0a990723ce7432c8", "57f34f592407ea3b"]]}, {"id": "18451df28e4a4781", "type": "function", "z": "12a013e7919087d2", "name": "Only send if the value is different from the previous one", "func": "// Get the previous value from context\nlet previousValue = context.get('previousValue');\n\n// Check if the current payload is different from the previous one\nif (msg.payload !== previousValue) {\n    // Save the current value as the new previous value\n    context.set('previousValue', msg.payload);\n    msg.topic = \"/3300/0/5700\"\n    \n    // Forward the message since the value is different\n    return msg;\n} else {\n    // Don't forward the message if the value is the same\n    return null;\n}\n\n\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 700, "y": 420, "wires": [["f9a64815929725ef"]]}, {"id": "6357feea0513d28c", "type": "function", "z": "12a013e7919087d2", "name": "Weight Sensor S2", "func": "\n\n    // Generate a random float number between 1.0 and 10.0\n//msg.payload = Math.random() * (10.0 - 1.0) + 1.0;\nmsg.payload = 19\n    // msg.payload = 0;\n\n\n// Set the topic value\nmsg.topic = \"/3300/0/5700\";\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 350, "y": 420, "wires": [["18451df28e4a4781"]]}, {"id": "2b97cd71b51d697d", "type": "function", "z": "12a013e7919087d2", "name": "Only send if the value is different from the previous one", "func": "// Get the previous value from context\nlet previousValue = context.get('previousValue');\n\n// Check if the current payload is different from the previous one\nif (msg.payload !== previousValue) {\n    // Save the current value as the new previous value\n    context.set('previousValue', msg.payload);\n    msg.topic = \"/3308/0/5900\"\n    \n    // Forward the message since the value is different\n    return msg;\n} else {\n    // Don't forward the message if the value is the same\n    return null;\n}\n\n\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 700, "y": 480, "wires": [["f9a64815929725ef"]]}, {"id": "94450747cbdaded2", "type": "function", "z": "12a013e7919087d2", "name": "A/C Process A1", "func": "\n\n    // Generate a random float number between 1.0 and 10.0\n//msg.payload = Math.random() * (10.0 - 1.0) + 1.0;\nmsg.payload = 17\n    // msg.payload = 0;\n\n\n// Set the topic value\nmsg.topic = \"/3308/0/5900\";\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 340, "y": 480, "wires": [["2b97cd71b51d697d"]]}, {"id": "1f8772c2ac78defb", "type": "comment", "z": "12a013e7919087d2", "name": "/3308/0/5900: A/C Process 1", "info": "Weight Scale Value", "x": 540, "y": 80, "wires": []}, {"id": "f087f1b0260cf60a", "type": "inject", "z": "12a013e7919087d2", "name": "", "props": [{"p": "payload"}, {"p": "topic", "vt": "str"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "date", "x": 140, "y": 380, "wires": [["d6e348483c01dfd8", "f7a2417239ae8d7e", "6357feea0513d28c", "94450747cbdaded2"]]}, {"id": "57f34f592407ea3b", "type": "debug", "z": "12a013e7919087d2", "name": "debug 5", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 540, "y": 660, "wires": []}, {"id": "d796f7c3b94896a8", "type": "http in", "z": "12a013e7919087d2", "name": "Start Process", "url": "/start/process", "method": "get", "upload": false, "swaggerDoc": "", "x": 90, "y": 100, "wires": [["53b261083c8b342b"]]}, {"id": "53b261083c8b342b", "type": "http response", "z": "12a013e7919087d2", "name": "Server", "statusCode": "", "headers": {}, "x": 90, "y": 140, "wires": []}, {"id": "6e5933fc6cedbb5e", "type": "function", "z": "12a013e7919087d2", "name": "function 1", "func": "var topic = msg.topic;\n//var payload = msg.payload.toString();  // Convert payload to string\n//var payload = msg.payload;\nvar value1 = \"\";\n\nswitch (topic) {\n    case \"/3300/0/5700\":\n        value1 = \"Storage-Weight\";\n        break;\n    case \"/3303/0/5700\":\n        value1 = \"Storage-Temp\";\n        break;\n    case \"/3304/0/5700\":\n        value1 = \"storage-Humidity\";\n        break;\n    case \"/3308/0/5900\":\n        value1 = \"Storage-AC\";\n        break; \n    case \"/3302/0/5500\":\n        value1 = \"Conveyor-presence\";\n        break;\n    case \"/3300/0/5700\":\n        value1 = \"Conveyor-Weight\";\n        break;\n    case \"/3306/0/5850\":\n        value1 = \"Conver-Actuator\";\n        break;\n    case \"/3302/0/5500\":\n        value1 = \"Fermen-presence\";\n        break;\n    case \"/3303/0/5700\":\n        value1 = \"Fermen-Temp\";\n        break;\n    case \"/3304/0/5700\":\n        value1 = \"Fermen-Humidity\";\n        break;\n    case \"/3306/0/5850\":\n        value1 = \"Fermen-Actuator-3\";\n        break;\n    case \"/3306/1/5850\":\n        value1 = \"Fermen-Actuator-4\";\n        break; \n    case \"/3306/2/5850\":\n        value1 = \"Fermen-Actuator-5\";\n        break;    \n    case \"/3306/3/5850\":\n        value1 = \"Fermen-basic-Actuator\";\n        break; \n    case \"/3306/4/5850\":\n        value1 = \"Fermen-Acidic-Actuator\";\n        break; \n    case \"/3300/0/5700\":\n        value1 = \"Fermen-PH-Sensor\";\n        break; \n    default:\n        return null; \n       //value1 = value1; \n}\n\nmsg.topic= value1;\n//msg.payload = payload; \n\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 860, "y": 620, "wires": [["83de9010853e5bef"]]}, {"id": "712d0204b6241b75", "type": "mqtt in", "z": "12a013e7919087d2", "name": "", "topic": "#", "qos": "2", "datatype": "auto-detect", "broker": "407a01e4.6b637", "nl": false, "rap": true, "rh": 0, "inputs": 0, "x": 950, "y": 700, "wires": [["fe01b1b993861a82", "3da3bf507c23badf"]]}, {"id": "fe01b1b993861a82", "type": "debug", "z": "12a013e7919087d2", "name": "debug 14", "active": false, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1140, "y": 740, "wires": []}, {"id": "83de9010853e5bef", "type": "debug", "z": "12a013e7919087d2", "name": "debug 15", "active": false, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1040, "y": 620, "wires": []}, {"id": "3da3bf507c23badf", "type": "lwm2m client out", "z": "12a013e7919087d2", "name": "Write or Execute LwM2M Resources", "lwm2mClient": "c23f657dbf953847", "x": 1190, "y": 700, "wires": []}, {"id": "568b8bc85b3cd37c", "type": "debug", "z": "12a013e7919087d2", "name": "debug 16", "active": false, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 990, "y": 300, "wires": []}, {"id": "a5e0a3ca6ae13e1f", "type": "mqtt in", "z": "674535e0813040d4", "g": "d316f7f3772ed69f", "name": "", "topic": "/3303/0/5700", "qos": "2", "datatype": "auto-detect", "broker": "407a01e4.6b637", "nl": false, "rap": true, "rh": 0, "inputs": 0, "x": 990, "y": 60, "wires": [["75e54dc680c30d09"]]}, {"id": "3dbb81499cd88655", "type": "mqtt out", "z": "674535e0813040d4", "g": "d316f7f3772ed69f", "name": "MQTT PUB", "topic": "", "qos": "0", "retain": "false", "respTopic": "", "contentType": "", "userProps": "", "correl": "", "expiry": "", "broker": "e09022ccd84710a0", "x": 810, "y": 140, "wires": []}, {"id": "6a224dd9191ae610", "type": "function", "z": "674535e0813040d4", "g": "d316f7f3772ed69f", "name": "Temperature P1", "func": "// Generate a random number between 10 and 25\nmsg.payload = Math.floor(Math.random() * (25 - 10 + 1)) + 10;\n\n// Set the topic\nmsg.topic = \"/3303/0/5700\";\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 520, "y": 100, "wires": [["3dbb81499cd88655"]]}, {"id": "c871ca0824b8029d", "type": "function", "z": "674535e0813040d4", "g": "d316f7f3772ed69f", "name": "Humidiy P1", "func": "// Generate a random number between 30 and 70\nmsg.payload = Math.floor(Math.random() * (70 - 30 + 1)) + 30;\n\n// Set the topic\nmsg.topic = \"/3304/0/5700\";\n\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 510, "y": 140, "wires": [["3dbb81499cd88655"]]}, {"id": "8b0f26b238da8e19", "type": "trigger", "z": "674535e0813040d4", "g": "d316f7f3772ed69f", "name": "", "op1": "1", "op2": "0", "op1type": "str", "op2type": "str", "duration": "-5", "extend": false, "overrideDelay": false, "units": "s", "reset": "", "bytopic": "all", "topic": "topic", "outputs": 1, "x": 320, "y": 120, "wires": [["6a224dd9191ae610", "c871ca0824b8029d", "56634b3247a2e675"]]}, {"id": "a7da024289aaca81", "type": "inject", "z": "674535e0813040d4", "g": "d316f7f3772ed69f", "name": "Start", "props": [{"p": "payload"}, {"p": "topic", "vt": "str"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "date", "x": 130, "y": 140, "wires": [["8b0f26b238da8e19"]]}, {"id": "46ca3abe80aca0a8", "type": "mqtt in", "z": "674535e0813040d4", "g": "d316f7f3772ed69f", "name": "", "topic": "/3304/0/5700", "qos": "2", "datatype": "auto-detect", "broker": "407a01e4.6b637", "nl": false, "rap": true, "rh": 0, "inputs": 0, "x": 990, "y": 120, "wires": [["75e54dc680c30d09"]]}, {"id": "75e54dc680c30d09", "type": "debug", "z": "674535e0813040d4", "g": "d316f7f3772ed69f", "name": "debug 3", "active": false, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1160, "y": 120, "wires": []}, {"id": "422fab61599c0d51", "type": "change", "z": "674535e0813040d4", "g": "d316f7f3772ed69f", "name": "", "rules": [{"t": "set", "p": "reset", "pt": "msg", "to": "true", "tot": "str"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 300, "y": 180, "wires": [["8b0f26b238da8e19"]]}, {"id": "3db7e98ceb9bb344", "type": "inject", "z": "674535e0813040d4", "g": "d316f7f3772ed69f", "name": "Stop", "props": [{"p": "payload"}, {"p": "topic", "vt": "str"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "date", "x": 130, "y": 180, "wires": [["422fab61599c0d51"]]}, {"id": "56634b3247a2e675", "type": "function", "z": "674535e0813040d4", "g": "d316f7f3772ed69f", "name": "Grapes Weight P1", "func": "// Retrieve the last stored value from the node's context\nlet lastValue = context.get(\"lastValue\") || 260; \n\n// Decrease by 10\nlet newValue = lastValue - 10;\n\n// Ensure it doesn't go below a certain threshold (optional)\nif (newValue < 0) {\n    newValue = 0;  // Stop at 0, or reset if needed\n}\n\n// Store the new value for the next execution\ncontext.set(\"lastValue\", newValue);\n\n// Set the payload\nmsg.payload = newValue;\nmsg.topic = \"/3300/0/5700\";\n\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 530, "y": 180, "wires": [["3dbb81499cd88655"]]}, {"id": "f43eb7b1d331025c", "type": "mqtt in", "z": "674535e0813040d4", "g": "d316f7f3772ed69f", "name": "", "topic": "/3300/0/5700", "qos": "2", "datatype": "auto-detect", "broker": "407a01e4.6b637", "nl": false, "rap": true, "rh": 0, "inputs": 0, "x": 990, "y": 180, "wires": [["75e54dc680c30d09"]]}, {"id": "9636197923c29c99", "type": "comment", "z": "674535e0813040d4", "g": "d316f7f3772ed69f", "name": "PROCESS 1", "info": "", "x": 1170, "y": 60, "wires": []}, {"id": "23c525b8784f31dd", "type": "inject", "z": "674535e0813040d4", "g": "8c7fb152801a7b0c", "name": "", "props": [{"p": "payload"}, {"p": "topic", "vt": "str"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "date", "x": 140, "y": 320, "wires": [["6afa8c2b1d4089f9"]]}, {"id": "6afa8c2b1d4089f9", "type": "change", "z": "674535e0813040d4", "g": "8c7fb152801a7b0c", "name": "", "rules": [{"t": "set", "p": "reset", "pt": "msg", "to": "true", "tot": "str"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 320, "y": 320, "wires": [["1030808ed2def1d2"]]}, {"id": "961d445328aa32a4", "type": "inject", "z": "674535e0813040d4", "g": "8c7fb152801a7b0c", "name": "", "props": [{"p": "payload"}, {"p": "topic", "vt": "str"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "date", "x": 140, "y": 280, "wires": [["1030808ed2def1d2"]]}, {"id": "1030808ed2def1d2", "type": "trigger", "z": "674535e0813040d4", "g": "8c7fb152801a7b0c", "name": "", "op1": "1", "op2": "0", "op1type": "str", "op2type": "str", "duration": "-1", "extend": false, "overrideDelay": false, "units": "s", "reset": "", "bytopic": "all", "topic": "topic", "outputs": 1, "x": 320, "y": 280, "wires": [["efebe386672db8ac"]]}, {"id": "efebe386672db8ac", "type": "function", "z": "674535e0813040d4", "g": "8c7fb152801a7b0c", "name": "Presence Sensor P2", "func": "// Define the maximum number of times to send \"1\"\nlet maxCount = 10; // Change this as needed\n\n// Retrieve the current count from context\nlet count = context.get(\"count\") || 0;\nlet sendingOnes = context.get(\"sendingOnes\") ?? true; // Track if we are still sending \"1\"\n\n// Check if we should send \"1\" or \"0\"\nif (sendingOnes && count < maxCount) {\n    msg.payload = 1;  // Send \"1\"\n    count++;\n    context.set(\"count\", count);\n} else {\n    msg.payload = 0;  // Switch to sending \"0\"\n    context.set(\"sendingOnes\", false); // Stop sending \"1\" after maxCount is reached\n}\n\nmsg.topic =\"/3302/0/5500\"\n\n// Return the message\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 560, "y": 280, "wires": [["2db4568e8de4ef41", "bad929ae98821397"]]}, {"id": "a8fe44a05ce6818c", "type": "function", "z": "674535e0813040d4", "g": "8c7fb152801a7b0c", "name": "Grapes Weight P2", "func": "// Check if the function has already executed\nlet hasExecuted = context.get(\"hasExecuted\") || false;\n\nif (!hasExecuted) {\n    // Generate a random number between 100 and 150\n    msg.payload = Math.floor(Math.random() * (150 - 100 + 1)) + 100;\n    msg.topic = \"/3300/1/5700\";\n\n    // Set execution flag to true (prevents further execution)\n    context.set(\"hasExecuted\", true);\n\n    return msg;  // Send the message\n} else {\n    return null; // Stop execution after the first run\n}\n\n\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 550, "y": 400, "wires": [["bad929ae98821397"]]}, {"id": "2db4568e8de4ef41", "type": "switch", "z": "674535e0813040d4", "g": "8c7fb152801a7b0c", "name": "", "property": "payload", "propertyType": "msg", "rules": [{"t": "eq", "v": "0", "vt": "num"}], "checkall": "true", "repair": false, "outputs": 1, "x": 510, "y": 340, "wires": [["8ae29fcb75b99928"]]}, {"id": "8ae29fcb75b99928", "type": "delay", "z": "674535e0813040d4", "g": "8c7fb152801a7b0c", "name": "", "pauseType": "delay", "timeout": "5", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "1", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 640, "y": 340, "wires": [["a8fe44a05ce6818c"]]}, {"id": "a77c7ba0e4a936f4", "type": "function", "z": "674535e0813040d4", "g": "8c7fb152801a7b0c", "name": "Level Sensor P2", "func": "// Retrieve the last stored value, default to 0\nlet currentValue = context.get(\"currentValue\") || 0;\n\n// Define the step size and max value\nlet stepSize = 10;\nlet maxValue = msg.payload;\n\n// Increment the value\nif (currentValue < maxValue) {\n    currentValue += stepSize;\n} else {\n    return null; // Stop execution when reaching 110\n}\n\n// Store the new value for the next execution\ncontext.set(\"currentValue\", currentValue);\n\n// Set the payload\nmsg.payload = currentValue;\nmsg.topic = \"/3300/2/5700\";\nreturn msg;\n\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 540, "y": 440, "wires": [["bad929ae98821397"]]}, {"id": "145c1eb087fb77fc", "type": "inject", "z": "674535e0813040d4", "g": "8c7fb152801a7b0c", "name": "", "props": [{"p": "payload"}, {"p": "topic", "vt": "str"}], "repeat": "", "crontab": "", "once": false, "onceDelay": "0.", "topic": "", "payload": "110", "payloadType": "num", "x": 130, "y": 360, "wires": [["a77c7ba0e4a936f4"]]}, {"id": "c11450e27b053ba1", "type": "function", "z": "674535e0813040d4", "g": "8c7fb152801a7b0c", "name": "PH  Sensor ", "func": "// Get the stored value or generate a random one if it's the first run\nlet currentValue = context.get(\"currentValue\") || (Math.random() * (5.00 - 2.00) + 2.00).toFixed(2);\n\n// Convert to a floating point number\ncurrentValue = parseFloat(currentValue);\n\n// Step size for increase/decrease\nlet stepSize = 0.01;\n\n// Adjust value based on payload input\nif (msg.payload === \"ACID\") {\n    currentValue -= stepSize;\n} else if (msg.payload === \"BASE\") {\n    currentValue += stepSize;\n}\n\n// Ensure the value stays between 2.00 and 5.00\nif (currentValue < 2.00) {\n    currentValue = 2.00;\n} else if (currentValue > 5.00) {\n    currentValue = 5.00;\n}\n\n// Store the updated value for next execution\ncontext.set(\"currentValue\", currentValue.toFixed(2));\n\n// Set the new payload\nmsg.payload = currentValue.toFixed(2);\nmsg.topic = \"/3300/3/5700\"\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 530, "y": 480, "wires": [["bad929ae98821397"]]}, {"id": "ce6ad6172302ac38", "type": "inject", "z": "674535e0813040d4", "g": "8c7fb152801a7b0c", "name": "", "props": [{"p": "payload"}, {"p": "topic", "vt": "str"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "date", "x": 140, "y": 400, "wires": [["c11450e27b053ba1"]]}, {"id": "ddc12573296d17e9", "type": "inject", "z": "674535e0813040d4", "g": "8c7fb152801a7b0c", "name": "", "props": [{"p": "payload"}, {"p": "topic", "vt": "str"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "ACID", "payloadType": "str", "x": 130, "y": 440, "wires": [["c11450e27b053ba1"]]}, {"id": "dd5cb416d91412ac", "type": "inject", "z": "674535e0813040d4", "g": "8c7fb152801a7b0c", "name": "", "props": [{"p": "payload"}, {"p": "topic", "vt": "str"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "BASE", "payloadType": "str", "x": 130, "y": 480, "wires": [["c11450e27b053ba1"]]}, {"id": "96bbffecf8bf0ad3", "type": "comment", "z": "674535e0813040d4", "g": "8c7fb152801a7b0c", "name": "PROCESS 2", "info": "", "x": 1170, "y": 280, "wires": []}, {"id": "c0f4de2d7fe06326", "type": "function", "z": "674535e0813040d4", "g": "fc2dbccb6257a01c", "name": "Hydrometer", "func": "// Define starting value and minimum value\nlet startValue = 1.16364;\nlet minValue = 1.0500;\nlet stepSize = 0.01;\n\n// Retrieve the last stored value or use the start value\nlet currentValue = context.get(\"currentValue\") || startValue;\n\n// Convert to floating point\ncurrentValue = parseFloat(currentValue);\n\n// Decrease the value by stepSize\nif (currentValue > minValue) {\n    currentValue -= stepSize;\n} else {\n    return null; // Stop execution when reaching minValue\n}\n\n// Ensure it does not go below minValue\nif (currentValue < minValue) {\n    currentValue = minValue;\n}\n\n// Store the updated value for next execution\ncontext.set(\"currentValue\", currentValue.toFixed(5));\n\n// Set the payload\nmsg.payload = currentValue.toFixed(5);\nmsg.topic = \"/3300/4/5700\";\n\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 510, "y": 580, "wires": [["adf196aedaf2df4f"]]}, {"id": "a61858d718689649", "type": "inject", "z": "674535e0813040d4", "g": "fc2dbccb6257a01c", "name": "", "props": [{"p": "payload"}, {"p": "topic", "vt": "str"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "date", "x": 140, "y": 640, "wires": [["c0f4de2d7fe06326", "d2adc87b2d8d9dd3", "dc70892583d2fd94"]]}, {"id": "d2adc87b2d8d9dd3", "type": "function", "z": "674535e0813040d4", "g": "fc2dbccb6257a01c", "name": "Temperature P1", "func": "// Generate a random number between 10 and 25\nmsg.payload = Math.floor(Math.random() * (25 - 10 + 1)) + 10;\n\n// Set the topic\nmsg.topic = \"/3303/0/5700\";\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 520, "y": 640, "wires": [["adf196aedaf2df4f"]]}, {"id": "dc70892583d2fd94", "type": "function", "z": "674535e0813040d4", "g": "fc2dbccb6257a01c", "name": "Humidiy P1", "func": "// Generate a random number between 30 and 70\nmsg.payload = Math.floor(Math.random() * (70 - 30 + 1)) + 30;\n\n// Set the topic\nmsg.topic = \"/3304/1/5700\";\n\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 510, "y": 700, "wires": [["adf196aedaf2df4f"]]}, {"id": "ebb905f1879fb12f", "type": "comment", "z": "674535e0813040d4", "g": "fc2dbccb6257a01c", "name": "PROCESS 3", "info": "", "x": 1170, "y": 580, "wires": []}, {"id": "adf196aedaf2df4f", "type": "mqtt out", "z": "674535e0813040d4", "g": "fc2dbccb6257a01c", "name": "MQTT PUB", "topic": "", "qos": "0", "retain": "false", "respTopic": "", "contentType": "", "userProps": "", "correl": "", "expiry": "", "broker": "e09022ccd84710a0", "x": 770, "y": 640, "wires": []}, {"id": "bad929ae98821397", "type": "mqtt out", "z": "674535e0813040d4", "g": "8c7fb152801a7b0c", "name": "MQTT PUB", "topic": "", "qos": "0", "retain": "false", "respTopic": "", "contentType": "", "userProps": "", "correl": "", "expiry": "", "broker": "e09022ccd84710a0", "x": 830, "y": 360, "wires": []}, {"id": "b58f7d6d59b2aa9d", "type": "mqtt in", "z": "674535e0813040d4", "g": "8c7fb152801a7b0c", "name": "", "topic": "/3302/0/5500", "qos": "2", "datatype": "auto-detect", "broker": "407a01e4.6b637", "nl": false, "rap": true, "rh": 0, "inputs": 0, "x": 990, "y": 280, "wires": [["a85f85d277f30320"]]}, {"id": "a85f85d277f30320", "type": "debug", "z": "674535e0813040d4", "g": "8c7fb152801a7b0c", "name": "debug 12", "active": false, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1160, "y": 360, "wires": []}, {"id": "faaa5209fda3e4d5", "type": "mqtt in", "z": "674535e0813040d4", "g": "8c7fb152801a7b0c", "name": "", "topic": "/3300/1/5700", "qos": "2", "datatype": "auto-detect", "broker": "407a01e4.6b637", "nl": false, "rap": true, "rh": 0, "inputs": 0, "x": 990, "y": 340, "wires": [["a85f85d277f30320"]]}, {"id": "d5a869a1db4f924e", "type": "mqtt in", "z": "674535e0813040d4", "g": "8c7fb152801a7b0c", "name": "", "topic": "/3300/3/5700", "qos": "2", "datatype": "auto-detect", "broker": "407a01e4.6b637", "nl": false, "rap": true, "rh": 0, "inputs": 0, "x": 990, "y": 460, "wires": [["a85f85d277f30320"]]}, {"id": "07fe1aef2e50a341", "type": "mqtt in", "z": "674535e0813040d4", "g": "8c7fb152801a7b0c", "name": "", "topic": "/3300/2/5700", "qos": "2", "datatype": "auto-detect", "broker": "407a01e4.6b637", "nl": false, "rap": true, "rh": 0, "inputs": 0, "x": 990, "y": 400, "wires": [["a85f85d277f30320"]]}, {"id": "3efcc8aaf9150be1", "type": "mqtt in", "z": "674535e0813040d4", "g": "fc2dbccb6257a01c", "name": "", "topic": "/3303/2/5700", "qos": "2", "datatype": "auto-detect", "broker": "407a01e4.6b637", "nl": false, "rap": true, "rh": 0, "inputs": 0, "x": 950, "y": 640, "wires": [["ef21238aac8ce12b"]]}, {"id": "7d137c76a5e06beb", "type": "mqtt in", "z": "674535e0813040d4", "g": "fc2dbccb6257a01c", "name": "", "topic": "/3300/4/5700", "qos": "2", "datatype": "auto-detect", "broker": "407a01e4.6b637", "nl": false, "rap": true, "rh": 0, "inputs": 0, "x": 950, "y": 580, "wires": [["ef21238aac8ce12b"]]}, {"id": "ef21238aac8ce12b", "type": "debug", "z": "674535e0813040d4", "g": "fc2dbccb6257a01c", "name": "debug 13", "active": false, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1120, "y": 640, "wires": []}, {"id": "a82db578e860893b", "type": "mqtt in", "z": "674535e0813040d4", "g": "fc2dbccb6257a01c", "name": "", "topic": "/3304/1/5700", "qos": "2", "datatype": "auto-detect", "broker": "407a01e4.6b637", "nl": false, "rap": true, "rh": 0, "inputs": 0, "x": 950, "y": 700, "wires": [["ef21238aac8ce12b"]]}, {"id": "b3980caa1a3789e7", "type": "function", "z": "674535e0813040d4", "name": "Send Data to DB", "func": "let payload = msg.payload;\nlet value1= \"default\";\nlet value = payload.value.value\nlet uri = payload.uri;\nswitch (uri) {\n    case \"/3300/0/5700\":\n        value1 = \"Storage-Weight\";\n        break;\n    case \"/3303/0/5700\":\n        value1 = \"Storage-Temp\";\n        break;\n    case \"/3304/0/5700\":\n        value1 = \"storage-Humidity\";\n        break;\n    case \"/3308/0/5900\":\n        value1 = \"Storage-AC\";\n        break; \n        ///////////////\n    case \"/3302/0/5500\":\n        value1 = \"Conveyor-presence\";\n        break;\n    case \"/3300/1/5700\":\n        value1 = \"Conveyor-Weight\";\n        break;\n    case \"/3306/0/5850\":\n        value1 = \"Conveyor-Actuator\";\n        break;\n        ////////////////\n    case \"/3300/2/5700\":\n        value1 = \"Level-Sensor\";\n        break;    \n    case \"/3300/3/5700\":\n        value1 = \"PH-Sensor\";\n        break;\n    case \"/3300/4/5700\":\n        value1 = \"Hydrometer\";\n        break;\n    case \"/3303/2/5700\":\n        value1 = \"Fermen-Temp\";\n        break;\n    case \"/3304/1/5700\":\n        value1 = \"Fermen-Humidity\";\n        break;\n    case \"/3306/0/5850\":\n        value1 = \"Fermen-Actuator-3\";\n        break;\n    case \"/3306/1/5850\":\n        value1 = \"Fermen-Actuator-4\";\n        break; \n    case \"/3306/2/5850\":\n        value1 = \"Fermen-Actuator-5\";\n        break;    \n    case \"/3306/3/5850\":\n        value1 = \"Fermen-basic-Actuator\";\n        break; \n    case \"/3306/4/5850\":\n        value1 = \"Fermen-Acidic-Actuator\";\n        break;  \n    default:\n        //return null; \n       value1 = \"000\"; \n}\nswitch (value) {\n    case true:\n        value = 1;\n        break;\n    case false:\n        value = 0;\n        break;\n    //default:\n    //    return null;\n}\nlet timestamp = payload.ts; \n//milliseconds to seconds (InfluxDB uses seconds by default)\ntimestamp = Math.floor(timestamp / 1000);\nmsg.payload = {\n    [value1]: value,  \n   // topic: topic,         \n    timestamp: timestamp \n};\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1370, "y": 340, "wires": [[]]}, {"id": "86417462338605df", "type": "lwm2m client in", "z": "384c213e7099162d", "name": "", "lwm2mClient": "c23f657dbf953847", "subscribeObjectEvents": true, "outputAsObject": false, "x": 410, "y": 440, "wires": [["c235e509393106fd"]]}, {"id": "a525aa537e5a96ad", "type": "influxdb out", "z": "384c213e7099162d", "influxdb": "3fc00e2ac08b5bfd", "name": "Influxdb", "measurement": "wine1", "precision": "", "retentionPolicy": "", "database": "database", "precisionV18FluxV20": "ms", "retentionPolicyV18Flux": "", "org": "ICT", "bucket": "wine", "x": 780, "y": 440, "wires": []}, {"id": "c235e509393106fd", "type": "function", "z": "384c213e7099162d", "name": "Send Data to DB", "func": "let payload = msg.payload;\nlet value1= \"default\";\nlet value = payload.value.value\nlet uri = payload.uri;\nswitch (uri) {\n    case \"/3300/0/5700\":\n        value1 = \"Storage-Weight\";\n        break;\n    case \"/3303/0/5700\":\n        value1 = \"Storage-Temp\";\n        break;\n    case \"/3304/0/5700\":\n        value1 = \"storage-Humidity\";\n        break;\n    case \"/3308/0/5900\":\n        value1 = \"Storage-AC\";\n        break; \n    case \"/3302/0/5500\":\n        value1 = \"Conveyor-presence\";\n        break;\n    case \"/3300/0/5700\":\n        value1 = \"Conveyor-Weight\";\n        break;\n    case \"/3306/0/5850\":\n        value1 = \"Conver-Actuator\";\n        break;\n    case \"/3302/0/5500\":\n        value1 = \"Fermen-presence\";\n        break;\n    case \"/3303/0/5700\":\n        value1 = \"Fermen-Temp\";\n        break;\n    case \"/3304/0/5700\":\n        value1 = \"Fermen-Humidity\";\n        break;\n    case \"/3306/0/5850\":\n        value1 = \"Fermen-Actuator-3\";\n        break;\n    case \"/3306/1/5850\":\n        value1 = \"Fermen-Actuator-4\";\n        break; \n    case \"/3306/2/5850\":\n        value1 = \"Fermen-Actuator-5\";\n        break;    \n    case \"/3306/3/5850\":\n        value1 = \"Fermen-basic-Actuator\";\n        break; \n    case \"/3306/4/5850\":\n        value1 = \"Fermen-Acidic-Actuator\";\n        break; \n    case \"/3300/4/5700\":\n        value1 = \"Fermen-PH-Sensor\";\n        break; \n    default:\n        //return null; \n       value1 = \"unknown\"; \n}\nswitch (value) {\n    case true:\n        value = 1;\n        break;\n    case false:\n        value = 0;\n        break;\n    //default:\n    //    return null;\n}\nlet timestamp = payload.ts; \n//milliseconds to seconds (InfluxDB uses seconds by default)\ntimestamp = Math.floor(timestamp / 1000);\nmsg.payload = {\n    [value1]: value,  \n   // topic: topic,         \n    timestamp: timestamp \n};\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 590, "y": 440, "wires": [["a525aa537e5a96ad"]]}, {"id": "e25bf376e15875c0", "type": "lwm2m client out", "z": "384c213e7099162d", "name": "", "lwm2mClient": "9571bffdf4883999", "x": 770, "y": 320, "wires": []}, {"id": "2246f8b994b838dd", "type": "function", "z": "384c213e7099162d", "name": "function 2", "func": "\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 600, "y": 320, "wires": [["e25bf376e15875c0"]]}, {"id": "7c4b1a1b221c52e0", "type": "mqtt in", "z": "384c213e7099162d", "name": "", "topic": "/3303/0/5700", "qos": "2", "datatype": "auto-detect", "broker": "e09022ccd84710a0", "nl": false, "rap": true, "rh": 0, "inputs": 0, "x": 390, "y": 320, "wires": [["2246f8b994b838dd"]]}, {"id": "d71ad4d516cd935f", "type": "http request", "z": "f3434801fe70a0c7", "name": "", "method": "POST", "ret": "obj", "paytoqs": "ignore", "url": "http://*************:5000/receive", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [{"keyType": "Content-Type", "keyValue": "", "valueType": "application/json", "valueValue": ""}], "x": 870, "y": 380, "wires": [["e434a9c0c4cd8fe1"]]}, {"id": "458b19250f6ecb73", "type": "influxdb in", "z": "f3434801fe70a0c7", "influxdb": "3fc00e2ac08b5bfd", "name": "Influx", "query": "from(bucket: \"wine\")\n  |> range(start: 0)  // `0` fetches data from the earliest available timestamp\n  |> filter(fn: (r) => r[\"_measurement\"] == \"wine1\")\n  |> yield(name: \"all_data\")  // Optional for clear labeling\n", "rawOutput": false, "precision": "", "retentionPolicy": "", "org": "ICT", "x": 390, "y": 380, "wires": [["3fc954c66a3adecc", "c0a8b2522d6cb8f0"]]}, {"id": "3fc954c66a3adecc", "type": "function", "z": "f3434801fe70a0c7", "name": "function 3", "func": "// Ensure data is an array\nif (!Array.isArray(msg.payload)) {\n    node.error(\"Payload is not an array\", msg);\n    return null;\n}\n\n// Map and format the data correctly\nmsg.payload = msg.payload.map(row => ({\n    timestamp: row._time,             // Correctly extract the timestamp\n    measurement: row._measurement,    // Extract measurement\n    field: row._field,                // Extract field\n    value: row._value                 // Extract value\n}));\n\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 540, "y": 380, "wires": [["2d24365904822344", "07423f00494126b0"]]}, {"id": "d6bfb925b71d45f9", "type": "inject", "z": "f3434801fe70a0c7", "name": "", "props": [{"p": "payload"}, {"p": "topic", "vt": "str"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "date", "x": 240, "y": 380, "wires": [["458b19250f6ecb73"]]}, {"id": "c0a8b2522d6cb8f0", "type": "debug", "z": "f3434801fe70a0c7", "name": "debug 17", "active": false, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 560, "y": 300, "wires": []}, {"id": "e434a9c0c4cd8fe1", "type": "debug", "z": "f3434801fe70a0c7", "name": "debug 18", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1040, "y": 380, "wires": []}, {"id": "2d24365904822344", "type": "debug", "z": "f3434801fe70a0c7", "name": "debug 19", "active": false, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 740, "y": 300, "wires": []}, {"id": "07423f00494126b0", "type": "function", "z": "f3434801fe70a0c7", "name": "function 4", "func": "const chunkSize = 1000;  \nconst chunks = [];\n\nfor (let i = 0; i < msg.payload.length; i += chunkSize) {\n    chunks.push({\n        payload: msg.payload.slice(i, i + chunkSize)  // Split the data\n    });\n}\n\nreturn [chunks];\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 700, "y": 380, "wires": [["d71ad4d516cd935f"]]}, {"id": "7e58905ef5bba452", "type": "comment", "z": "f3434801fe70a0c7", "name": "", "info": "from(bucket: \"wine\")\n  |> range(start: 0)  // `0` fetches data from the earliest available timestamp\n  |> filter(fn: (r) => r[\"_measurement\"] == \"wine1\")\n  |> filter(fn: (r) => \n      r[\"_field\"] == \"storage-Humidity\" or\n      r[\"_field\"] == \"Storage-Weight\" or\n      r[\"_field\"] == \"Storage-Temp\" or\n      r[\"_field\"] == \"Fermen-PH-Sensor\" or\n      r[\"_field\"] == \"Conveyor-presence\"\n  )\n  |> yield(name: \"all_data\")  // Optional for clear labeling\n", "x": 240, "y": 260, "wires": []}, {"id": "33a653b5fd4a4817", "type": "lwm2m client in", "z": "f3434801fe70a0c7", "name": "", "lwm2mClient": "c23f657dbf953847", "subscribeObjectEvents": true, "outputAsObject": false, "x": 550, "y": 460, "wires": [["adedf9191cd5b099"]]}, {"id": "adedf9191cd5b099", "type": "function", "z": "f3434801fe70a0c7", "name": "function 5", "func": "let payload = msg.payload;\nlet value1= \"default\";\nlet value = payload.value.value\nlet uri = payload.uri;\nswitch (uri) {\n    case \"/3300/0/5700\":\n        value1 = \"Storage-Weight\";\n        break;\n    case \"/3303/0/5700\":\n        value1 = \"Storage-Temp\";\n        break;\n    case \"/3304/0/5700\":\n        value1 = \"storage-Humidity\";\n        break;\n    case \"/3308/0/5900\":\n        value1 = \"Storage-AC\";\n        break; \n    case \"/3302/0/5500\":\n        value1 = \"Conveyor-presence\";\n        break;\n    case \"/3300/0/5700\":\n        value1 = \"Conveyor-Weight\";\n        break;\n    case \"/3306/0/5850\":\n        value1 = \"Conver-Actuator\";\n        break;\n    case \"/3302/0/5500\":\n        value1 = \"Fermen-presence\";\n        break;\n    case \"/3303/0/5700\":\n        value1 = \"Fermen-Temp\";\n        break;\n    case \"/3304/0/5700\":\n        value1 = \"Fermen-Humidity\";\n        break;\n    case \"/3306/0/5850\":\n        value1 = \"Fermen-Actuator-3\";\n        break;\n    case \"/3306/1/5850\":\n        value1 = \"Fermen-Actuator-4\";\n        break; \n    case \"/3306/2/5850\":\n        value1 = \"Fermen-Actuator-5\";\n        break;    \n    case \"/3306/3/5850\":\n        value1 = \"Fermen-basic-Actuator\";\n        break; \n    case \"/3306/4/5850\":\n        value1 = \"Fermen-Acidic-Actuator\";\n        break; \n    case \"/3300/4/5700\":\n        value1 = \"Fermen-PH-Sensor\";\n        break; \n    default:\n        //return null; \n       value1 = \"unknown\"; \n}\nswitch (value) {\n    case true:\n        value = 1;\n        break;\n    case false:\n        value = 0;\n        break;\n    //default:\n    //    return null;\n}\nlet timestamp = payload.ts; \n//milliseconds to seconds (InfluxDB uses seconds by default)\ntimestamp = Math.floor(timestamp / 1000);\nmsg.payload = {\n    [value1]: value,  \n   // topic: topic,         \n    timestamp: timestamp \n};\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 700, "y": 460, "wires": [["d71ad4d516cd935f"]]}]