[{"id": "8056a2538439382a", "type": "tab", "label": "Washing", "disabled": false, "info": "", "env": []}, {"id": "cda5c6113e87d3f0", "type": "tab", "label": "Conveyor", "disabled": true, "info": "", "env": []}, {"id": "59bd685206d338c7", "type": "group", "z": "8056a2538439382a", "name": "HW PART S P2", "style": {"label": true, "stroke": "#0070c0"}, "nodes": ["d6e348483c01dfd8"], "x": 434, "y": 279, "w": 252, "h": 82}, {"id": "613f63d79b782acc", "type": "group", "z": "8056a2538439382a", "name": "HW PART A P3", "style": {"stroke": "#0070c0", "label": true}, "nodes": ["c8cb51e56405b902", "d04bd1f69ec62e37"], "x": 734, "y": 579, "w": 332, "h": 242}, {"id": "b62ab9bad6e206f6", "type": "group", "z": "8056a2538439382a", "name": "LESHAN CLIENT ", "style": {"stroke": "#92d04f", "label": true}, "nodes": ["48fbd7d3f7dc3caf"], "x": 174, "y": 519, "w": 252, "h": 82}, {"id": "b50014e557f437a7", "type": "group", "z": "8056a2538439382a", "name": "LESHAN CLIENT", "style": {"stroke": "#92d04f", "label": true}, "nodes": ["7f3a32c998a99d42", "eee39e6bbd2928c9", "a6603c66eac3878a", "a2b1803f60e5fcf0", "f7f137b219e42f01"], "x": 1094, "y": 179, "w": 352, "h": 722}, {"id": "7569f6e998040ed3", "type": "group", "z": "8056a2538439382a", "name": "HW Part S P2", "style": {"stroke": "#0070c0", "label": true}, "nodes": ["a0cb7e0b136e2961", "cd1afc4b7e355601", "887aee97409b296a", "9f5c5eec6d6f0967"], "x": 54, "y": 139, "w": 472, "h": 122}, {"id": "b0db00be41ef17b4", "type": "group", "z": "8056a2538439382a", "name": "Influx DB", "style": {"stroke": "#3f5787", "label": true}, "nodes": ["378c6409ef91ecb3", "85f6d1363c5b443a"], "x": 194, "y": 719, "w": 352, "h": 262}, {"id": "e0d77326a49c99a4", "type": "group", "z": "cda5c6113e87d3f0", "name": "InFlux DB", "style": {"stroke": "#6f2fa0", "label": true}, "nodes": ["be6429cb67a6f6cd", "1d9963bf7e12665c"], "x": 154, "y": 439, "w": 232, "h": 122}, {"id": "3fc00e2ac08b5bfd", "type": "influxdb", "hostname": "127.0.0.1", "port": "8086", "protocol": "http", "database": "database", "name": "ICT", "usetls": false, "tls": "", "influxdbVersion": "2.0", "url": "http://influxdb-0.influxdb-headless.default.svc.cluster.local:8086", "timeout": "10", "rejectUnauthorized": true}, {"id": "0eaea711b6b3b72c", "type": "lwm2m client", "disabled": false, "lazyStart": false, "clientName": "WASHING-MACHINE", "enableDTLS": false, "clientPort": "56832", "lifetimeSec": "300", "reconnectSec": "60", "bootstrapIntervalSec": "3600", "maxRecvPacketSize": "16486", "requestBootstrap": false, "saveProvisionedConfig": false, "useIPv4": true, "serverHost": "leshan-0.leshan-headless.default.svc.cluster.local", "serverPort": "5683", "redirectLwm2mClientLog": false, "dumpLwm2mMessages": false, "hideSensitiveInfo": true, "propagateInternalEvents": false, "objects": "{\"3300\":{\"0\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0}},\"1\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0}}},\"3302\":{\"0\":{\"2\":true,\"5500\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}},\"1\":{\"2\":true,\"5500\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}},\"2\":{\"2\":true,\"5500\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}}},\"3303\":{\"0\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0}}},\"3306\":{\"0\":{\"2\":true,\"5850\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}},\"1\":{\"2\":true,\"5850\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}},\"2\":{\"2\":true,\"5850\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}},\"3\":{\"2\":true,\"5850\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false},\"5851\":{\"type\":\"INTEGER\",\"acl\":\"RW\",\"value\":0}},\"4\":{\"2\":true,\"5850\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false},\"5851\":{\"type\":\"INTEGER\",\"acl\":\"RW\",\"value\":0}},\"5\":{\"2\":true,\"5850\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}}}}"}, {"id": "3cb20fbce3341ff5", "type": "lwm2m client", "d": true, "disabled": false, "lazyStart": false, "clientName": "Conver", "enableDTLS": false, "clientPort": "56832", "lifetimeSec": "300", "reconnectSec": "60", "bootstrapIntervalSec": "3600", "maxRecvPacketSize": "16486", "requestBootstrap": false, "saveProvisionedConfig": false, "useIPv4": true, "serverHost": "leshan-0.leshan-headless.default.svc.cluster.local", "serverPort": "5683", "redirectLwm2mClientLog": false, "dumpLwm2mMessages": false, "hideSensitiveInfo": true, "propagateInternalEvents": false, "objects": "{\"3300\":{\"0\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0}}},\"3302\":{\"0\":{\"2\":true,\"5500\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}}},\"3306\":{\"0\":{\"2\":true,\"5850\":{\"type\":\"BOOLEAN\",\"acl\":\"RW\",\"value\":false}}}}"}, {"id": "ac758b9854381cde", "type": "function", "z": "8056a2538439382a", "name": "Water Flow Sensor AVG", "func": "// Get the stored values array from context, or initialize a new one\nlet values = context.get('values') || [];\n\n// Add the current payload value to the array\nvalues.push(msg.payload);\n\n// If we have 10 values, calculate the average\nif (values.length === 5) {\n    let sum = values.reduce((a, b) => a + b, 0);\n    let average = sum / values.length;\n    \n    // Set the average as the payload\n    msg.payload = average;\n    msg.topic = \"/3300/0/5700\";\n    \n    // Clear the stored values\n    context.set('values', []);\n    \n    // Send the message with the average to the next node\n    return msg;\n} else {\n    // Save the updated values array in context\n    context.set('values', values);\n    \n    // Don't send a message until we have 10 values\n    return null;\n}", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 850, "y": 320, "wires": [["eee39e6bbd2928c9"]]}, {"id": "0a33b72628575eeb", "type": "function", "z": "8056a2538439382a", "name": "Temperature Sensor AVG", "func": "// Get the stored values array from context, or initialize a new one\nlet values = context.get('values') || [];\n\n// Add the current payload value to the array\nvalues.push(msg.payload);\n\n// If we have 10 values, calculate the average\nif (values.length === 5) {\n    let sum = values.reduce((a, b) => a + b, 0);\n    let average = sum / values.length;\n    \n    // Set the average as the payload\n    msg.payload = average;\n    msg.topic = \"/3303/0/5700\";\n    \n    // Clear the stored values\n    context.set('values', []);\n    \n    // Send the message with the average to the next node\n    return msg;\n} else {\n    // Save the updated values array in context\n    context.set('values', values);\n    \n    // Don't send a message until we have 10 values\n    return null;\n}", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 850, "y": 400, "wires": [["a6603c66eac3878a"]]}, {"id": "45743c7fadfac8a5", "type": "comment", "z": "8056a2538439382a", "name": "\"/3300/0/5700\"", "info": "", "x": 840, "y": 280, "wires": []}, {"id": "9f1e86ce0e69e5f9", "type": "comment", "z": "8056a2538439382a", "name": "\"/3303/0/5700\"", "info": "", "x": 840, "y": 360, "wires": []}, {"id": "baeaa4178933121e", "type": "comment", "z": "8056a2538439382a", "name": "/3302/1/5500", "info": "", "x": 830, "y": 180, "wires": []}, {"id": "0f99a61f095af5bd", "type": "comment", "z": "8056a2538439382a", "name": "Washing machine", "info": "", "x": 810, "y": 60, "wires": []}, {"id": "c7016290914a0cba", "type": "function", "z": "8056a2538439382a", "name": "Only send if the value is different from the previous one", "func": "// Get the previous value from context\nlet previousValue = context.get('previousValue');\n\n// Check if the current payload is different from the previous one\nif (msg.payload !== previousValue) {\n    // Save the current value as the new previous value\n    context.set('previousValue', msg.payload);\n    msg.topic = \"/3302/1/5500\"\n    \n    // Forward the message since the value is different\n    return msg;\n} else {\n    // Don't forward the message if the value is the same\n    return null;\n}\n\n\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 780, "y": 220, "wires": [["7f3a32c998a99d42"]]}, {"id": "9b94c21009439961", "type": "function", "z": "8056a2538439382a", "name": "Update Actuator", "func": "// Step 1: Filter based on the URI\nif (msg.payload.uri === \"/3302/1/5500\") {\n    \n    // Step 2: Check the value to determine if it's true or false\n    if (msg.payload.value && msg.payload.value.value === true) {\n        // If the value is true, set the payload to \"GO\" and send immediately\n        setTimeout(function() {\n        msg.payload = true;\n        msg.topic = \"/3306/2/5850\"\n        node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 1000);\n    } else if (msg.payload.value && msg.payload.value.value === false) {\n        // If the value is false, delay for 5 seconds before sending \"STOP\"\n        setTimeout(function() {\n            msg.payload = false;\n            msg.topic = \"/3306/2/5850\"\n            node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 5000);\n    } else {\n        // If the value is neither true nor false, handle it accordingly\n        msg.payload = \"UNKNOWN\";\n        node.send(msg);  // Send the message if the value is unknown\n    }\n\n} else {\n    // If the URI doesn't match, ignore the message\n    return null;\n}", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 580, "y": 560, "wires": [["a2b1803f60e5fcf0"]]}, {"id": "0d856283790d5710", "type": "function", "z": "8056a2538439382a", "name": "Activate Actuator", "func": "// Step 1: Filter based on the URI\nif (msg.payload.uri === \"/3302/1/5500\") {\n    \n    // Step 2: Check the value to determine if it's true or false\n    if (msg.payload.value && msg.payload.value.value === true) {\n        // If the value is true, set the payload to \"GO\" and send immediately\n        setTimeout(function() {\n        msg.payload = \"GO\";\n        node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 1000);\n    } else if (msg.payload.value && msg.payload.value.value === false) {\n        // If the value is false, delay for 5 seconds before sending \"STOP\"\n        setTimeout(function() {\n            msg.payload = \"STOP\";\n            node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 5000);\n    } else {\n        // If the value is neither true nor false, handle it accordingly\n        msg.payload = \"UNKNOWN\";\n        node.send(msg);  // Send the message if the value is unknown\n    }\n\n} else {\n    // If the URI doesn't match, ignore the message\n    return null;\n}", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 590, "y": 620, "wires": [["c8cb51e56405b902"]]}, {"id": "380ad83c05214a9f", "type": "comment", "z": "8056a2538439382a", "name": "/3306/2/5850", "info": "", "x": 590, "y": 660, "wires": []}, {"id": "45526bc482f42924", "type": "function", "z": "8056a2538439382a", "name": "Update Actuator", "func": "// Step 1: Filter based on the URI\nif (msg.payload.uri === \"/3302/1/5500\") {\n    \n    // Step 2: Check the value to determine if it's true or false\n    if (msg.payload.value && msg.payload.value.value === true) {\n        // If the value is true, set the payload to \"GO\" and send immediately\n        setTimeout(function() {\n        msg.payload = true;\n        msg.topic = \"/3306/3/5850\"\n        node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 1000);\n    } else if (msg.payload.value && msg.payload.value.value === false) {\n        // If the value is false, delay for 5 seconds before sending \"STOP\"\n        setTimeout(function() {\n            msg.payload = false;\n            msg.topic = \"/3306/3/5850\"\n            node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 5000);\n    } else {\n        // If the value is neither true nor false, handle it accordingly\n        msg.payload = \"UNKNOWN\";\n        node.send(msg);  // Send the message if the value is unknown\n    }\n\n} \n\nelse if (msg.payload.uri === \"/3300/0/5700\") {\n    \n    // Step 2: Check the value to determine if it's true or false\n    if (msg.payload.value && msg.payload.value.value > 5.0) {\n        // If the value is true, set the payload to \"GO\" and send immediately\n        setTimeout(function() {\n        msg.payload = 60;\n        msg.topic = \"/3306/3/5851\"\n        node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 1000);\n    } else {\n        setTimeout(function() {\n        msg.payload = 30;\n        msg.topic = \"/3306/3/5851\"\n        node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 1000);\n    }\n\n}\n\n\n\n\nelse {\n    // If the URI doesn't match, ignore the message\n    return null;\n}", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 580, "y": 860, "wires": [["f7f137b219e42f01"]]}, {"id": "c9a655dadaa0a0b7", "type": "function", "z": "8056a2538439382a", "name": "Activate Actuator", "func": "// Step 1: Filter based on the URI\nif (msg.payload.uri === \"/3302/1/5500\") {\n    \n    // Step 2: Check the value to determine if it's true or false\n    if (msg.payload.value && msg.payload.value.value === true) {\n        // If the value is true, set the payload to \"GO\" and send immediately\n        setTimeout(function() {\n        msg.payload = \"GO\";\n        node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 1000);\n    } else if (msg.payload.value && msg.payload.value.value === false) {\n        // If the value is false, delay for 5 seconds before sending \"STOP\"\n        setTimeout(function() {\n            msg.payload = \"STOP\";\n            node.send(msg);  // Use node.send to send the \"STOP\" after delay\n        }, 5000);\n    } else {\n        // If the value is neither true nor false, handle it accordingly\n        msg.payload = \"UNKNOWN\";\n        node.send(msg);  // Send the message if the value is unknown\n    }\n\n} else {\n    // If the URI doesn't match, ignore the message\n    return null;\n}", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 590, "y": 780, "wires": [["d04bd1f69ec62e37"]]}, {"id": "c1349acb59413c61", "type": "comment", "z": "8056a2538439382a", "name": "/3306/3/5850", "info": "", "x": 590, "y": 900, "wires": []}, {"id": "378c6409ef91ecb3", "type": "function", "z": "8056a2538439382a", "g": "b0db00be41ef17b4", "name": "Send Data to DB", "func": "let payload = msg.payload;\nlet value1= \"default\";\nlet value = payload.value.value\nlet uri = payload.uri;\nswitch (uri) {\n    case \"/3306/0/5850\":\n        value1 = \"M-defoliator-conveyor\";\n        break;\n    case \"/3306/1/5850\":\n        value1 = \"M-defoliator\";\n        break;\n    case \"/3306/2/5850\":\n        value1 = \"M-wm-conveyor\";\n        break;\n    case \"/3306/3/5850\":\n        value1 = \"M-waterPump\";\n        break;\n    case \"/3306/3/5851\":\n        value1 = \"M-waterpump-dimmer\";\n        break;\n    case \"/3306/4/5850\":\n        value1 = \"M-cm\";\n        break;\n    case \"/3306/4/5851\":\n        value1 = \"M-cm-dimmer\";\n        break;\n    case \"/3306/5/5850\":\n        value1 = \"M-cm-conveyor\";\n        break;\n    case \"/3303/0/5700\":\n        value1 = \"S-Temperature\";\n        break;\n    case \"/3302/0/5500\":\n        value1 = \"S-Presence-one\";\n        break;\n    case \"/3302/1/5500\":\n        value1 = \"S-Presence-two\";\n        break;\n    case \"/3302/2/5500\":\n        value1 = \"S-Presence-three\";\n        break;\n    case \"/3300/0/5700\":\n        value1 = \"S-water-flow\";\n        break;   \n    case \"/3300/1/5700\":\n        value1 = \"S-viscosity-p\";\n        break;    \n    default:\n       value1 = value1;  \n}\nswitch (value) {\n    case true:\n        value = 1;\n        break;\n    case false:\n        value = 0;\n        break;\n    //default:\n     //   value = value;\n}\nlet timestamp = payload.ts; \n//milliseconds to seconds (InfluxDB uses seconds by default)\ntimestamp = Math.floor(timestamp / 1000);\nmsg.payload = {\n    [value1]: value,  \n    uri: uri,         \n    timestamp: timestamp \n};\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 310, "y": 760, "wires": [["9fa4cc6ed4967fe4"]]}, {"id": "85f6d1363c5b443a", "type": "influxdb out", "z": "8056a2538439382a", "g": "b0db00be41ef17b4", "influxdb": "3fc00e2ac08b5bfd", "name": "Influxdb", "measurement": "nodered", "precision": "", "retentionPolicy": "", "database": "database", "precisionV18FluxV20": "ms", "retentionPolicyV18Flux": "", "org": "ICT", "bucket": "data2", "x": 460, "y": 940, "wires": []}, {"id": "5c0919a317021f14", "type": "http in", "z": "8056a2538439382a", "name": "Start washing Machine", "url": "/start/washing", "method": "get", "upload": false, "swaggerDoc": "", "x": 120, "y": 60, "wires": [["c946436e199179ed", "887aee97409b296a"]]}, {"id": "a0cb7e0b136e2961", "type": "function", "z": "8056a2538439382a", "g": "7569f6e998040ed3", "name": "loop", "func": "function getRandomInt(min, max) {\n  return Math.floor(Math.random() * (max - min +1)) + min;\n}\n\ncontext.loop = context.loop || \"stop\";\ncontext.loops = context.loops || 0;\n\nswitch (msg.payload) {\n\tcase \"stop\":\n\t\tcontext.loops = context.loops + 1;\n\t\tmsg.payload = \"stopped\";\n\t\tcontext.loop = \"stop\";\n\t\treturn [msg,null];\n\tcase \"toggle\":\n\t\tif (context.loop == \"start\") {\n\t\t\tmsg.payload = \"stopped\";\n\t\t\tcontext.loop = \"stop\";\n\t\t\treturn [msg,null];\n\t\t} else {\n\t\t\tmsg.payload = \"started\";\n\t\t\tcontext.loop = \"loop\";\n\t\t\tcontext.loops = 1;\n\t\t\treturn [msg,msg];\n\t\t}\n\tcase \"start\":\n\t\tmsg.payload = 20;\n\t\tcontext.loop = \"loop\";\n\t\tcontext.loops = 1;\n\t\treturn [msg,msg];\n\tdefault:\n\t\tif (context.loop == \"loop\") {\n\t\t\tcontext.loops = context.loops + 1;\n\t\t\tif (msg.payload > 40) {\n\t\t\t    msg.payload = msg.payload + getRandomInt(-4, 0)\n\t\t\t}\n\t\t\telse if (msg.payload < -20) {\n\t\t\t    msg.payload = msg.payload + getRandomInt(-4, 0)\n\t\t\t}\n\t\t\telse {\n\t\t\t    msg.payload = msg.payload + getRandomInt(-3, 3);\n\t\t\t}\n\t\t\treturn [msg,msg];\n\t\t} else {\n\t\t\treturn [null,null]; \n\t\t}\n}", "outputs": "2", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 130, "y": 180, "wires": [["9f5c5eec6d6f0967"], ["cd1afc4b7e355601"]]}, {"id": "cd1afc4b7e355601", "type": "delay", "z": "8056a2538439382a", "g": "7569f6e998040ed3", "name": "", "pauseType": "delay", "timeout": "1", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 140, "y": 220, "wires": [["a0cb7e0b136e2961"]]}, {"id": "c946436e199179ed", "type": "function", "z": "8056a2538439382a", "name": "Return Flow Data", "func": "msg.payload = 'start';  // Replace with actual sensor logic\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 350, "y": 60, "wires": [["a0cb7e0b136e2961", "c217a9b7f4883c7b"]]}, {"id": "c217a9b7f4883c7b", "type": "http response", "z": "8056a2538439382a", "name": "Airflow Server", "statusCode": "", "headers": {}, "x": 540, "y": 60, "wires": []}, {"id": "d6e348483c01dfd8", "type": "function", "z": "8056a2538439382a", "g": "59bd685206d338c7", "name": "Water Flow Sensor S3", "func": "\nif (msg.payload === true) {\n    // Generate a random float number between 1.0 and 10.0\n    msg.payload = Math.random() * (10.0 - 1.0) + 1.0;\n} else {\n    // msg.payload = 0;\n    return null;\n}\n\n// Set the topic value\nmsg.topic = \"/3300/0/5700\";\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 560, "y": 320, "wires": [["ac758b9854381cde"]]}, {"id": "48fbd7d3f7dc3caf", "type": "lwm2m client in", "z": "8056a2538439382a", "g": "b62ab9bad6e206f6", "name": "", "lwm2mClient": "0eaea711b6b3b72c", "subscribeObjectEvents": true, "outputAsObject": false, "x": 300, "y": 560, "wires": [["9b94c21009439961", "0d856283790d5710", "45526bc482f42924", "c9a655dadaa0a0b7", "378c6409ef91ecb3", "05e5c18b2759eaa4"]]}, {"id": "7f3a32c998a99d42", "type": "lwm2m client out", "z": "8056a2538439382a", "g": "b50014e557f437a7", "name": "Write or Execute LwM2M Resources", "lwm2mClient": "0eaea711b6b3b72c", "x": 1270, "y": 220, "wires": []}, {"id": "eee39e6bbd2928c9", "type": "lwm2m client out", "z": "8056a2538439382a", "g": "b50014e557f437a7", "name": "Write or Execute LwM2M Resources", "lwm2mClient": "0eaea711b6b3b72c", "x": 1270, "y": 320, "wires": []}, {"id": "a6603c66eac3878a", "type": "lwm2m client out", "z": "8056a2538439382a", "g": "b50014e557f437a7", "name": "Write or Execute LwM2M Resources", "lwm2mClient": "0eaea711b6b3b72c", "x": 1270, "y": 400, "wires": []}, {"id": "a2b1803f60e5fcf0", "type": "lwm2m client out", "z": "8056a2538439382a", "g": "b50014e557f437a7", "name": "Write or Execute LwM2M Resources", "lwm2mClient": "0eaea711b6b3b72c", "x": 1270, "y": 560, "wires": []}, {"id": "f7f137b219e42f01", "type": "lwm2m client out", "z": "8056a2538439382a", "g": "b50014e557f437a7", "name": "Write or Execute LwM2M Resources", "lwm2mClient": "0eaea711b6b3b72c", "x": 1270, "y": 860, "wires": []}, {"id": "c8cb51e56405b902", "type": "traffic", "z": "8056a2538439382a", "g": "613f63d79b782acc", "name": "Washing Machine Conveyor Motor ", "property_allow": "payload", "filter_allow": "GO", "ignore_case_allow": false, "negate_allow": false, "send_allow": false, "property_stop": "payload", "filter_stop": "stop", "ignore_case_stop": true, "negate_stop": false, "send_stop": true, "default_start": false, "differ": false, "x": 900, "y": 620, "wires": [[]]}, {"id": "d04bd1f69ec62e37", "type": "traffic", "z": "8056a2538439382a", "g": "613f63d79b782acc", "name": "Water Pump Washing Machine", "property_allow": "payload", "filter_allow": "GO", "ignore_case_allow": false, "negate_allow": false, "send_allow": false, "property_stop": "payload", "filter_stop": "stop", "ignore_case_stop": true, "negate_stop": false, "send_stop": true, "default_start": false, "differ": false, "x": 890, "y": 780, "wires": [[]]}, {"id": "05e5c18b2759eaa4", "type": "debug", "z": "8056a2538439382a", "name": "debug 12", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 560, "y": 520, "wires": []}, {"id": "887aee97409b296a", "type": "function", "z": "8056a2538439382a", "g": "7569f6e998040ed3", "name": "Reset Flow", "func": "msg.payload = 'reset';  // Replace with actual sensor logic\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 350, "y": 180, "wires": [["9f5c5eec6d6f0967"]]}, {"id": "9f5c5eec6d6f0967", "type": "function", "z": "8056a2538439382a", "g": "7569f6e998040ed3", "name": "Presence Sensor Process 2", "func": "// Reset timer when signal is received (from an HTTP node or similar)\nif (msg.payload === 'reset') {\n    // Set new startTime to current time\n    context.set('startTime', Date.now());\n    msg.payload = 'Timer reset and started again';\n    return msg;  // Return message immediately after reset\n}\n\n// If no reset signal, calculate elapsed time\nif (context.get('startTime') === undefined) {\n    // If startTime hasn't been set, initialize it\n    context.set('startTime', Date.now());\n}\n\nlet elapsed = (Date.now() - context.get('startTime')) / 1000;\nmsg.payload = false;\nif (elapsed > 60) {\n    msg.payload = true;\n\n} if (elapsed > 280) {\n    msg.payload = false;\n}\n\nmsg.topic = \"/3302/1/5500\"\n\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 380, "y": 220, "wires": [["c7016290914a0cba", "d6e348483c01dfd8"]]}, {"id": "9fa4cc6ed4967fe4", "type": "msg-size", "z": "8056a2538439382a", "name": "", "frequency": "sec", "interval": 1, "statusContent": "avg", "estimation": false, "ignore": false, "pauseAtStartup": false, "humanReadableStatus": true, "topicDependent": false, "x": 260, "y": 980, "wires": [["85f6d1363c5b443a"], ["23f5413f1cd4fbdd"]]}, {"id": "23f5413f1cd4fbdd", "type": "debug", "z": "8056a2538439382a", "name": "debug 13", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 430, "y": 1000, "wires": []}, {"id": "96a0028cd36d6f00", "type": "function", "z": "cda5c6113e87d3f0", "name": "Presence Sensor Process 2 (S3)", "func": "// Reset timer when signal is received (from an HTTP node or similar)\nif (msg.payload === 'reset') {\n    // Set new startTime to current time\n    context.set('startTime', Date.now());\n    msg.payload = 'Timer reset and started again';\n    return msg;  // Return message immediately after reset\n}\n\n// If no reset signal, calculate elapsed time\nif (context.get('startTime') === undefined) {\n    // If startTime hasn't been set, initialize it\n    context.set('startTime', Date.now());\n}\n\nlet elapsed = (Date.now() - context.get('startTime')) / 1000;\n\nif (elapsed < 200) {\n    msg.payload = true;\n} else {\n    msg.payload = false;\n}\n\nmsg.topic = \"/3302/0/5500\";\n\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 580, "y": 200, "wires": [["067a68b621c2adcb"]]}, {"id": "c3c28b0d48cb3816", "type": "lwm2m client out", "z": "cda5c6113e87d3f0", "name": "Write or Execute LwM2M Resources", "lwm2mClient": "3cb20fbce3341ff5", "x": 1035, "y": 300, "wires": [], "l": false}, {"id": "f309fe75c0b8b43f", "type": "comment", "z": "cda5c6113e87d3f0", "name": "/3306/0/5850: Actuator", "info": "", "x": 520, "y": 140, "wires": []}, {"id": "eac9105d0916ac28", "type": "comment", "z": "cda5c6113e87d3f0", "name": "/3300/0/5700: Weight Scale Value", "info": "Weight Scale Value", "x": 550, "y": 100, "wires": []}, {"id": "63a0779662e6d89c", "type": "lwm2m client in", "z": "cda5c6113e87d3f0", "name": "", "lwm2mClient": "3cb20fbce3341ff5", "subscribeObjectEvents": true, "outputAsObject": false, "x": 120, "y": 400, "wires": [["6e56614f51e4743a", "1d9963bf7e12665c"]]}, {"id": "6e56614f51e4743a", "type": "debug", "z": "cda5c6113e87d3f0", "name": "debug 2", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 280, "y": 400, "wires": []}, {"id": "30758c58372ddf9b", "type": "function", "z": "cda5c6113e87d3f0", "name": "Only send if the value is different from the previous one", "func": "// Get the previous value from context\nlet previousValue = context.get('previousValue');\n\n// Check if the current payload is different from the previous one\nif (msg.payload !== previousValue) {\n    // Save the current value as the new previous value\n    context.set('previousValue', msg.payload);\n    msg.topic = \"/3300/0/5700\"\n    \n    // Forward the message since the value is different\n    return msg;\n} else {\n    // Don't forward the message if the value is the same\n    return null;\n}\n\n\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 640, "y": 320, "wires": [["c3c28b0d48cb3816"]]}, {"id": "7f48ef3d32cfb1e5", "type": "function", "z": "cda5c6113e87d3f0", "name": "Weight Sensor S2", "func": "\n\n    // Generate a random float number between 1.0 and 10.0\n//msg.payload = Math.random() * (10.0 - 1.0) + 1.0;\nmsg.payload = 19\n    // msg.payload = 0;\n\n\n// Set the topic value\nmsg.topic = \"/3300/0/5700\";\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 530, "y": 280, "wires": [["30758c58372ddf9b"]]}, {"id": "b1b6fa48faa89450", "type": "function", "z": "cda5c6113e87d3f0", "name": "Only send if the value is different from the previous one", "func": "// Get the previous value from context\nlet previousValue = context.get('previousValue');\n\n// Check if the current payload is different from the previous one\nif (msg.payload !== previousValue) {\n    // Save the current value as the new previous value\n    context.set('previousValue', msg.payload);\n    msg.topic = \"/3306/0/5850\"\n    \n    // Forward the message since the value is different\n    return msg;\n} else {\n    // Don't forward the message if the value is the same\n    return null;\n}\n\n\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 640, "y": 400, "wires": [["c3c28b0d48cb3816"]]}, {"id": "944652e9a706375e", "type": "function", "z": "cda5c6113e87d3f0", "name": "ActuatorA1", "func": "\n\n    // Generate a random float number between 1.0 and 10.0\n//msg.payload = Math.random() * (10.0 - 1.0) + 1.0;\nmsg.payload = 17\n    // msg.payload = 0;\n\n\n// Set the topic value\nmsg.topic = \"/3306/0/5850\";\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 510, "y": 360, "wires": [["b1b6fa48faa89450"]]}, {"id": "c55ce40277a2bd5b", "type": "comment", "z": "cda5c6113e87d3f0", "name": "/3302/0/5500: Presence S3", "info": "", "x": 530, "y": 60, "wires": []}, {"id": "3d792d45ccbe289b", "type": "inject", "z": "cda5c6113e87d3f0", "name": "", "props": [{"p": "payload"}, {"p": "topic", "vt": "str"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "date", "x": 200, "y": 280, "wires": [["7f48ef3d32cfb1e5", "944652e9a706375e", "96a0028cd36d6f00"]]}, {"id": "c47a3b6922d02d67", "type": "debug", "z": "cda5c6113e87d3f0", "name": "debug 5", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 300, "y": 580, "wires": []}, {"id": "19fe05055e97b53c", "type": "http in", "z": "cda5c6113e87d3f0", "name": "Start Conveyor", "url": "/start/conver", "method": "get", "upload": false, "swaggerDoc": "", "x": 100, "y": 120, "wires": [["744c104dd640767a"]]}, {"id": "744c104dd640767a", "type": "http response", "z": "cda5c6113e87d3f0", "name": "Server", "statusCode": "", "headers": {}, "x": 270, "y": 120, "wires": []}, {"id": "be6429cb67a6f6cd", "type": "influxdb out", "z": "cda5c6113e87d3f0", "g": "e0d77326a49c99a4", "influxdb": "3fc00e2ac08b5bfd", "name": "Influxdb", "measurement": "wine1", "precision": "", "retentionPolicy": "", "database": "database", "precisionV18FluxV20": "ms", "retentionPolicyV18Flux": "", "org": "ICT", "bucket": "wine", "x": 260, "y": 520, "wires": []}, {"id": "1d9963bf7e12665c", "type": "function", "z": "cda5c6113e87d3f0", "g": "e0d77326a49c99a4", "name": "Send Data to DB", "func": "let payload = msg.payload;\nlet value1= \"default\";\nlet value = payload.value.value\nlet uri = payload.uri;\nswitch (uri) {\n    case \"/3302/0/5500\":\n        value1 = \"Conveyor-presence\";\n        break;\n    case \"/3300/0/5700\":\n        value1 = \"Conveyor-Weight\";\n        break;\n    case \"/3306/0/5850\":\n        value1 = \"Conver-Actuator\";\n        break;\n    case \"/3300/1/5700\":\n        value1 = \"Conveyor-P\";\n        break;\n    case \"/3303/0/5700\":\n        value1 = \"S-Temperature\";\n        break;\n    case \"/3302/0/5500\":\n        value1 = \"S-Presence-one\";\n        break;\n    case \"/3302/1/5500\":\n        value1 = \"S-Presence-two\";\n        break;\n    case \"/3302/2/5500\":\n        value1 = \"S-Presence-three\";\n        break;\n    case \"/3300/0/5700\":\n        value1 = \"S-water-flow\";\n        break;   \n    case \"/3300/1/5700\":\n        value1 = \"S-viscosity-p\";\n        break;    \n    default:\n       value1 = value1;  \n}\nswitch (value) {\n    case true:\n        value = 1;\n        break;\n    case false:\n        value = 0;\n        break;\n    //default:\n     //   value = value;\n}\nlet timestamp = payload.ts; \n//milliseconds to seconds (InfluxDB uses seconds by default)\ntimestamp = Math.floor(timestamp / 1000);\nmsg.payload = {\n    [value1]: value,  \n    uri: uri,         \n    timestamp: timestamp \n};\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 270, "y": 480, "wires": [["be6429cb67a6f6cd", "c47a3b6922d02d67"]]}, {"id": "067a68b621c2adcb", "type": "function", "z": "cda5c6113e87d3f0", "name": "Only send if the value is different from the previous one", "func": "// Get the previous value from context\nlet previousValue = context.get('previousValue');\n\n// Check if the current payload is different from the previous one\nif (msg.payload !== previousValue) {\n    // Save the current value as the new previous value\n    context.set('previousValue', msg.payload);\n    msg.topic = \"/3302/0/5500\"\n    \n    // Forward the message since the value is different\n    return msg;\n} else {\n    // Don't forward the message if the value is the same\n    return null;\n}\n\n\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 640, "y": 240, "wires": [["c3c28b0d48cb3816"]]}]