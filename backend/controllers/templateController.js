const fs = require('fs');
const path = require('path');
const dockerService = require('../services/dockerService');

// Path to templates directory
const templatesDir = path.join(__dirname, '../templates');

// Get all templates
exports.getAllTemplates = (req, res) => {
  try {
    // Read all files in the templates directory
    const files = fs.readdirSync(templatesDir);

    // Filter for JSON files and create template objects
    const templates = files
      .filter(file => file.endsWith('.json'))
      .map(file => {
        const filePath = path.join(templatesDir, file);
        const stats = fs.statSync(filePath);

        return {
          name: path.basename(file, '.json'),
          size: stats.size,
          lastModified: stats.mtime
        };
      });

    res.json(templates);
  } catch (error) {
    console.error('Error getting templates:', error);
    res.status(500).json({ message: 'Failed to get templates', error: error.message });
  }
};

// Upload a new template
exports.uploadTemplate = (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    // The file should be available at req.file.path (temporary location)
    const tempPath = req.file.path;
    const targetPath = path.join(templatesDir, req.file.originalname);

    // Validate that the file is a valid JSON
    try {
      const fileContent = fs.readFileSync(tempPath, 'utf8');
      JSON.parse(fileContent); // This will throw if not valid JSON
    } catch (error) {
      return res.status(400).json({ message: 'Invalid JSON file' });
    }

    // Move the file to the templates directory
    fs.copyFileSync(tempPath, targetPath);
    fs.unlinkSync(tempPath); // Delete the temp file

    res.status(201).json({
      message: 'Template uploaded successfully',
      template: {
        name: path.basename(req.file.originalname, '.json'),
        size: req.file.size,
        lastModified: new Date()
      }
    });
  } catch (error) {
    console.error('Error uploading template:', error);
    res.status(500).json({ message: 'Failed to upload template', error: error.message });
  }
};

// Get template content
exports.getTemplateContent = (req, res) => {
  try {
    const templateName = req.params.name;
    const templatePath = path.join(templatesDir, `${templateName}.json`);

    // Check if the template exists
    if (!fs.existsSync(templatePath)) {
      return res.status(404).json({ message: 'Template not found' });
    }

    // Read the template file
    const templateContent = fs.readFileSync(templatePath, 'utf8');

    try {
      // Parse the template content to JSON
      const templateObj = JSON.parse(templateContent);
      res.json(templateObj);
    } catch (error) {
      res.status(400).json({ message: 'Invalid JSON template', error: error.message });
    }
  } catch (error) {
    console.error('Error getting template content:', error);
    res.status(500).json({ message: 'Failed to get template content', error: error.message });
  }
};

// Delete a template
exports.deleteTemplate = (req, res) => {
  try {
    const templateName = req.params.name;
    const templatePath = path.join(templatesDir, `${templateName}.json`);

    // Check if the template exists
    if (!fs.existsSync(templatePath)) {
      return res.status(404).json({ message: 'Template not found' });
    }

    // Delete the template file
    fs.unlinkSync(templatePath);

    res.json({ message: 'Template deleted successfully' });
  } catch (error) {
    console.error('Error deleting template:', error);
    res.status(500).json({ message: 'Failed to delete template', error: error.message });
  }
};

// Deploy a template
exports.deployTemplate = async (req, res) => {
  try {
    console.log('Received deploy request with body:', req.body);

    const { templateName, containerName, leshanIpAddress } = req.body;

    console.log('Extracted parameters:', { templateName, containerName, leshanIpAddress });

    if (!templateName) {
      return res.status(400).json({ message: 'Template name is required' });
    }

    if (!containerName) {
      return res.status(400).json({ message: 'Container name is required' });
    }

    // Validate container name - Docker container names must be valid DNS names
    if (!/^[a-zA-Z0-9][a-zA-Z0-9_.-]*$/.test(containerName)) {
      return res.status(400).json({
        message: 'Invalid container name. Container names must contain only letters, numbers, underscores, periods, or hyphens, and must start with a letter or number.'
      });
    }

    if (!leshanIpAddress) {
      return res.status(400).json({ message: 'Leshan IP address is required' });
    }

    console.log(`Deploying template '${templateName}' as container '${containerName}' with Leshan IP '${leshanIpAddress}'`);

    const templatePath = path.join(templatesDir, `${templateName}.json`);

    // Check if the template exists
    if (!fs.existsSync(templatePath)) {
      return res.status(404).json({ message: 'Template not found' });
    }

    // Create a unique directory for this container
    const flowsDir = path.join(__dirname, "../flows_generated");
    const containerDir = path.join(flowsDir, `container-${containerName}-${Date.now()}`);
    if (!fs.existsSync(containerDir)) {
      fs.mkdirSync(containerDir, { recursive: true });
    }

    // Check if a modified template was provided in the request
    let templateObj;
    if (req.body.modifiedTemplate) {
      console.log('Using modified template provided in the request');
      templateObj = req.body.modifiedTemplate;
    } else {
      // Read the template file
      const templateContent = fs.readFileSync(templatePath, 'utf8');
      templateObj = JSON.parse(templateContent);
    }

    console.log('Template object:', JSON.stringify(templateObj).substring(0, 200) + '...');

    // Function to recursively search and replace values in the template
    const processObject = (obj) => {
      if (!obj || typeof obj !== 'object') return;

      // Process arrays
      if (Array.isArray(obj)) {
        obj.forEach(item => processObject(item));
        return;
      }

      // Process object properties
      for (const key in obj) {
        // LwM2M client configuration
        if (key === 'clientName' && typeof obj[key] === 'string') {
          console.log(`Replacing clientName from '${obj[key]}' to '${containerName}'`);
          obj[key] = containerName;
        }

        if (key === 'serverHost' && typeof obj[key] === 'string') {
          console.log(`Replacing serverHost from '${obj[key]}' to '${leshanIpAddress}'`);
          obj[key] = leshanIpAddress;
        }

        // MQTT broker configuration
        if (key === 'broker' && typeof obj[key] === 'string' && obj[key] === 'mqttBrokerConfig') {
          console.log(`Found MQTT broker reference: ${obj[key]}`);
        }

        // Handle MQTT topic
        if (key === 'topic' && typeof obj[key] === 'string') {
          console.log(`Found MQTT topic: ${obj[key]}`);
          // We'll keep the original topic unless specified in the request
          if (req.body.mqttTopic) {
            console.log(`Replacing MQTT topic from '${obj[key]}' to '${req.body.mqttTopic}'`);
            obj[key] = req.body.mqttTopic;
          }
        }

        // Handle MQTT broker configuration
        if (key === 'broker' && obj.type === 'mqtt-broker') {
          console.log(`Found MQTT broker configuration`);

          // Update broker IP if provided
          if (req.body.mqttBrokerIp) {
            console.log(`Replacing MQTT broker IP from '${obj.broker}' to '${req.body.mqttBrokerIp}'`);
            obj.broker = req.body.mqttBrokerIp;
          }

          // Update broker port if provided
          if (req.body.mqttBrokerPort) {
            console.log(`Replacing MQTT broker port from '${obj.port}' to '${req.body.mqttBrokerPort}'`);
            obj.port = parseInt(req.body.mqttBrokerPort, 10);
          }

          // Update broker credentials if provided
          if (req.body.mqttUsername) {
            console.log(`Setting MQTT username to '${req.body.mqttUsername}'`);
            obj.username = req.body.mqttUsername;
          }

          if (req.body.mqttPassword) {
            console.log(`Setting MQTT password (hidden for security)`);
            obj.password = req.body.mqttPassword;
          }
        }

        // Also check for these values in nested objects
        if (typeof obj[key] === 'object') {
          processObject(obj[key]);
        }
      }
    };

    // Process the template object if it wasn't already modified
    if (!req.body.modifiedTemplate) {
      processObject(templateObj);
    }

    // Convert back to JSON string
    const modifiedTemplateContent = JSON.stringify(templateObj, null, 2);
    console.log('Modified template content (first 200 chars):', modifiedTemplateContent.substring(0, 200) + '...');

    // Write the modified flow file in the container's directory
    const flowPath = path.join(containerDir, 'flows.json');
    fs.writeFileSync(flowPath, modifiedTemplateContent, 'utf8');

    // Also write a template file for the entrypoint.sh script
    const flowTemplatePath = path.join(containerDir, 'flows.template.json');
    fs.writeFileSync(flowTemplatePath, modifiedTemplateContent, 'utf8');

    console.log(`Template files written to ${containerDir} with containerName=${containerName} and serverHost=${leshanIpAddress}`);

    // Extract MQTT configuration from request body
    const mqttConfig = {
      mqttBrokerIp: req.body.mqttBrokerIp,
      mqttBrokerPort: req.body.mqttBrokerPort,
      mqttTopic: req.body.mqttTopic,
      mqttUsername: req.body.mqttUsername,
      mqttPassword: req.body.mqttPassword
    };

    console.log('MQTT Configuration:', {
      mqttBrokerIp: mqttConfig.mqttBrokerIp,
      mqttBrokerPort: mqttConfig.mqttBrokerPort,
      mqttTopic: mqttConfig.mqttTopic,
      mqttUsername: mqttConfig.mqttUsername,
      mqttPassword: mqttConfig.mqttPassword ? '******' : undefined
    });

    // Deploy the container with the custom container name and MQTT configuration
    const containerData = await dockerService.createAndStartContainer(
      containerName,
      flowPath,
      {},
      leshanIpAddress,
      mqttConfig
    );

    res.json({
      message: `Template deployed successfully as container '${containerName}' with Leshan IP '${leshanIpAddress}'`,
      container: {
        ...containerData,
        containerName,
        leshanIpAddress,
        mqttConfig: {
          mqttBrokerIp: mqttConfig.mqttBrokerIp,
          mqttBrokerPort: mqttConfig.mqttBrokerPort,
          mqttTopic: mqttConfig.mqttTopic,
          mqttUsername: mqttConfig.mqttUsername,
          mqttPassword: mqttConfig.mqttPassword ? '******' : undefined
        }
      }
    });
  } catch (error) {
    console.error('Error deploying template:', error);

    // Provide more specific error messages based on the error type
    let statusCode = 500;
    let errorMessage = 'Failed to deploy template';

    if (error.message && error.message.includes('already in use')) {
      statusCode = 409; // Conflict
      errorMessage = `Container name conflict: The name "${templateName}" is already in use`;
    } else if (error.code === 'ENOENT') {
      statusCode = 404; // Not Found
      errorMessage = 'Template file or directory not found';
    } else if (error.code === 'EACCES') {
      statusCode = 403; // Forbidden
      errorMessage = 'Permission denied when accessing template file or Docker socket';
    } else if (error.code === 'ECONNREFUSED') {
      statusCode = 503; // Service Unavailable
      errorMessage = 'Could not connect to Docker daemon. Is Docker running?';
    }

    res.status(statusCode).json({
      message: errorMessage,
      error: error.message,
      code: error.code || 'UNKNOWN'
    });
  }
};
